{"title": "OurTrip | My Bookings", "description": "Manage your stays with ease. Access your bookings at various hotels on OurTrip. Organize your trips conveniently, all in one place.", "reservations": {"header": "My Bookings", "updateButton": "Update", "emptyState": {"title": "No bookings made!", "description": "Click the button below and make a booking to see it here", "actionButton": "Go to Home"}, "toastError": "Error loading bookings!"}, "pagination": {"previous": "Previous", "next": "Next"}, "buttons": {"refresh": "Refresh", "goHome": "Go to Home"}, "orderCard": {"status": {"canceled": "Canceled", "pendingCancellation": "Cancellation Pending", "confirmed": "Order Confirmed", "pendingPayment": "Processing Payment", "pendingAntifraud": "Processing Payment", "paymentError": "Payment Not Approved", "paymentExpired": "Payment Expired", "paymentRejected": "Payment Rejected", "pendingConfirmation": "Processing Booking", "bookingError": "Offer Unavailable"}, "receiptModal": {"title": "Download Receipt", "description": "The receipt must be issued in the name of:", "financeResponsable": "Finance Responsible", "guest": "Guest", "cancel": "Cancel", "download": "Download"}, "buttons": {"downloadVoucher": "Download Voucher", "downloadReceipt": "Download Receipt", "moreOptions": "More Options", "requestCancellation": "Request Cancellation", "askForHelp": "Ask for Help"}, "modal": {"title": "Cancellation", "confirmText": "Are you sure you want to request the cancellation of this order?", "cancelRequestSuccess": "Cancellation request successfully made!", "cancelRequestError": "Error requesting cancellation!", "cancelButton": "Cancel", "confirmButton": "Confirm", "notNow": "Not Now", "yesCancel": "Yes, <PERSON>cel"}, "order": "Order", "labels": {"totalValue": "Total Value"}}, "reservationCard": {"hotelReservation": "Hotel Reservation", "checkinDay": "Check-in Day", "checkoutDay": "Check-out Day", "reservationCode": "Reservation Code", "rooms": "Rooms", "clickToSeeMore": "Click to see more", "clickToSeeLess": "Click to see less", "reservationCardRoom": {"room": "Room", "refundable": "Refundable", "nonRefundable": "Non-refundable", "freeCancellationUntil": "Free cancellation until", "mealPlan": "Meal Plan", "guests": "Guests", "yearsOld": "{{age}} years old"}}, "orderCardPayment": {"external": {"title": "Payment", "description": "{{amount}} on the credit card", "priceDescription": "Total Value"}, "pix": {"title": "Payment via Pix", "description": "{{amount}} in cash", "priceDescription": "Total Value"}, "creditCard": {"title": "Credit Card", "description": "{{installments}}x of {{amount}}"}, "tripcash": {"title": "Tripcash", "description": "Payment made with Tripcash"}}, "pixPaymentCard": {"title": "Payment via Pix", "description": "To make the payment, copy the pix code or scan the QR Code with your phone.", "expired": "Pix Expired!", "timeRemaining": "Time remaining: {{time}}", "copyQRCode": "Copy QR Code", "copied": "QR Code copied!"}, "externalPaymentCard": {"title": "Payment", "description": "To make the payment, click the following button and follow the instructions on the form.", "buttonText": "Make Payment", "timeRemaining": "Time remaining: {{time}}", "expired": "Payment Expired!"}}