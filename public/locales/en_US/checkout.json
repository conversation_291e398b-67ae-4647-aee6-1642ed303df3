{"title": "OurTrip | Booking", "description": "Complete your booking at OurTrip with ease. Choose the payment method, configure the rooms, and ensure an unforgettable stay. Enjoy simple, secure processes and dive into the excitement of planning your next journey.", "steper": {"stepOf": "{{current}} of {{total}}", "next": "Next: {{title}}", "account": "Account", "payment": "Payment Method", "guests": "Guests", "resume": "Book", "finish": "Finish"}, "steps": {"account": "Email", "payment": "Payment", "guests": "Guests", "resume": "Summary"}, "account": {"title": "Enter your email", "subtitle": "We need your email to send the voucher after the booking is completed!", "input": "Email", "login": {"description": "It looks like you already have an account. Log in to continue your purchase!", "changeEmail": "Change email"}}, "payment": {"step": {"pix": "Payment via Pix", "creditCard": "Payment via Card", "external": "Payment Details"}, "method": {"title": "Payment method", "pix": "Pix", "external": "Payment Details", "creditCard": "Credit Card", "installments": "up to 12x", "installment": "Installment"}, "card": {"title": "Card", "number": "Number", "name": "Name on card", "cvv": "CVV", "expiry": "Expiry", "installments": "Installments"}}, "payer": {"title": "Payer", "useMyInfo": "Use my user information", "warning": {"pix": "You have 1 hour to complete the Pix payment!"}, "personalInfo": {"title": "Personal information", "email": "Email", "firstName": "First Name", "lastName": "Last Name", "fullName": "Full Name", "document": {"type": "Document", "number": "Document number"}, "phone": {"areaCode": "Area Code", "ddi": "DDI", "number": "Phone"}}, "address": {"title": "Address", "postalCode": "Postal Code", "state": "State", "city": "City", "country": "Country", "street": "Street", "number": "Number", "neighborhood": "Neighborhood", "complement": "Complement"}}, "guests": {"data": "Data", "birth": "Date of Birth", "adultIndex": "Adult {{index}}"}, "room": {"title": "Rooms", "change": "Change", "index": "Room {{index}}", "freeCancelation": "Free cancellation until {{date}}"}, "tripcash": {"title": "Tripcash", "use": "Use Tripcash", "discount": "Discount"}, "buttons": {"back": "Back", "next": "Next", "book": "Book"}, "terms": {"accept": "I have read and accept the", "termsAndConditions": "Terms and Conditions", "and": "and", "privacyPolicy": "Privacy Policy", "roomTerms": "I have read and accept the terms of the stay:"}, "validation": {"required": "Fill in the field!", "invalid": {"email": "Invalid email", "name": "Invalid name", "surname": "Invalid surname", "cpf": "Invalid CPF", "cnpj": "Invalid CNPJ", "ddi": "Invalid DDI", "areaCode": "Invalid area code", "phone": "Invalid phone number", "cvv": "Invalid CVV", "date": "Invalid date"}}, "errors": {"tripcash": "Failed to update Tripcash!", "default": "An error occurred while completing your order", "cep": "No address found for this postal code"}, "expired": {"title": "Purchase expired!", "description": "Search again and start a new purchase to proceed.", "button": "Back to Home"}, "exitConfirmation": {"title": "Are you sure you want to exit?", "description": "If you exit, you will lose all the information filled in so far.", "cancel": "Cancel", "exit": "Exit"}, "details": "Details", "timeRemaining": "Time remaining", "loading": "Processing"}