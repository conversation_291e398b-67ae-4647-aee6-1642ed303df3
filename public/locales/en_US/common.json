{"navbar": {"home": "Home", "tripcash": {"title": "Tripcash", "balance": "Tripcash Balance"}, "language": {"portuguese": "Portuguese", "portuguese_brazil": "Portuguese (Brazil)", "english": "English", "spanish": "Spanish"}, "signIn": "Sign In", "signUp": "Sign Up", "myProfile": "My Profile", "myReservations": "My Bookings", "myTripcash": "My Tripcash", "logout": "Log Out"}, "footer": {"company": {"title": "Company", "contact": "Contact", "tripcash": "Tripcash"}, "support": {"title": "Support", "security": "Security", "privacy": "Privacy Policy", "terms": "Terms and Conditions"}, "paymentMethods": {"title": "Payment Methods", "12x": "Up to 12 installments", "transfers": "Transfers"}, "seals": {"title": "Seals and Verifications"}, "rights": "© {{year}} OurTrip All rights reserved."}, "search": {"inputPlaceholder": "Destination, Date, Guests", "destination": {"label": "Destination", "placeholder": "Where do you want to go?", "min": "Searching for cities requires at least 3 letters!", "loading": "Loading...", "notfound": "No results found!"}, "dates": {"label": "Check-in | Check-out", "placeholder": "Select a period"}, "rooms": {"label": "Rooms", "placeholder": "1 room, 2 adults", "room": "Room", "remove": "Remove", "adults": "Adults", "kids": "Kids", "kidsAge_zero": "Less than 1 year", "kidsAge_one": "1 year", "kidsAge_other": "{{count}} years", "addRoom": "Add room", "apply": "Apply", "roomsAdultsAndKids": "$t(search.rooms.roomsCount, {'count': {{roomsCount}} })$t(search.rooms.adultsCount, {'count': {{adultsCount}} })$t(search.rooms.kidsCount, {'count': {{kidsCount}} })", "roomsCount_one": "{{count}} room", "roomsCount_other": "{{count}} rooms", "adultsCount_one": ", {{count}} adult", "adultsCount_other": ", {{count}} adults", "kidsCount_zero": "", "kidsCount_one": " and {{count}} child", "kidsCount_other": " and {{count}} children", "adultsAndKids": "$t(search.rooms.adultsCount2, {'count': {{adultsCount}} })$t(search.rooms.kidsCount, {'count': {{kidsCount}} })", "adultsCount2_one": "{{count}} adult", "adultsCount2_other": "{{count}} adults"}, "filters": {"expand": "See more", "collapse": "See less", "collapseAll": "Hide all"}, "search": "Search"}, "cookies": {"title": "We use cookies to improve your experience", "description": "To provide the best possible experience on our website, we use cookies. They help us remember your preferences, personalize content, and analyze how you use our site. With this information, we can ensure your navigation is more efficient and that you have access to the most relevant content.", "myPreferences": "My preferences", "accept": "Accept cookies", "preferences": {"title": "Preferences", "necessaryCookies": {"title": "Necessary Cookies", "description": "Necessary cookies are essential for the basic functioning of the site. Without them, some essential functions, such as accessing secure areas, would not be possible."}, "analyticsCookies": {"title": "Analytics Cookies", "description": "We use analytics cookies to collect data about how visitors interact with our website. This information helps us understand the site's performance and improve the experience for everyone."}, "advertisingCookies": {"title": "Advertising Cookies", "description": "These cookies are used to display relevant ads based on your interests. They help measure the effectiveness of advertising campaigns and show ads that are more relevant to you."}, "functionalCookies": {"title": "Functional Cookies", "description": "These cookies are essential for the site to function properly and offer a personalized experience. They allow, for example, remembering your preferences and settings during your navigation."}, "whatIsCookies": {"title": "What are Cookies?", "description": "Cookies are small text files stored on your device when you visit a website. They are used to remember your choices and improve the site's functionality."}, "save": "Save", "acceptAll": "Accept all"}}, "cashbackCard": {"title": "Tripcash", "description": {"withValue": "You will receive up to <span>{{value}}</span> by making this purchase!", "withoutValue": "Select a room to see your cashback!"}}, "purchaseDetails": {"resume": {"button": "Book"}, "price": {"tripcash": "You earn {{value}} of Tripcash!"}, "details": "Details"}, "validations": {"required": "Required field", "invalidPhoneAreaCode": "Invalid area code", "invalidPhoneNumber": "Invalid phone number"}, "fieldsLabels": {"ddi": "DDI", "areaCode": "Area Code", "number": "Number"}, "currencies": {"BRL": "Brazilian Real", "USD": "US Dollar", "EUR": "Euro", "CLP": "Chilean Peso", "ARS": "Argentine Peso"}, "routes": {"signIn": "/sign-in", "signUp": "/sign-up", "passwordChange": "/change-password", "passwordRecovery": "/recover-password", "verifyAccount": "/verify-account", "contact": "/contact", "tripcash": "/tripcash", "security": "/security", "termsAndConditions": "/terms-and-conditions", "privacyPolicy": "/privacy-policy", "search": "/search", "hotel": "/hotel", "checkout": "/checkout", "purchase": "/purchase", "myProfile": "/my-profile", "myReservations": "/my-bookings", "myTripcash": "/my-tripcash", "receipt": "/receipt"}}