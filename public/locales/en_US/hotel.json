{"title": "OurTrip | {{hotelName}}", "description": "Explore the charms of {{hotelName}} with OurTrip. Discover exclusive amenities, luxurious rooms, and a unique lodging experience. Plan your stay now and immerse yourself in the excellence of {{hotelName}}.", "amenities": {"title": "Amenities", "expandLess": "View less", "expandMore": "View more"}, "descriptionSection": {"title": "Description", "expandLess": "View less", "expandMore": "View more"}, "filters": {"hotelName": "Hotel Name", "apply": "Apply"}, "search": {"button": "Search", "dateLabel": "Check-in | Check-out", "datePlaceholder": "Select a period"}, "availableRooms": {"title": "Available Rooms", "content": {"refundable": "Refundable", "nonRefundable": "Non-refundable", "freeCancellation": "Free cancellation until {{date}}", "mealPlan": "{{plan}}", "perNight": "Per night", "pixDiscount": "Pix discount", "roomOption": "Option of", "room": "Room {{number}}", "select": "Select", "selected": "Selected"}}, "purchaseDetails": {"resume": {"button": "Book"}, "price": {"tripcash": "You earn {{value}} in Tripcash!"}, "details": "Details"}, "purchaseResume": {"title": "Summary", "checkin": "Check-in", "checkout": "Check-out", "refundable": "Refundable", "nonRefundable": "Non-refundable", "mealPlan": "{{plan}}", "aboutRoom": "Important information", "roomsAndDaily": "$t(purchaseResume.roomsCount, {'count': {{roomsCount}} }) and $t(purchaseResume.dailyCount, {'count': {{dailyCount}} })", "roomsCount_one": "{{count}} room", "roomsCount_other": "{{count}} rooms", "dailyCount_one": "{{count}} night", "dailyCount_other": "{{count}} nights", "taxAndFees": "Taxes and fees", "payment": "Payment", "paymentMethod": "Credit Card", "paymentMethodBRL": "Credit Card (up to 12x)"}, "alerts": {"kidsWarning": {"title": "Attention! Please note that children accommodated under special conditions and/or complimentary do not have a guaranteed bed during the stay.", "content": "Hotels usually grant this type of benefit when children are accommodated together with their parents. If you wish to guarantee beds for all members of the reservation, look for the nomenclature that matches your reservation profile. Ex: 2 adults and 1 child = Triple Apartment, 2 adults and two children = Quadruple Apartment.", "toggleOpen": "Learn more", "toggleClose": "View less"}}, "emptyState": {"title": "No available rooms found!", "description": "Try changing the search period to find available rooms."}, "loading": {"message": "Please wait...", "description": "We are confirming the availability of the rooms!"}, "breadcrumbs": {"home": "Home"}, "errors": {"checkout": "An error occurred while completing your order.", "search": "An error occurred while searching for available rooms."}}