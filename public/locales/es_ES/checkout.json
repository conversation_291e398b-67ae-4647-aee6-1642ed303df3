{"title": "OurTrip | Reserva", "description": "Finaliza tu reserva en OurTrip con facilidad. Elige el método de pago, configura las habitaciones y asegura una estadía inolvidable. Disfruta de procesos simples, seguros y sumérgete en la emoción de planear tu próximo viaje.", "steper": {"stepOf": "{{current}} de {{total}}", "next": "Siguiente: {{title}}", "account": "C<PERSON><PERSON>", "payment": "Forma de Pago", "guests": "Huéspedes", "resume": "<PERSON><PERSON><PERSON>", "finish": "Finalización"}, "steps": {"account": "Email", "payment": "Pago", "guests": "Huéspedes", "resume": "Resumen"}, "account": {"title": "Ingresa tu email", "subtitle": "¡Necesitamos tu email para enviarte el voucher después de realizar la reserva!", "input": "Email", "login": {"description": "Parece que ya tienes una cuenta. ¡Inicia sesión para continuar tu compra!", "changeEmail": "Cambiar email"}}, "payment": {"step": {"pix": "Pago por Pix", "creditCard": "Pago con Tarjeta", "external": "Detalles de pago"}, "method": {"title": "Forma de pago", "pix": "Pix", "external": "Detalles de pago", "creditCard": "Tarjeta de Crédito", "installments": "hasta 12 cuotas", "installment": "<PERSON><PERSON><PERSON>"}, "card": {"title": "Tarjeta", "number": "Número", "name": "Nombre en la tarjeta", "cvv": "CVV", "expiry": "Vencimiento", "installments": "<PERSON><PERSON><PERSON>"}}, "payer": {"title": "Pa<PERSON>r", "useMyInfo": "Usar mi información de usuario", "warning": {"pix": "¡Tienes 1 hora para realizar el pago por Pix!"}, "personalInfo": {"title": "Información personal", "email": "Email", "firstName": "Nombre", "lastName": "Apellido", "fullName": "Nombre completo", "document": {"type": "Documento", "number": "Número de documento"}, "phone": {"areaCode": "Código <PERSON>", "ddi": "DDI", "number": "Teléfono"}}, "address": {"title": "Dirección", "postalCode": "Código Postal", "state": "Estado", "city": "Ciudad", "country": "<PERSON><PERSON>", "street": "Calle", "number": "Número", "neighborhood": "Barrio", "complement": "Complemento"}}, "guests": {"data": "Datos", "birth": "<PERSON><PERSON> de Nacimiento", "adultIndex": "Adulto {{index}}"}, "room": {"title": "Habitaciones", "change": "Cambiar", "index": "Habitación {{index}}", "freeCancelation": "Cancelación gratuita hasta {{date}}"}, "tripcash": {"title": "Tripcash", "use": "<PERSON><PERSON><PERSON><PERSON>", "discount": "Descuento"}, "buttons": {"back": "Atrás", "next": "Siguient<PERSON>", "book": "<PERSON><PERSON><PERSON>"}, "terms": {"accept": "He leído y acepto los", "termsAndConditions": "Términos y Condiciones", "and": "y", "privacyPolicy": "Política de Privacidad", "roomTerms": "He leído y acepto los términos de la estadía:"}, "validation": {"required": "¡Complete el campo!", "invalid": {"email": "<PERSON><PERSON>", "name": "Nombre inválido", "surname": "<PERSON><PERSON>lid<PERSON>", "cpf": "CPF inválido", "cnpj": "CNPJ inválido", "ddi": "DDI inválido", "areaCode": "Código de área inválido", "phone": "Número de teléfono inválido", "cvv": "CVV inválido", "date": "<PERSON><PERSON>"}}, "errors": {"tripcash": "¡Error al actualizar Tripcash!", "default": "Ocurrió un error al finalizar tu pedido", "cep": "No se encontró ninguna dirección para este código postal"}, "expired": {"title": "¡Compra expirada!", "description": "Realiza una nueva búsqueda e inicia una nueva compra para continuar.", "button": "Volver al Inicio"}, "exitConfirmation": {"title": "¿Estás seguro de que deseas salir?", "description": "Si sales, perderás toda la información ingresada hasta el momento.", "cancel": "<PERSON><PERSON><PERSON>", "exit": "Salir"}, "details": "Detalles", "timeRemaining": "Tiempo restante", "loading": "Procesando"}