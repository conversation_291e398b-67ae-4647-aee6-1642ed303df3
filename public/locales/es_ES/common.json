{"navbar": {"home": "<PERSON><PERSON>o", "tripcash": {"title": "Tripcash", "balance": "<PERSON><PERSON>"}, "language": {"portuguese": "Portugués", "portuguese_brazil": "<PERSON>ug<PERSON><PERSON> (Brasil)", "english": "Inglés", "spanish": "Español"}, "signIn": "In<PERSON><PERSON>", "signUp": "Regístrate", "myProfile": "Mi perfil", "myReservations": "Mis reservas", "myTripcash": "Mi <PERSON>", "logout": "<PERSON><PERSON><PERSON>"}, "footer": {"company": {"title": "Empresa", "contact": "Contacto", "tripcash": "Tripcash"}, "support": {"title": "Soporte", "security": "Seguridad", "privacy": "Política de Privacidad", "terms": "Términos y Condiciones"}, "paymentMethods": {"title": "Métodos de Pago", "12x": "Hasta 12 cuotas", "transfers": "Transferencias"}, "seals": {"title": "Sellos y Verificaciones"}, "rights": "© {{year}} OurTrip Todos los derechos reservados."}, "search": {"inputPlaceholder": "<PERSON><PERSON>, <PERSON><PERSON>, Hués<PERSON>es", "destination": {"label": "<PERSON><PERSON>", "placeholder": "¿A dónde quieres ir?", "min": "¡La búsqueda de ciudades requiere al menos 3 letras!", "loading": "Cargando...", "notfound": "¡No se encontraron resultados!"}, "dates": {"label": "Check-in | Check-out", "placeholder": "Seleccione un período"}, "rooms": {"label": "Habitaciones", "placeholder": "1 habitación, 2 adultos", "room": "Habitación", "remove": "Eliminar", "adults": "Adultos", "kids": "<PERSON><PERSON><PERSON>", "kidsAge_zero": "Menos de 1 año", "kidsAge_one": "1 año", "kidsAge_other": "{{count}} años", "addRoom": "Añadir habitac<PERSON>", "apply": "Aplicar", "roomsAdultsAndKids": "$t(search.rooms.roomsCount, {'count': {{roomsCount}} })$t(search.rooms.adultsCount, {'count': {{adultsCount}} })$t(search.rooms.kidsCount, {'count': {{kidsCount}} })", "roomsCount_one": "{{count}} habitación", "roomsCount_other": "{{count}} habitaciones", "adultsCount_one": ", {{count}} adulto", "adultsCount_other": ", {{count}} adultos", "kidsCount_zero": "", "kidsCount_one": " y {{count}} niño", "kidsCount_other": " y {{count}} niños", "adultsAndKids": "$t(search.rooms.adultsCount2, {'count': {{adultsCount}} })$t(search.rooms.kidsCount, {'count': {{kidsCount}} })", "adultsCount2_one": "{{count}} adulto", "adultsCount2_other": "{{count}} adultos"}, "filters": {"expand": "<PERSON>er más", "collapse": "<PERSON>er menos", "collapseAll": "<PERSON><PERSON><PERSON><PERSON> todos"}, "search": "Buscar"}, "cookies": {"title": "Utilizamos cookies para mejorar su experiencia", "description": "Para ofrecer la mejor experiencia posible en nuestro sitio web, utilizamos cookies. Nos ayudan a recordar sus preferencias, personalizar el contenido y analizar cómo utiliza nuestro sitio. Con esta información, podemos garantizar que su navegación sea más eficiente y que tenga acceso al contenido más relevante.", "myPreferences": "Mis preferencias", "accept": "Aceptar cookies", "preferences": {"title": "Preferencias", "necessaryCookies": {"title": "Cookies Necesarias", "description": "Las cookies necesarias son fundamentales para el funcionamiento básico del sitio. <PERSON> ellas, algunas funciones esenciales, como el acceso a áreas seguras, no serían posibles."}, "analyticsCookies": {"title": "Cookies Analíticas", "description": "Utilizamos cookies analíticas para recopilar datos sobre cómo los visitantes interactúan con nuestro sitio web. Esta información nos ayuda a entender el rendimiento del sitio y mejorar la experiencia para todos."}, "advertisingCookies": {"title": "Cookies de Publicidad", "description": "Estas cookies se utilizan para mostrar anuncios relevantes basados en sus intereses. Ayudan a medir la eficacia de las campañas publicitarias y a mostrar anuncios que sean más relevantes para usted."}, "functionalCookies": {"title": "Cookies Funcionales", "description": "Estas cookies son esenciales para que el sitio funcione correctamente y ofrezca una experiencia personalizada. <PERSON><PERSON>n, por ejemplo, recordar sus preferencias y configuraciones durante su navegación."}, "whatIsCookies": {"title": "¿Qué son las Cookies?", "description": "Las cookies son pequeños archivos de texto almacenados en su dispositivo cuando visita un sitio web. Se utilizan para recordar sus elecciones y mejorar la funcionalidad del sitio."}, "save": "Guardar", "acceptAll": "<PERSON><PERSON><PERSON> to<PERSON>"}}, "cashbackCard": {"title": "Tripcash", "description": {"withValue": "¡Recibirás hasta <span>{{value}}</span> realizando esta compra!", "withoutValue": "¡Seleccione una habitación para ver su cashback!"}}, "purchaseDetails": {"resume": {"button": "<PERSON><PERSON><PERSON>"}, "price": {"tripcash": "¡Ganas {{value}} de Tripcash!"}, "details": "Detalles"}, "validations": {"required": "Campo obligatorio", "invalidPhoneAreaCode": "Código de área inválido", "invalidPhoneNumber": "Número de teléfono inválido"}, "fieldsLabels": {"ddi": "DDI", "areaCode": "Código <PERSON>", "number": "Número"}, "currencies": {"BRL": "Real brasileño", "USD": "Dólar EE. UU.", "EUR": "Euro", "CLP": "Peso chileno", "ARS": "Peso argentino"}, "routes": {"signIn": "/iniciar-sesion", "signUp": "/registro", "passwordChange": "/cambiar-contrasena", "passwordRecovery": "/recuperar-contrasena", "verifyAccount": "/verificar-cuenta", "contact": "/contacto", "tripcash": "/tripcash", "security": "/seguridad", "termsAndConditions": "/terminos-y-condiciones", "privacyPolicy": "/politica-de-privacidad", "search": "/busqueda", "hotel": "/hotel", "checkout": "/finalizar-compra", "purchase": "/compra", "myProfile": "/mi-perfil", "myReservations": "/mis-reservas", "myTripcash": "/mi-tripcash", "receipt": "/recibo"}}