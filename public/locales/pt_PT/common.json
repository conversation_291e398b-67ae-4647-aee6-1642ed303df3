{"navbar": {"home": "Início", "tripcash": {"title": "Tripcash", "balance": "Saldo do Tripcash"}, "language": {"portuguese": "Português", "portuguese_brazil": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)", "english": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spanish": "Espanhol"}, "signIn": "<PERSON><PERSON><PERSON>", "signUp": "Registe-se", "myProfile": "O meu perfil", "myReservations": "As minhas reservas", "myTripcash": "O meu Trip<PERSON>h", "logout": "<PERSON><PERSON><PERSON><PERSON>"}, "footer": {"company": {"title": "Empresa", "contact": "Contacto", "tripcash": "Tripcash"}, "support": {"title": "Suporte", "security": "Segurança", "privacy": "Política de Privacidade", "terms": "Termos e Condições"}, "paymentMethods": {"title": "Métodos de Pagamento", "12x": "até 12x", "transfers": "Transferências"}, "seals": {"title": "Selos e Verificações"}, "rights": "© {{year}} OurTrip. Todos os direitos reservados."}, "search": {"inputPlaceholder": "<PERSON><PERSON>, Data, Hóspedes", "destination": {"label": "<PERSON><PERSON>", "placeholder": "Onde quer ir?", "min": "A pesquisa por cidades requer no mínimo 3 letras!", "loading": "A carregar...", "notfound": "Nenhum resultado encontrado!"}, "dates": {"label": "Check-in | Check-out", "placeholder": "Selecione um período"}, "rooms": {"label": "Quartos", "placeholder": "1 quarto, 2 adultos", "room": "Quarto", "remove": "Remover", "adults": "Adultos", "kids": "Crian<PERSON><PERSON>", "kidsAge_zero": "Menos de 1 ano", "kidsAge_one": "1 ano", "kidsAge_other": "{{count}} anos", "addRoom": "Adicionar quarto", "apply": "Aplicar", "roomsAdultsAndKids": "$t(search.rooms.roomsCount, {'count': {{roomsCount}} })$t(search.rooms.adultsCount, {'count': {{adultsCount}} })$t(search.rooms.kidsCount, {'count': {{kidsCount}} })", "roomsCount_one": "{{count}} quarto", "roomsCount_other": "{{count}} quartos", "adultsCount_one": ", {{count}} adulto", "adultsCount_other": ", {{count}} adultos", "kidsCount_zero": "", "kidsCount_one": " e {{count}} crian<PERSON>", "kidsCount_other": " e {{count}} c<PERSON><PERSON><PERSON>", "adultsAndKids": "$t(search.rooms.adultsCount2, {'count': {{adultsCount}} })$t(search.rooms.kidsCount, {'count': {{kidsCount}} })", "adultsCount2_one": "{{count}} adulto", "adultsCount2_other": "{{count}} adultos"}, "filters": {"expand": "Ver mais", "collapse": "<PERSON>er menos", "collapseAll": "<PERSON><PERSON><PERSON><PERSON> todos"}, "search": "<PERSON><PERSON><PERSON><PERSON>"}, "cookies": {"title": "Utilizamos cookies para melhorar a sua experiência", "description": "Para oferecer a melhor experiência possível no nosso site, utilizamos cookies. Eles ajudam-nos a lembrar as suas preferências, personalizar o conteúdo e analisar como utiliza o nosso site. Com estas informações, garantimos que a sua navegação seja mais eficiente e que aceda ao conteúdo mais relevante.", "myPreferences": "As minhas preferências", "accept": "Aceitar cookies", "preferences": {"title": "Preferências", "necessaryCookies": {"title": "<PERSON><PERSON>", "description": "Estes cookies são fundamentais para o funcionamento básico do site. <PERSON><PERSON>, algumas funções essenciais, como o acesso a áreas seguras, não seriam possíveis."}, "analyticsCookies": {"title": "Cookies Analíticos", "description": "Utilizamos cookies analíticos para recolher dados sobre como os visitantes interagem com o nosso site. Essas informações ajudam-nos a perceber o desempenho do site e a melhorar a experiência."}, "advertisingCookies": {"title": "Cookies de Publicidade", "description": "Estes cookies são usados para mostrar anúncios relevantes com base nos seus interesses. Ajudam a medir a eficácia das campanhas publicitárias e a exibir anún<PERSON>s mais adequados."}, "functionalCookies": {"title": "Cookies Funcionais", "description": "Esses cookies são essenciais para o funcionamento do site e para oferecer uma experiência personalizada. Permitem, por exemplo, lembrar as suas preferências e definições."}, "whatIsCookies": {"title": "O que são Cook<PERSON>?", "description": "Os cookies são pequenos arquivos de texto armazenados no seu dispositivo quando visita um site. São usados para lembrar as suas escolhas e melhorar as funcionalidades do site."}, "save": "Guardar", "acceptAll": "Aceitar todos"}}, "cashbackCard": {"title": "Tripcash", "description": {"withValue": "Receberá até <span>{{value}}</span> ao efetuar esta compra!", "withoutValue": "Selecione um quarto para ver o seu cashback!"}}, "purchaseDetails": {"resume": {"button": "<PERSON><PERSON><PERSON>"}, "price": {"tripcash": "Ganha {{value}} de Tripcash!"}, "details": "<PERSON><PERSON><PERSON>"}, "validations": {"required": "Campo obrigatório", "invalidPhoneAreaCode": "Código de área inválido", "invalidPhoneNumber": "Número <PERSON>"}, "fieldsLabels": {"ddi": "DDI", "areaCode": "Código <PERSON>", "number": "Número"}, "currencies": {"BRL": "Real brasileiro", "USD": "Dólar dos Estados Unidos", "EUR": "Euro", "CLP": "Peso chileno", "ARS": "Peso argentino"}, "routes": {"signIn": "/entrar", "signUp": "/cadastro", "passwordChange": "/alterar-senha", "passwordRecovery": "/recuperar-senha", "verifyAccount": "/verificar-conta", "contact": "/contato", "tripcash": "/tripcash", "security": "/seguranca", "termsAndConditions": "/termos-e-condicoes", "privacyPolicy": "/politica-de-privacidade", "search": "/busca", "hotel": "/hotel", "checkout": "/checkout", "purchase": "/compra", "myProfile": "/meu-perfil", "myReservations": "/minhas-reservas", "myTripcash": "/meu-tripcash", "receipt": "/recibo"}}