{"title": "OurTrip | Reserva", "description": "Finalize sua reserva na OurTrip com facilidade. Escolha o método de pagamento, configure os quartos e garanta uma estadia inesquecível. Desfrute de processos simples, seguros e mergulhe na empolgação de planejar sua próxima jornada.", "steper": {"stepOf": "{{current}} de {{total}}", "next": "Próximo: {{title}}", "account": "Conta", "payment": "Forma de Pagamento", "guests": "<PERSON><PERSON><PERSON><PERSON>", "resume": "<PERSON><PERSON><PERSON>", "finish": "<PERSON><PERSON><PERSON><PERSON>"}, "steps": {"account": "Email", "payment": "Pagamento", "guests": "<PERSON><PERSON><PERSON><PERSON>", "resume": "Resumo"}, "account": {"title": "Informe seu email", "subtitle": "Precisamos que informe seu email para envio do voucher após a realização da reserva!", "input": "Email", "login": {"description": "Parece que você já possui uma conta. Realize o login para continuar sua compra!", "changeEmail": "Alterar email"}}, "payment": {"step": {"pix": "Pagamento via Pix", "creditCard": "Pagamento via Cartão", "external": "Dados de pagamento"}, "method": {"title": "Forma de pagamento", "pix": "Pix", "external": "Dados de pagamento", "creditCard": "Cartão de Crédito", "installments": "até 12x", "installment": "<PERSON><PERSON><PERSON><PERSON>"}, "card": {"title": "Cartão", "number": "Número", "name": "Nome no cartão", "cvv": "CVV", "expiry": "Validade", "installments": "<PERSON><PERSON><PERSON><PERSON>"}}, "payer": {"title": "Pa<PERSON>r", "useMyInfo": "Usar minhas informações de usuário", "warning": {"pix": "Você tem 1 hora para realizar o pagamento do Pix!"}, "personalInfo": {"title": "Informações pessoais", "email": "Email", "firstName": "Nome", "lastName": "Sobrenome", "fullName": "Nome completo", "document": {"type": "Documento", "number": "Número do documento"}, "phone": {"areaCode": "DDD", "ddi": "DDI", "number": "Telefone"}}, "address": {"title": "Endereço", "postalCode": "CEP", "state": "UF", "city": "Cidade", "country": "<PERSON><PERSON>", "street": "Logradouro", "number": "Número", "neighborhood": "Bairro", "complement": "Complemento"}}, "guests": {"data": "<PERSON><PERSON>", "birth": "Data de Nascimento", "adultIndex": "Adulto {{index}}"}, "room": {"title": "Quartos", "change": "Alterar", "index": "Quarto {{index}}", "freeCancelation": "Cancelament<PERSON> gr<PERSON><PERSON> até {{date}}"}, "tripcash": {"title": "Tripcash", "use": "<PERSON><PERSON><PERSON><PERSON>", "discount": "Desconto"}, "buttons": {"back": "Voltar", "next": "Próximo", "book": "<PERSON><PERSON><PERSON>"}, "terms": {"accept": "Li e aceito os", "termsAndConditions": "Termos e Condições", "and": "e", "privacyPolicy": "Política de Privacidade", "roomTerms": "Li e aceito os termos da hospedagem:"}, "validation": {"required": "Preencha o campo!", "invalid": {"email": "<PERSON><PERSON>", "name": "Nome inválido", "surname": "Sobrenome inválido", "cpf": "CPF inválido", "cnpj": "CNPJ inválido", "ddi": "DDI inválido", "areaCode": "DDD inválido", "phone": "Número de telefone inválido", "cvv": "CVV inválido", "date": "Data inválida"}}, "errors": {"tripcash": "Falha ao atualizar Trip<PERSON>h!", "default": "Ocorreu um erro ao finalizar seu pedido", "cep": "Não foi encontrado nenhum endereço para esse CEP"}, "expired": {"title": "Compra expirada!", "description": "Faça uma nova pesquisa e inicie uma nova compra para prosseguir.", "button": "Voltar para o Início"}, "exitConfirmation": {"title": "Tem certeza que deseja sair?", "description": "Se você sair, perder<PERSON> todas as informações preenchidas até o momento.", "cancel": "<PERSON><PERSON><PERSON>", "exit": "<PERSON><PERSON>"}, "details": "<PERSON><PERSON><PERSON>", "timeRemaining": "Tempo restante", "loading": "Processando"}