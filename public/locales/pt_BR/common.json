{"navbar": {"home": "Início", "tripcash": {"title": "Tripcash", "balance": "<PERSON><PERSON>"}, "language": {"portuguese": "Português", "portuguese_brazil": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)", "english": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spanish": "Espanhol"}, "signIn": "Entrar", "signUp": "Cadastre-se", "myProfile": "<PERSON><PERSON> perfil", "myReservations": "<PERSON><PERSON> reservas", "myTripcash": "<PERSON><PERSON>", "logout": "<PERSON><PERSON>"}, "footer": {"company": {"title": "Empresa", "contact": "Contato", "tripcash": "Tripcash"}, "support": {"title": "Suporte", "security": "Segurança", "privacy": "Política de Privacidade", "terms": "Termos e Condições"}, "paymentMethods": {"title": "Formas de Pagamentos", "12x": "Em até 12x", "transfers": "Transferências"}, "seals": {"title": "Selos e Verificações"}, "rights": "© {{year}} OurTrip Todos os direitos reservados."}, "search": {"inputPlaceholder": "<PERSON><PERSON>, Data, Hóspedes", "destination": {"label": "<PERSON><PERSON>", "placeholder": "Onde você quer ir?", "min": "A busca por cidades requer no mínimo 3 letras!", "loading": "Carregando...", "notfound": "Nenhum resultado encontrado!"}, "dates": {"label": "Check-in | Check-out", "placeholder": "Selecione um período"}, "rooms": {"label": "Quartos", "placeholder": "1 quarto, 2 adultos", "room": "Quarto", "remove": "Remover", "adults": "Adultos", "kids": "Crian<PERSON><PERSON>", "kidsAge_zero": "Menos de 1 ano", "kidsAge_one": "1 ano", "kidsAge_other": "{{count}} anos", "addRoom": "Adicionar quarto", "apply": "Aplicar", "roomsAdultsAndKids": "$t(search.rooms.roomsCount, {'count': {{roomsCount}} })$t(search.rooms.adultsCount, {'count': {{adultsCount}} })$t(search.rooms.kidsCount, {'count': {{kidsCount}} })", "roomsCount_one": "{{count}} quarto", "roomsCount_other": "{{count}} quartos", "adultsCount_one": ", {{count}} adulto", "adultsCount_other": ", {{count}} adultos", "kidsCount_zero": "", "kidsCount_one": " e {{count}} crian<PERSON>", "kidsCount_other": " e {{count}} c<PERSON><PERSON><PERSON>", "adultsAndKids": "$t(search.rooms.adultsCount2, {'count': {{adultsCount}} })$t(search.rooms.kidsCount, {'count': {{kidsCount}} })", "adultsCount2_one": "{{count}} adulto", "adultsCount2_other": "{{count}} adultos"}, "filters": {"expand": "Ver mais", "collapse": "<PERSON>er menos", "collapseAll": "<PERSON><PERSON><PERSON><PERSON> todos"}, "search": "<PERSON><PERSON><PERSON><PERSON>"}, "cookies": {"title": "Utilizamos cookies para melhorar sua experiência", "description": "Para oferecer a melhor experiência possível no nosso site, utilizamos cookies. Eles nos ajudam a lembrar suas preferências, personalizar o conteúdo e analisar como você utiliza nosso site. Com essas informações, podemos garantir que sua navegação seja mais eficiente e que você tenha acesso ao conteúdo mais relevante.", "myPreferences": "Minhas preferências", "accept": "Aceitar cookies", "preferences": {"title": "Preferências", "necessaryCookies": {"title": "<PERSON><PERSON>", "description": "Cookies necessários são fundamentais para o funcionamento básico do site. <PERSON><PERSON> <PERSON>, algumas funções essenciais, como o acesso a áreas seguras, não seriam possíveis."}, "analyticsCookies": {"title": "Cookies Analíticos", "description": "Utilizamos cookies analíticos para coletar dados sobre como os visitantes interagem com nosso site. Essas informações nos ajudam a entender o desempenho do site e a melhorar a experiência para todos."}, "advertisingCookies": {"title": "Cookies de Publicidade", "description": "Estes cookies são usados para mostrar anúncios relevantes com base nos seus interesses. Eles ajudam a medir a eficácia das campanhas publicitárias e a exibir anún<PERSON>s que sejam mais relevantes para você."}, "functionalCookies": {"title": "Cookies Funcionais", "description": "Esses cookies são essenciais para que o site funcione corretamente e ofereça uma experiência personalizada. Eles permitem, por exemplo, lembrar suas preferências e configurações durante sua navegação."}, "whatIsCookies": {"title": "O que são Cook<PERSON>?", "description": "Cookies são pequenos arquivos de texto armazenados no seu dispositivo quando você visita um site. Eles são utilizados para lembrar suas escolhas e melhorar a funcionalidade do site."}, "save": "<PERSON><PERSON>", "acceptAll": "Aceitar todos"}}, "cashbackCard": {"title": "Tripcash", "description": {"withValue": "Você receberá até <span>{{value}}</span> realizando essa compra!", "withoutValue": "Selecione um quarto para ver seu cashback!"}}, "purchaseDetails": {"resume": {"button": "<PERSON><PERSON><PERSON>"}, "price": {"tripcash": "Você ganha {{value}} de Tripcash!"}, "details": "<PERSON><PERSON><PERSON>"}, "validations": {"required": "Campo obrigatório", "invalidPhoneAreaCode": "DDD inválido", "invalidPhoneNumber": "Número <PERSON>"}, "fieldsLabels": {"ddi": "DDI", "areaCode": "DDD", "number": "Número"}, "currencies": {"BRL": "Real brasileiro", "USD": "Dólar dos Estados Unidos", "EUR": "Euro", "CLP": "Peso chileno", "ARS": "Peso argentino"}, "routes": {"signIn": "/entrar", "signUp": "/cadastro", "passwordChange": "/alterar-senha", "passwordRecovery": "/recuperar-senha", "verifyAccount": "/verificar-conta", "contact": "/contato", "tripcash": "/tripcash", "security": "/seguranca", "termsAndConditions": "/termos-e-condicoes", "privacyPolicy": "/politica-de-privacidade", "search": "/busca", "hotel": "/hotel", "checkout": "/checkout", "purchase": "/compra", "myProfile": "/meu-perfil", "myReservations": "/minhas-reservas", "myTripcash": "/meu-tripcash", "receipt": "/recibo"}}