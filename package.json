{"name": "website", "version": "2.4.4", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@mercadopago/sdk-js": "^0.0.3", "@next/mdx": "^15.3.3", "@phosphor-icons/react": "^2.1.7", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-select": "^2.1.6", "@rive-app/react-canvas": "^4.17.6", "@sentry/nextjs": "^9.22.0", "@types/mdx": "^2.0.13", "axios": "^1.3.4", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "cookies-next": "^3.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.4", "html2pdf.js": "^0.10.2", "i18next": "^24.0.5", "nanoid": "^4.0.2", "next": "^15.3.2", "next-i18next": "^15.4.0", "react": "^18.3.1", "react-circular-progressbar": "^2.1.0", "react-credit-cards-2": "^1.0.1", "react-datepicker": "^8.1.0", "react-dom": "^18.3.1", "react-hook-form": "^7.43.9", "react-i18next": "^15.1.3", "react-intersection-observer": "^9.5.2", "react-loading-skeleton": "^3.3.1", "react-slider": "^2.0.6", "react-toastify": "^10.0.6", "sharp": "^0.32.6", "tailwindcss-animate": "^1.0.7", "use-mask-input": "^3.4.2", "uuid": "^9.0.1"}, "devDependencies": {"@tailwindcss/postcss": "^4.0.10", "@types/d3-ease": "^3.0.2", "@types/gtag.js": "^0.0.18", "@types/node": "18.14.2", "@types/react": "^18.0.28", "@types/react-datepicker": "^4.15.0", "@types/react-dom": "^18.0.11", "@types/react-slider": "^1.3.6", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^5.54.0", "@typescript-eslint/parser": "^5.54.0", "eslint": "^8.57.1", "eslint-config-airbnb": "^19.0.4", "eslint-config-next": "^15.0.3", "eslint-config-prettier": "^8.6.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^4.6.0", "postcss": "^8.4.49", "prettier": "^2.8.4", "tailwindcss": "^4.0.10", "typescript": "^5"}}