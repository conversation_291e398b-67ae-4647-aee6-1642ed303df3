import withMDX from '@next/mdx';
import type { NextConfig } from 'next';
import { withSentryConfig } from '@sentry/nextjs';
import { i18n } from './next-i18next.config.js';

/** @type {import('next').NextConfig} */
const nextConfig: NextConfig = {
  output: 'standalone',
  reactStrictMode: false,
  compress: true,
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'ourtrips3.s3.amazonaws.com',
        pathname: '/**'
      }
    ]
  },
  pageExtensions: ['tsx', 'mdx'],
  env: {
    NEXT_PUBLIC_MAIN_API_BASE_URL: process.env.NEXT_PUBLIC_MAIN_API_BASE_URL,
    NEXT_PUBLIC_MERCADO_PAGO_KEY: process.env.NEXT_PUBLIC_MERCADO_PAGO_KEY,
    NEXT_PUBLIC_GOOGLE_ANALYTICS: process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS,
    NEXT_PUBLIC_CLARITY: process.env.NEXT_PUBLIC_CLARITY,
    NEXT_PUBLIC_GOOGLE_TAG_MANAGER: process.env.NEXT_PUBLIC_GOOGLE_TAG_MANAGER
  },
  i18n,
  async rewrites() {
    return [
      { source: '/sign-in', destination: '/entrar' },
      { source: '/iniciar-sesion', destination: '/entrar' },
      { source: '/sign-up', destination: '/cadastro' },
      { source: '/registro', destination: '/cadastro' },
      { source: '/change-password', destination: '/alterar-senha' },
      { source: '/cambiar-contrasena', destination: '/alterar-senha' },
      { source: '/recover-password', destination: '/recuperar-senha' },
      { source: '/recuperar-contrasena', destination: '/recuperar-senha' },
      { source: '/verify-account', destination: '/verificar-conta' },
      { source: '/verificar-cuenta', destination: '/verificar-conta' },
      { source: '/contact', destination: '/contato' },
      { source: '/contacto', destination: '/contato' },
      { source: '/security', destination: '/seguranca' },
      { source: '/seguridad', destination: '/seguranca' },
      { source: '/terms-and-conditions', destination: '/termos-e-condicoes' },
      { source: '/terminos-y-condiciones', destination: '/termos-e-condicoes' },
      { source: '/privacy-policy', destination: '/politica-de-privacidade' },
      {
        source: '/politica-de-privacidad',
        destination: '/politica-de-privacidade'
      },
      { source: '/search', destination: '/busca' },
      { source: '/busqueda', destination: '/busca' },
      { source: '/finalizar-compra/:id*', destination: '/checkout/:id*' },
      { source: '/purchase/:id*', destination: '/compra/:id*' },
      { source: '/my-profile', destination: '/meu-perfil' },
      { source: '/mi-perfil', destination: '/meu-perfil' },
      { source: '/my-bookings', destination: '/minhas-reservas' },
      { source: '/mis-reservas', destination: '/minhas-reservas' },
      { source: '/my-tripcash', destination: '/meu-tripcash' },
      { source: '/mi-tripcash', destination: '/meu-tripcash' },
      { source: '/receipt/:id*', destination: '/recibo/:id*' }
    ];
  }
};

export default withSentryConfig(withMDX({})(nextConfig), {
  org: 'ourtrip',
  project: 'front-site',
  silent: !process.env.CI,
  widenClientFileUpload: true,
  disableLogger: true,
  automaticVercelMonitors: true
});
