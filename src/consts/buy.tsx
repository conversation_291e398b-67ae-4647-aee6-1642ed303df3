import { CheckCircle, ClockCountdown, X } from '@phosphor-icons/react';
import { BookingInfo } from 'src/types/buy';

export const bookingInfo: BookingInfo = {
  PIX: {
    CONFIRMED: {
      title: 'Concluído',
      subtitle: 'Seu pagamento foi realizado e processado com sucesso!',
      icon: <CheckCircle size={25} weight='duotone' />,
      messageType: 'success'
    },
    PENDING_PAYMENT: {
      title: 'Falta pouco!',
      subtitle: 'Realize o pagamento do Pix para finalizar sua reserva',
      icon: <ClockCountdown size={25} weight='duotone' />,
      messageType: 'info'
    },
    PENDING_ANTIFRAUD: {
      title: 'Falta pouco!',
      subtitle: 'Realize o pagamento do Pix para finalizar sua reserva',
      icon: <ClockCountdown size={25} weight='duotone' />,
      messageType: 'info'
    },
    PENDING_CONFIRMATION: {
      title: 'Falta pouco!',
      subtitle: 'Realize o pagamento do Pix para finalizar sua reserva',
      icon: <ClockCountdown size={25} weight='duotone' />,
      messageType: 'info'
    },
    PAYMENT_REJECTED: {
      title: 'Pagamento rejeitado!',
      subtitle: 'Seu pagamento foi rejeitado, contate o suporte',
      icon: <X size={25} weight='bold' />,
      messageType: 'danger'
    },
    CANCELED: {
      title: 'Pedido cancelado!',
      subtitle: 'Um reembolso será realizado para sua conta',
      icon: <X size={25} weight='bold' />,
      messageType: 'danger'
    }
  },
  CREDIT_CARD: {
    CONFIRMED: {
      title: 'Pagamento efetuado!',
      subtitle: 'Seu pagamento foi realizado e processado com sucesso!',
      icon: <CheckCircle size={25} weight='duotone' />,
      messageType: 'success'
    },
    PENDING_PAYMENT: {
      title: 'Seu pagamento está sendo processado!',
      subtitle: 'Estamos validando a compra com sua instituição financeira...',
      icon: <ClockCountdown size={25} weight='duotone' />,
      messageType: 'info'
    },
    PENDING_ANTIFRAUD: {
      title: 'Seu pagamento está sendo processado!',
      subtitle: 'Estamos validando a compra com sua instituição financeira...',
      icon: <ClockCountdown size={25} weight='duotone' />,
      messageType: 'info'
    },
    PENDING_CONFIRMATION: {
      title: 'Seu pagamento está sendo processado!',
      subtitle: 'Estamos validando a compra com sua instituição financeira...',
      icon: <ClockCountdown size={25} weight='duotone' />,
      messageType: 'info'
    },
    PAYMENT_REJECTED: {
      title: 'Pagamento rejeitado!',
      subtitle: 'Seu pagamento foi rejeitado, contate o suporte',
      icon: <X size={25} weight='bold' />,
      messageType: 'danger'
    },
    CANCELED: {
      title: 'Pedido cancelado!',
      subtitle: 'Seu pagamento via cartão de crédito será estornado em breve',
      icon: <X size={25} weight='bold' />,
      messageType: 'danger'
    }
  },
  TRIPCASH: {
    CONFIRMED: {
      title: 'Pagamento realizado com Tripcash!',
      subtitle:
        'Seu Tripcash foi utilizado para esta compra, confira sua conta Tripcash para mais detalhes.',
      icon: <CheckCircle size={25} weight='duotone' />,
      messageType: 'success'
    },
    PENDING_PAYMENT: {
      title: 'Seu pagamento está sendo processado!',
      subtitle: 'Estamos validando sua compra para utilizar seu Tripcash...',
      icon: <ClockCountdown size={25} weight='duotone' />,
      messageType: 'info'
    },
    PENDING_CONFIRMATION: {
      title: 'Seu pagamento está sendo processado!',
      subtitle: 'Estamos validando sua compra para utilizar seu Tripcash...',
      icon: <ClockCountdown size={25} weight='duotone' />,
      messageType: 'info'
    },
    PENDING_ANTIFRAUD: {
      title: 'Seu pagamento está sendo processado!',
      subtitle: 'Estamos validando sua compra para utilizar seu Tripcash...',
      icon: <ClockCountdown size={25} weight='duotone' />,
      messageType: 'info'
    },
    PAYMENT_REJECTED: {
      title: 'Pagamento rejeitado!',
      subtitle: 'Seu pagamento foi rejeitado, contate o suporte',
      icon: <X size={25} weight='bold' />,
      messageType: 'danger'
    },
    CANCELED: {
      title: 'Pedido cancelado!',
      subtitle: 'Seu Tripcash foi devolvido à sua conta.',
      icon: <X size={25} weight='bold' />,
      messageType: 'danger'
    }
  },
  EXTERNAL: {
    CONFIRMED: {
      title: 'Pagamento realizado com Tripcash!',
      subtitle:
        'Seu Tripcash foi utilizado para esta compra, confira sua conta Tripcash para mais detalhes.',
      icon: <CheckCircle size={25} weight='duotone' />,
      messageType: 'success'
    },
    PENDING_PAYMENT: {
      title: 'Seu pagamento está sendo processado!',
      subtitle: 'Estamos validando sua compra para utilizar seu Tripcash...',
      icon: <ClockCountdown size={25} weight='duotone' />,
      messageType: 'info'
    },
    PENDING_CONFIRMATION: {
      title: 'Seu pagamento está sendo processado!',
      subtitle: 'Estamos validando sua compra para utilizar seu Tripcash...',
      icon: <ClockCountdown size={25} weight='duotone' />,
      messageType: 'info'
    },
    PENDING_ANTIFRAUD: {
      title: 'Seu pagamento está sendo processado!',
      subtitle: 'Estamos validando sua compra para utilizar seu Tripcash...',
      icon: <ClockCountdown size={25} weight='duotone' />,
      messageType: 'info'
    },
    PAYMENT_REJECTED: {
      title: 'Pagamento rejeitado!',
      subtitle: 'Seu pagamento foi rejeitado, contate o suporte',
      icon: <X size={25} weight='bold' />,
      messageType: 'danger'
    },
    CANCELED: {
      title: 'Pedido cancelado!',
      subtitle: 'Seu Tripcash foi devolvido à sua conta.',
      icon: <X size={25} weight='bold' />,
      messageType: 'danger'
    }
  }
};
