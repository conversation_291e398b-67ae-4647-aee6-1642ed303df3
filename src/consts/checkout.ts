import { GuestRoomType } from 'src/types/checkout';

export const requiredInputMessage = 'Campo obrigatório';

export const payerDefaultValues = {
  userAsPayer: false,
  email: '',
  firstName: '',
  lastName: '',
  identification: {
    documentType: 'CPF',
    documentNumber: ''
  },
  phone: {
    ddi: '',
    areaCode: '',
    number: ''
  }
};

export const guestsDefaultValues: GuestRoomType[] = [
  {
    adult: [
      {
        name: '',
        birthday: ''
      }
    ],
    document: ''
  }
];

export const addressDefaultValues = {
  street: '',
  number: '',
  complement: '',
  neighborhood: '',
  postalCode: '',
  city: '',
  uf: '',
  country: ''
};

export const creditCardPayerDefaulValues = {
  ...payerDefaultValues,
  address: addressDefaultValues
};

export const CreditCardDefaultValues = {
  cvv: '',
  name: '',
  number: '',
  expireDate: '',
  installments: 1,
  payer: creditCardPayerDefaulValues
};

export const FormDefaultValues = {
  email: '',
  pix: { ...payerDefaultValues },
  external: { ...payerDefaultValues },
  creditCard: { ...CreditCardDefaultValues },
  room: []
};

export const documentTypeOptions = [
  { label: 'CPF', value: 'CPF' },
  { label: 'CNPJ', value: 'CNPJ' }
];

export const CardDetailsDefaultValues = {
  issuer: undefined,
  totalAmount: undefined,
  installments: 1,
  installmentAmount: undefined,
  paymentMethodId: undefined,
  paymentMethodOptionId: undefined,
  instalmentRates: undefined
};

const defaultCardMask = {
  number: '9999 9999 9999 9999',
  cvv: '999'
};

export const cardMasks = {
  amex: {
    number: '9999 999999 99999',
    cvv: '9999'
  },
  visa: { ...defaultCardMask },
  master: { ...defaultCardMask },
  default: { ...defaultCardMask }
};
