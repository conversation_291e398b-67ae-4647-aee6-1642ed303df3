import { ISearchPayload, ISearchResponse } from 'src/types/search';

export const roomInitialValues = {
  adults: 2,
  kids: []
};

export const filtersInitialValues = {
  mealPlans: [],
  stars: [],
  acommodationTypes: [],
  amenities: [],
  price: null,
  hotelName: null,
  cancellationPolicyRefundable: false,
  tripcash: []
};

export const searchPayloadDefaultValue: ISearchPayload = {
  destination: {
    display: '',
    id: '',
    group: '',
    location: { latitude: 0, longitude: 0 }
  },
  checkin: '',
  checkout: '',
  distribution: [{ ...roomInitialValues }],
  page: 0,
  limit: 20,
  searchId: '',
  sorting: 'relevance',
  filters: filtersInitialValues,
  channel: 'SITE',
  lang: ''
};

export const searchResponseDefaultValues: ISearchResponse = {
  searchId: '',
  checkin: '',
  checkout: '',
  paging: {
    page: 0,
    limit: 0,
    total: 0
  },
  filtersApplied: [],
  filtersMain: [],
  sorting: [],
  result: []
};

export const childrenAgeOptions = [
  { value: '0', label: 'Menos de 1 ano' },
  { value: '1', label: '1 ano' },
  { value: '2', label: '2 anos' },
  { value: '3', label: '3 anos' },
  { value: '4', label: '4 anos' },
  { value: '5', label: '5 anos' },
  { value: '6', label: '6 anos' },
  { value: '7', label: '7 anos' },
  { value: '8', label: '8 anos' },
  { value: '9', label: '9 anos' },
  { value: '10', label: '10 anos' },
  { value: '11', label: '11 anos' },
  { value: '12', label: '12 anos' },
  { value: '13', label: '13 anos' },
  { value: '14', label: '14 anos' },
  { value: '15', label: '15 anos' },
  { value: '16', label: '16 anos' },
  { value: '17', label: '17 anos' }
];
