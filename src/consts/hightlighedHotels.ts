import { IDestination } from 'src/types/search';

type HighlightedHotel = {
  stars: number;
  name: string;
  image: string;
  address: string;
  destination: IDestination;
};

const highlightedHotels: HighlightedHotel[] = [
  {
    stars: 3,
    name: 'Estanplaza International',
    address: '<PERSON><PERSON>, 1293 - Chácara Santo Antônio, São Paulo',
    image:
      'https://i.travelapi.com/lodging/1000000/880000/874200/874124/ac00677d_z.jpg',
    destination: {
      id: '120187',
      group: 'HOTEL',
      display: 'Estanplaza International',
      location: { latitude: -23.629772, longitude: -46.706624 }
    }
  },
  {
    stars: 4,
    name: 'Novotel São Paulo Morumbi',
    address: 'R. <PERSON>. <PERSON>, 577 - <PERSON>ila <PERSON>, São Paulo',
    image:
      'https://cf.bstatic.com/xdata/images/hotel/max1024x768/*********.jpg?k=84d61c44ebc3b713faa4674c62e266559aed02c544b865320a295da9a46540fd&o=&hp=1',
    destination: {
      id: '118107',
      group: 'HOTEL',
      display: 'Novotel São Paulo Morumbi',
      location: { latitude: -23.613537, longitude: -46.704037 }
    }
  },
  {
    stars: 5,
    name: 'Melia Paulista',
    address: 'Av. Paulista, 2181 - Consolação, São Paulo',
    image:
      'https://dam.melia.com/melia/accounts/f8/4000018/projects/127/assets/a2/51579/e3f89a2838522af9dfae51f1d4d3775b-**********.jpg?fp=1771.5,1156.5&width=2000&height=1306',
    destination: {
      id: '120300',
      group: 'HOTEL',
      display: 'Melia Paulista',
      location: { latitude: -23.557943, longitude: -46.660774 }
    }
  },
  {
    stars: 5,
    name: 'Sheraton Grand Rio Hotel & Resort',
    address: 'Av. Niemeyer, 121 - Leblon, Rio de Janeiro',
    image:
      'https://cache.marriott.com/content/dam/marriott-renditions/RIOSI/riosi-suite-3656-hor-clsc.jpg?output-quality=70&interpolation=progressive-bilinear&downsize=1336px:*',
    destination: {
      id: '118240',
      group: 'HOTEL',
      display: 'Sheraton Grand Rio Hotel & Resort',
      location: { latitude: -22.99205, longitude: -43.23371 }
    }
  },
  {
    stars: 4,
    name: 'Hilton Barra Rio de Janeiro',
    address: 'Abelardo Bueno Avenue',
    image:
      'https://www.hilton.com/im/en/RIOABHH/5651979/rioab-exterior-01.jpg?impolicy=crop&cw=4500&ch=2929&gravity=NorthWest&xposition=0&yposition=35&rw=768&rh=500',
    destination: {
      id: '108384',
      group: 'HOTEL',
      display: 'Hilton Barra Rio de Janeiro',
      location: { latitude: -22.971949, longitude: -43.374252 }
    }
  },
  {
    stars: 4,
    name: 'Rio Othon Palace',
    address: 'Avenida Atlantica 3264',
    image:
      'https://media.omnibees.com/Images/2042/RoomTypes/640x426/663952.jpg',
    destination: {
      id: '104585',
      group: 'HOTEL',
      display: 'Rio Othon Palace',
      location: { latitude: -22.97746, longitude: -43.188711 }
    }
  },
  {
    stars: 4,
    name: 'Novotel Salvador Hangar Aeroporto',
    address: 'Av Luis Viana Filho 13 223',
    image:
      'https://novotel-hangar-aeroporto.hotelsinsalvador.com/data/Photos/r1920x1080/12828/1282820/1282820821/salvador-novotel-hangar-aeroporto-photo-1.JPEG',
    destination: {
      id: '101967',
      group: 'HOTEL',
      display: 'Novotel Salvador Hangar Aeroporto',
      location: { latitude: -12.920143, longitude: -38.35466 }
    }
  },
  {
    stars: 4,
    name: 'Hotel Deville Prime Salvador',
    address: 'R. Passárgada, S/n - Itapuã, Salvador - BA, 41620-430',
    image:
      'https://lirp.cdn-website.com/e11124be/dms3rep/multi/opt/Luxo+1+cama+casal+%281%29-7875c6b2-1920w.jpg',
    destination: {
      id: '103981',
      group: 'HOTEL',
      display: 'Hotel Deville Prime Salvador',
      location: { latitude: -12.94978, longitude: -38.351382 }
    }
  }
];

export default highlightedHotels;
