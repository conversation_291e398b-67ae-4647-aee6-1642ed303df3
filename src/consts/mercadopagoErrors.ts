export const mercadoPagoErrors = {
  '205': 'Digite o número do seu cartão',
  '208': 'Escolha um mês',
  '209': 'Escolha um ano',
  '212': 'Informe seu documento',
  '214': 'Informe seu documento',
  '220': 'Informe seu banco emissor',
  '221': 'Digite o nome e sobrenome do titular do cartão',
  '224': 'Digite o código de segurança',
  E203: 'Código de segurança inválido',
  E205: 'Ano de validade do cartão de crédito é inválido',
  E301: 'Há algo de errado com esse número do cartão. Digite novamente.',
  '316': 'Por favor, digite um nome e sobrenome do titular do cartão.',
  '322': 'Confira seu documento.',
  '324': 'Confira seu documento.',
  '325': 'Confira a data de validade do cartão.',
  '326': 'Confira a data de validade do cartão.',
  default: 'Confira os dados de pagamento.'
};

export type MercadoPageErrorTypes =
  | '205'
  | '208'
  | '209'
  | '212'
  | '214'
  | '220'
  | '221'
  | '224'
  | 'E203'
  | 'E205'
  | 'E301'
  | '316'
  | '322'
  | '324'
  | '325'
  | '326'
  | 'default';
