export const range = (start: number, end: number) => {
  const output = [];

  for (let i = start; i < end; i += 1) {
    output.push(i);
  }
  return output;
};

export const variants = {
  enter: (direction: number) => {
    return {
      x: direction > 0 ? 1000 : -1000,
      opacity: 0
    };
  },
  center: {
    x: 0,
    opacity: 1
  },
  exit: (direction: number) => {
    return {
      x: direction < 0 ? 1000 : -1000,
      opacity: 0
    };
  }
};
