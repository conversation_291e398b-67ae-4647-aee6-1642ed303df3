export const CPFMask = (v: string): string => {
  let value: string = '';

  value = v.replace(/\D/g, '');
  value = value.replace(/^(\d{3})(\d)/g, '$1.$2');
  value = value.replace(/^(\d{3})\.(\d{3})(\d)/, '$1.$2.$3');
  value = value.replace(/^(\d{3})\.(\d{3})\.(\d{3})(\d)/, '$1.$2.$3-$4');
  value = value.replace(
    /^(\d{3})\.(\d{3})\.(\d{3})\/(\d{2})(\d)/,
    '$1.$2.$3-$4'
  );

  return value.substring(0, 14);
};
