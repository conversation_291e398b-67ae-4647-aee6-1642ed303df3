/* eslint-disable import/no-duplicates */
import { format, Locale } from 'date-fns';
import { pt, ptBR, enUS, es } from 'date-fns/locale';
import { capitalizeFirstLetter } from './strings';

export const convertStringToUTCDate = (data: string): Date => {
  const date = new Date(data);
  return data.includes('T')
    ? date
    : new Date(date.valueOf() + date.getTimezoneOffset() * 60 * 1000);
};

export const getDateFNSLocale = (locale: string) => {
  switch (locale) {
    case 'pt_BR':
      return ptBR;
    case 'pt_PT':
      return pt;
    case 'en_US':
      return enUS;
    case 'es_ES':
      return es;
    default:
      return ptBR;
  }
};

export const convertStringToDefaultFormatedDate = (
  locale: Locale,
  date?: string
) => {
  if (!date || date === '') return '';
  try {
    const data = convertStringToUTCDate(date);
    return format(data, 'P', {
      locale
    });
  } catch (err) {
    return '-';
  }
};

export const convertStringToFormatedDate = (locale: Locale, value?: string) => {
  if (!value || value === '') return '';

  const date = convertStringToUTCDate(value);
  const day = capitalizeFirstLetter(format(date, 'PPPP', { locale }));

  return `${day}`;
};

export const convertSecondsToMinutes = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;

  const formatedMinutes = minutes < 10 ? `0${minutes}` : minutes;
  const formatedSeconds =
    remainingSeconds < 10 ? `0${remainingSeconds}` : remainingSeconds;

  return `${formatedMinutes}:${formatedSeconds}`;
};
