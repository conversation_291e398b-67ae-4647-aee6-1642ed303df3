import { PurchasePricingItemType } from '@components/molecules/PurchasePricing';
import { TFunction, i18n as I18n } from 'i18next';
import { AddressByCep } from 'src/server/cep';
import {
  ICartInstallments,
  ICartResponse,
  Price,
  PriceDescription
} from 'src/types/cart';
import {
  GuestFields,
  GuestPayload,
  GuestRoomType,
  IGuestsPayload,
  ICheckoutCreditCardPayerAddress,
  ICheckoutRequestPayer,
  PaymentMethodEnum,
  PaymentMethodType,
  ICheckoutRequestPayload
} from 'src/types/checkout';
import { IRegisterPayload } from 'src/types/customer';
import { NextRouter } from 'next/router';
import { mountRoomDistributionStringFromRoomDistribution } from './search';
import { convertStringToDefaultFormatedDate, getDateFNSLocale } from './date';

const mountGuestPayload = (
  guests: GuestFields[],
  document: string,
  child: boolean
) => {
  return guests.map((guest): GuestPayload => {
    const fullName = guest.name.trim().split(' ');
    return {
      name: fullName.shift() as string,
      surname: fullName.join(' '),
      document,
      age: guest.age,
      birthday: guest.birthday,
      child
    };
  });
};

export const mountGuestsPayload = (
  rooms: GuestRoomType[]
): IGuestsPayload[] => {
  return rooms.map((room, index): IGuestsPayload => {
    const adults = mountGuestPayload(room.adult, room.document, false);
    const kids = mountGuestPayload(room.kid ?? [], room.document, true);

    return { index, guests: [...adults, ...kids] };
  });
};

export const removeMasks = (obj: any): any => {
  if (typeof obj === 'string') {
    const formatedValue = obj.replace(/[().\-/]/g, '');
    return formatedValue;
  }
  if (typeof obj === 'object' && obj !== null) {
    if (Array.isArray(obj)) {
      return obj.map(item => removeMasks(item));
    }
    const newObj: { [key: string]: any } = {};
    Object.keys(obj).forEach(key => {
      newObj[key] = removeMasks(obj[key]);
    });
    return newObj;
  }
  return obj;
};

export const createPurchaseItemsFromPriceDescriptions = (
  priceDesciprtions: PriceDescription[]
): PurchasePricingItemType[] => {
  return priceDesciprtions.map(description => {
    return {
      text: description.text,
      value: `${description.currency ? description.currency : ''} ${
        description.value
      }`,
      discount: description.discount ?? false
    };
  });
};

export const mountAdressValues = (
  data: AddressByCep
): ICheckoutCreditCardPayerAddress => {
  return {
    city: data?.localidade,
    complement: data?.complemento,
    country: 'Brasil',
    neighborhood: data.bairro,
    uf: data.uf,
    street: data.logradouro,
    postalCode: data.cep,
    number: ''
  };
};

export const mountInstallmentOptions = (installments?: ICartInstallments[]) => {
  return (
    installments?.map(installment => {
      return {
        key: installment.pkTokenization,
        label: installment.recommendedMessage,
        installmentOptionId: installment.installmentOptionId,
        value: installment.installments
      };
    }) ?? []
  );
};

export const getPurchaseDescriptionItems = (
  t: TFunction,
  checkoutData: ICartResponse,
  tripcashApplied: boolean,
  paymentMethod: PaymentMethodType,
  selectedInstallment: number
) => {
  let price: Price;

  if (tripcashApplied) {
    price =
      paymentMethod === 'PIX'
        ? checkoutData.pricePromotionalWithTripcash
        : checkoutData.priceWithTripcash;
  } else {
    price =
      paymentMethod === 'PIX'
        ? checkoutData.pricePromotional
        : checkoutData.price;
  }

  const {
    currencySymbol,
    summary: { totalWithoutTaxes, taxesAndFees, adjustments, subTotal }
  } = price;

  const items: PriceDescription[] = [
    { ...totalWithoutTaxes, currency: currencySymbol },
    { ...taxesAndFees, currency: currencySymbol }
  ];

  const showInstallments =
    paymentMethod === PaymentMethodEnum.CREDIT_CARD && selectedInstallment > 1;

  if (tripcashApplied || showInstallments)
    items.push({
      ...subTotal,
      currency: currencySymbol
    });

  items.push(
    ...adjustments.map(adjustment => {
      return {
        text: adjustment.text,
        value: `- ${currencySymbol} ${adjustment.value.replace('-', '')}`,
        discount: true
      };
    })
  );

  if (showInstallments) {
    const selectedInstallmentOption = tripcashApplied
      ? checkoutData.paymentInstallmentsWithTripcash
      : checkoutData.paymentInstallments;
    const option = selectedInstallmentOption.find(
      installmentOption =>
        installmentOption.installments === selectedInstallment
    );

    items.push({
      text: t('payment.method.installment'),
      value: option?.installmentAmountText ?? ''
    });
  }
  return createPurchaseItemsFromPriceDescriptions(items);
};

export const getTripcashInfo = (
  isTripcashApplied: boolean,
  paymentMethod: PaymentMethodType,
  checkoutData: ICartResponse
) => {
  if (isTripcashApplied) {
    return paymentMethod === 'PIX'
      ? checkoutData!.pricePromotionalWithTripcash.tripcashInfo
      : checkoutData!.priceWithTripcash.tripcashInfo;
  }

  return paymentMethod === 'PIX'
    ? checkoutData!.pricePromotional.tripcashInfo
    : checkoutData!.price.tripcashInfo;
};

export const getTotalPrice = (
  paymentMethod: PaymentMethodType,
  selectedInstallment: number,
  isTripcashApplied: boolean,
  checkoutData: ICartResponse
) => {
  let price;

  if (
    paymentMethod === PaymentMethodEnum.CREDIT_CARD &&
    selectedInstallment > 1
  ) {
    const selectedInstallmentOption = isTripcashApplied
      ? checkoutData!.paymentInstallmentsWithTripcash
      : checkoutData!.paymentInstallments;

    price = selectedInstallmentOption.find(
      el => el.installments === selectedInstallment
    )?.totalAmount;
  } else if (isTripcashApplied) {
    price =
      paymentMethod === PaymentMethodEnum.PIX
        ? checkoutData!.pricePromotionalWithTripcash.price
        : checkoutData!.priceWithTripcash.price;
  } else {
    price =
      paymentMethod === PaymentMethodEnum.PIX
        ? checkoutData!.pricePromotional?.price || checkoutData!.price.price
        : checkoutData!.price.price;
  }

  return price;
};

export const getDistributionConfig = (
  t: TFunction,
  i18n: I18n,
  checkoutData: ICartResponse
) => {
  const {
    items: [{ offers }],
    distribution
  } = checkoutData;
  return offers.map((offer, index) => {
    return {
      index: offer.accommodationIndex,
      title: t('room.index', { index: index + 1 }),
      text: offer.accommodationName,
      value: mountRoomDistributionStringFromRoomDistribution(
        t,
        distribution[index]
      ),
      description: offer.cancellationPolicies.refundable
        ? t('room.freeCancelation', {
            date: convertStringToDefaultFormatedDate(
              getDateFNSLocale(i18n.language),
              offer.cancellationPolicies.cancellationLimitDate
            )
          })
        : undefined,
      refundable: offer.cancellationPolicies.refundable,
      rateComments: offer.rateComments,
      mealPlan: offer.mealPlan.description
    };
  });
};

export const sendGTMEvent = (
  payload: ICheckoutRequestPayload,
  total: number,
  checkoutData: ICartResponse
) => {
  window?.gtag?.('event', 'purchase', {
    transaction_id: payload.cartId,
    value: total,
    currency: checkoutData!.price.currency,
    items: checkoutData!.items.map(item =>
      Object({
        item_id: checkoutData!.hotel.id,
        item_name: checkoutData!.hotel.name,
        item_category: item.type,
        price: item.prices.priceWithDecimal
      })
    )
  });
};

export const redirectToPurchase = (
  t: TFunction,
  language: string,
  orderId: string,
  router: NextRouter
) => {
  router.push({ pathname: `${t('routes.purchase')}/${orderId}` }, '', {
    locale: language
  });
};

export const mountRegisterPayloadByFormValues = (
  formValues: ICheckoutRequestPayer,
  lang: string
): IRegisterPayload => {
  const {
    email,
    phone,
    firstName,
    lastName,
    identification: { documentNumber, documentType }
  } = formValues;
  return {
    email,
    phone,
    name: firstName,
    surname: lastName,
    documentNumber,
    documentType,
    password: '',
    lang
  };
};
