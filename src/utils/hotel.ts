import {
  IHotelOffer,
  IHotelOfferPlan,
  IHotelOfferPrices
} from 'src/types/hotel';
import { PurchaseResumeConfigType } from '@components/molecules/PurchaseResume';
import { differenceInDays, Locale } from 'date-fns';
import { TFunction } from 'next-i18next';
import { mountRoomDistributionStringFromRoomDistribution } from './search';
import { convertNumberToCurrency } from './currency';
import { convertStringToDefaultFormatedDate } from './date';

export const getSelectedOffersByTokens = (
  offerPlans: IHotelOfferPlan[],
  selectedOfferTokens: string[]
) => {
  const selectedOffersObject: IHotelOffer[] = [];
  for (let i = 0; i < selectedOfferTokens.length; i++) {
    offerPlans?.forEach(offerPlan => {
      offerPlan.offers?.forEach(offer => {
        if (
          offer.accommodationIndex === i &&
          offer.token === selectedOfferTokens[i]
        ) {
          selectedOffersObject.push(offer);
        }
      });
    });
  }

  return selectedOffersObject;
};

export const getSelectedOffersPrices = (
  selectedOffersObject: IHotelOffer[]
) => {
  return selectedOffersObject?.map(offer => offer?.prices);
};

export const getRoomNameAndDistribution = (
  t: TFunction,
  locale: Locale,
  offerPlans: IHotelOfferPlan[],
  distribution: any
): PurchaseResumeConfigType[] => {
  return offerPlans?.map((offerPlan, index) =>
    Object({
      title: t('availableRooms.content.room', { number: index + 1 }),
      text: offerPlan.accommodationName,
      value: mountRoomDistributionStringFromRoomDistribution(
        t,
        distribution[index]
      ),
      description: offerPlan.cancellationPolicies.refundable
        ? t('availableRooms.content.freeCancellation', {
            date: convertStringToDefaultFormatedDate(
              locale,
              offerPlan.cancellationPolicies.cancellationLimitDate
            )
          })
        : null,
      refundable: offerPlan.cancellationPolicies.refundable,
      mealPlan: offerPlan.mealPlan.description
    })
  );
};

export const getSelectedOfferPlansByTokens = (
  offerPlans: IHotelOfferPlan[],
  selectedOfferTokens: string[]
) => {
  const selectedOfferPlansObject: IHotelOfferPlan[] = [];

  offerPlans?.forEach(offerPlan => {
    offerPlan.offers.forEach(offer => {
      if (selectedOfferTokens.includes(offer.token)) {
        const tokenIndex = selectedOfferTokens?.indexOf(offer.token);
        selectedOfferPlansObject[tokenIndex] = offerPlan;
      }
    });
  });

  return selectedOfferPlansObject;
};
export const getTotalPriceBySelectedOffersPrices = (
  selectedOffersPrices: IHotelOfferPrices[]
) => {
  return selectedOffersPrices?.reduce(
    (total, offer) =>
      total + (offer?.finalPricePromotional || offer?.finalPrice || 0),
    0
  );
};

export const getPurchasePriceDetailsDistribution = (
  t: TFunction,
  checkinDate: string | undefined,
  checkoutDate: string | undefined,
  roomsAmount: number
) => {
  const dailyQuantity =
    checkinDate && checkoutDate
      ? differenceInDays(new Date(checkoutDate), new Date(checkinDate))
      : 0;

  return t('purchaseResume.roomsAndDaily', {
    dailyCount: dailyQuantity,
    roomsCount: roomsAmount
  });
};

export const getPurchasePriceDetailsPrice = (
  locale: string,
  currency: string,
  selectedOffersPrices: IHotelOfferPrices[]
) => {
  return convertNumberToCurrency(
    locale,
    currency,
    selectedOffersPrices.reduce(
      (total, offer) => total + (offer?.pricePromotional || offer?.price || 0),
      0
    )
  );
};

export const getPurchasePriceDetailsTaxValue = (
  locale: string,
  currency: string,
  selectedOffersPrices: IHotelOfferPrices[]
) => {
  return convertNumberToCurrency(
    locale,
    currency,
    selectedOffersPrices.reduce(
      (total, offer) =>
        total + (offer?.taxValuePromotional || offer?.taxValue || 0),
      0
    )
  );
};

export const getPurchasePriceDetails = (
  t: TFunction,
  locale: string,
  currency: string,
  checkinDate: string | undefined,
  checkoutDate: string | undefined,
  selectedOffersObject: IHotelOffer[],
  selectedOffersPrices: IHotelOfferPrices[]
) => {
  return [
    {
      text: getPurchasePriceDetailsDistribution(
        t,
        checkinDate,
        checkoutDate,
        selectedOffersObject.length
      ),
      value: getPurchasePriceDetailsPrice(
        locale,
        currency,
        selectedOffersPrices
      ),
      type: 'price'
    },
    {
      text: t('purchaseResume.taxAndFees'),
      value: getPurchasePriceDetailsTaxValue(
        locale,
        currency,
        selectedOffersPrices
      ),
      type: 'price'
    },
    {
      text: t('purchaseResume.payment'),
      value:
        currency === 'BRL'
          ? t('purchaseResume.paymentMethodBRL')
          : t('purchaseResume.paymentMethod'),
      type: 'info'
    }
  ];
};
