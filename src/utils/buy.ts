import { PriceValues } from 'src/types/buy';
import { convertNumberToCurrency } from './currency';

export const createPurchaseItemsFromPriceValues = (
  locale: string,
  currency: string,
  priceValues: PriceValues[]
) => {
  return priceValues.map(value => {
    return {
      text: value.description,
      value: value?.value
        ? `${value.discount ? '-' : ''} ${convertNumberToCurrency(
            locale,
            currency,
            value.value
          )}`
        : `${value.currency ? value.currency : ''} ${value.formattedValue}`,
      discount: !!value.discount
    };
  });
};
