export const capitalizeFirstLetter = (value: string) => {
  return value.charAt(0).toUpperCase() + value.slice(1);
};

export const isJson = (item: string) => {
  let value = typeof item !== 'string' ? JSON.stringify(item) : item;
  try {
    value = JSON.parse(value);
  } catch (e) {
    return false;
  }

  return typeof value === 'object' && value !== null;
};

export const extractPath = (url: string): string => {
  const match = url.match(/https?:\/\/[^/]+(\/.*)?/);
  return match && match[1] ? match[1] : '/';
};
