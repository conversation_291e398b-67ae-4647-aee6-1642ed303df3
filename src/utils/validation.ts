import { isBefore, isValid, parse, subYears } from 'date-fns';

export const isValidCpf = (formatedCpf: string): boolean => {
  const cpf = formatedCpf.replace(/\D/g, '');

  if (cpf.length !== 11 || /^(\d)\1+$/.test(cpf)) {
    return false;
  }

  // Calcular el primer dígito verificador
  let soma = 0;

  for (let i = 0; i < 9; i++) {
    soma += parseInt(cpf.charAt(i), 10) * (10 - i);
  }
  let digito1 = 11 - (soma % 11);
  if (digito1 > 9) {
    digito1 = 0;
  }

  // Calcular el segundo dígito verificador
  soma = 0;
  for (let i = 0; i < 10; i++) {
    soma += parseInt(cpf.charAt(i), 10) * (11 - i);
  }
  let digito2 = 11 - (soma % 11);
  if (digito2 > 9) {
    digito2 = 0;
  }

  // Verificar si los dígitos verificadores son iguales a los últimos dos dígitos del CPF
  if (
    parseInt(cpf.charAt(9), 10) === digito1 &&
    parseInt(cpf.charAt(10), 10) === digito2
  ) {
    return true;
  }
  return false;
};

export const isValidCNPJ = (formatedCnpj: string): boolean => {
  const cnpj = formatedCnpj.replace(/[^\d]+/g, '');

  if (cnpj.length !== 14) {
    return false;
  }

  if (/^(\d)\1+$/.test(cnpj)) {
    return false;
  }

  let length = cnpj.length - 2;
  let numbers = cnpj.substring(0, length);
  const digitosVerificadores = cnpj.substring(length);

  let soma = 0;
  let pos = length - 7;

  for (let i = length; i >= 1; i--) {
    soma += parseInt(numbers.charAt(length - i), 10) * pos--;
    if (pos < 2) {
      pos = 9;
    }
  }

  const resultado = soma % 11 < 2 ? 0 : 11 - (soma % 11);

  if (resultado !== parseInt(digitosVerificadores.charAt(0), 10)) {
    return false;
  }

  length += 1;
  numbers = cnpj.substring(0, length);
  soma = 0;
  pos = length - 7;

  for (let i = length; i >= 1; i--) {
    soma += parseInt(numbers.charAt(length - i), 10) * pos--;
    if (pos < 2) {
      pos = 9;
    }
  }

  const segundoDigito = soma % 11 < 2 ? 0 : 11 - (soma % 11);

  return segundoDigito === parseInt(digitosVerificadores.charAt(1), 10);
};

export const isCPF = (document: string) => {
  const onlyNumbers = document?.replace(/\D/g, '');

  if (onlyNumbers?.length === 11 && isValidCpf(onlyNumbers)) return true;

  return false;
};

export const isCNPJ = (document: string) => {
  const onlyNumbers = document?.replace(/\D/g, '');

  if (onlyNumbers?.length === 14 && isValidCNPJ(onlyNumbers)) return true;

  return false;
};

export const isFullName = (name: string) =>
  /^(?:[A-Za-zÀ-ÿ]{2,}(?: [A-Za-zÀ-ÿ]{2,})+)$/.test(name.trim());

export const isValidName = (name: string) =>
  /^[A-Za-zÀ-ÿ\s.'-]+(?: [A-Za-zÀ-ÿ\s.'-]+)*$/.test(name.trim());

export const isValidEmail = (email: string) =>
  /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/.test(email.trim());

export const isValidPhoneNumber = (number: string) => {
  const phoneNumber = number.replace('-', '');
  return /^\d{8}$/.test(phoneNumber) || /^\d{9}$/.test(phoneNumber);
};

export const isValidNumberCode = (numberCode: string) =>
  /^\d{2}$/.test(numberCode.replace(/[()]/g, ''));

export const isValidDDD = (areaCode: string) => {
  const dddList = [
    11, 12, 13, 14, 15, 16, 17, 18, 19, 21, 22, 24, 27, 28, 31, 32, 33, 34, 35,
    37, 38, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 53, 54, 55, 61, 62, 64, 63,
    65, 66, 67, 68, 69, 71, 73, 74, 75, 77, 79, 81, 82, 83, 84, 85, 86, 87, 88,
    89, 91, 92, 93, 94, 95, 96, 97, 98, 99
  ];

  return dddList.includes(parseFloat(areaCode?.replace(/\D/g, '')));
};

export const isValidChildDate = (date?: string) => {
  if (!date) return 'Data inválida';

  const birthDate = parse(date, 'yyyy-MM-dd', new Date());

  if (!isValid(birthDate)) {
    return 'Data inválida';
  }

  const currentDate = new Date();
  const adultAge = subYears(currentDate, 18);

  if (isBefore(adultAge, birthDate) && isBefore(birthDate, currentDate)) {
    return true;
  }

  return 'Idade deve ser entre 1 a 17 anos';
};

export const isValidAdult = (date?: string) => {
  if (!date) return 'Data inválida';

  const birthDate = parse(date, 'yyyy-MM-dd', new Date());

  if (!isValid(birthDate)) {
    return 'Data inválida';
  }

  const adultAge = subYears(new Date(), 18);

  return isBefore(birthDate, adultAge) || 'Adulto deve ter mais de 18 anos';
};
