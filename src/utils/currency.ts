export const convertNumberToCurrency = (
  locale: string,
  currency: string,
  value: number
) => {
  if (!locale) return '';

  const formatter = new Intl.NumberFormat(locale?.replace('_', '-'), {
    style: 'currency',
    currencyDisplay: 'code',
    currency
  });

  return formatter
    .format(value / 100)
    .replace(currency, '')
    .trim();
};

export const convertNumberToCurrencyWithoutCurrency = (value: number) => {
  const formatter = new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  });

  return formatter.format(value / 100).replace('R$', '');
};

export const convertNumberToCurrencyWithDecimal = (value: number) => {
  const formatter = new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  });

  return formatter.format(value);
};
