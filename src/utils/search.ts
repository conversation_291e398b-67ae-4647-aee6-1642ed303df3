import { searchPayloadDefaultValue } from '@consts/search';
import { format } from 'date-fns';
import { TFunction } from 'next-i18next';
import { Guest } from 'src/types/buy';
import {
  ISearchDTO,
  ISearchPayload,
  ISearchPayloadRooms,
  SearchUrlParams
} from 'src/types/search';
import { convertStringToUTCDate } from './date';

export const mountRoomDistributionStringFromObject = (
  distribution: ISearchPayloadRooms[]
) =>
  distribution.reduce((acc, room, index) => {
    return `${acc}${room.adults}${
      room.kids.length > 0 ? `-${room.kids.join('-')}` : ''
    }${index < distribution.length - 1 ? '!' : ''}`;
  }, '');

export const mountSearchUrlByPayload = (searchPayload: ISearchPayload) => {
  const roomsString = mountRoomDistributionStringFromObject(
    searchPayload.distribution
  );

  const { destination } = searchPayload;
  const queryString = `destination=${destination.display}&code=${
    destination.id
  }&group=${destination.group}&latlng=${destination?.location?.latitude}!${
    destination?.location?.longitude
  }&checkin=${format(
    convertStringToUTCDate(searchPayload.checkin!),
    'yyyy-MM-dd'
  )}&checkout=${format(
    convertStringToUTCDate(searchPayload.checkout!),
    'yyyy-MM-dd'
  )}&distribution=${roomsString}`;

  const queryObject = {
    destination: destination.display,
    code: destination.id,
    group: destination.group,
    latlng: `${destination?.location?.latitude}!${destination?.location?.longitude}`,
    checkin: format(
      convertStringToUTCDate(searchPayload.checkin!),
      'yyyy-MM-dd'
    ),
    checkout: format(
      convertStringToUTCDate(searchPayload.checkout!),
      'yyyy-MM-dd'
    ),
    distribution: roomsString
  };

  return [queryString, queryObject];
};

export const mountHotelUrlByPayload = (payload: ISearchDTO) => {
  const roomsString = mountRoomDistributionStringFromObject(
    payload.distribution
  );

  return `distribution=${roomsString}&checkin=${payload.checkin}&checkout=${payload.checkout}`;
};

export const mountRoomDistributionObjectFromString = (
  t: TFunction,
  distribution: string = '2'
) => {
  return distribution.split('!').map<ISearchPayloadRooms>(room => {
    const roomDistribution = room.split('-');
    return {
      adults: Number(roomDistribution.shift()),
      kids: roomDistribution.map(age => Number(age))
    };
  });
};

export const mountRoomDistributionStringFromString = (
  t: TFunction,
  distribution: string = '2'
) => {
  const distributionArray = distribution
    .split('!')
    .map<ISearchPayloadRooms>(room => {
      const roomDistribution = room.split('-');
      return {
        adults: Number(roomDistribution.shift()),
        kids: roomDistribution.map(age => Number(age))
      };
    });

  const totalRooms = distributionArray.length;
  const totalAdults = distributionArray.reduce(
    (sum, booking) => sum + booking.adults,
    0
  );
  const totalKids = distributionArray.reduce(
    (sum, booking) => sum + booking.kids.length,
    0
  );

  return t('search.rooms.roomsAdultsAndKids', {
    roomsCount: totalRooms,
    adultsCount: totalAdults,
    kidsCount: totalKids
  });
};

export const mountRoomDistributionStringFromDistribution = (
  t: TFunction,
  distributionArray: ISearchPayloadRooms[]
) => {
  const totalRooms = distributionArray.length;
  const totalAdults = distributionArray.reduce(
    (sum, booking) => sum + booking.adults,
    0
  );
  const totalKids = distributionArray.reduce(
    (sum, booking) => sum + booking.kids.length,
    0
  );

  return t('search.rooms.roomsAdultsAndKids', {
    roomsCount: totalRooms,
    adultsCount: totalAdults,
    kidsCount: totalKids
  });
};

export const mountRoomDistributionStringFromDistributionAndRoom = (
  t: TFunction,
  distribution: ISearchPayloadRooms[],
  room: { adults: number; kids: number }
) => {
  return t('search.rooms.roomsAdultsAndKids', {
    roomsCount: distribution.length,
    adultsCount: room.adults,
    kidsCount: room.kids
  });
};

export const mountRoomDistributionStringFromGuestsResponse = (
  t: TFunction,
  guests: Guest[]
) => {
  const adultsCount = guests.filter(guest => !guest.child).length;
  const childrenCount = guests.filter(guest => guest.child).length;

  return t('rooms.adultsAndKids', {
    adultsCount,
    kidsCount: childrenCount
  });
};

export const mountRoomDistributionStringFromRoomDistribution = (
  t: TFunction,
  distribution: ISearchPayloadRooms
) => {
  return t('search.rooms.adultsAndKids', {
    adultsCount: distribution.adults,
    kidsCount: distribution.kids.length
  });
};

export const mountPayloadBySearchUrl = ({
  t,
  destination,
  code,
  group,
  latlng,
  checkin,
  checkout,
  distribution,
  currency,
  lang
}: SearchUrlParams): ISearchPayload => {
  const [latitude, longitude] = latlng?.split('!') ?? [0, 0];

  return {
    ...searchPayloadDefaultValue,
    destination: {
      display: destination,
      group,
      id: code,
      location: {
        latitude: Number(latitude),
        longitude: Number(longitude)
      }
    },
    distribution: mountRoomDistributionObjectFromString(t, distribution),
    checkin,
    checkout,
    currency,
    lang
  };
};
