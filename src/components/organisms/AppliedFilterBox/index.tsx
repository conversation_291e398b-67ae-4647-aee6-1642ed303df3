import { useEffect, useState } from 'react';
import { useSearch } from 'src/context/SearchContext';
import {
  ISearchFilters,
  ISearchResponseFiltersApplied
} from 'src/types/search';
import { availability } from 'src/server/search';
import { Filters } from 'src/types/filters';
import { X } from '@phosphor-icons/react';
import Stars from '@components/atoms/Stars';

const AppliedFilterBox = () => {
  const {
    searchResponse,
    setSearchResponse,
    setLoading,
    searchPayload,
    setSearchPayload
  } = useSearch();
  const [appliedFilters, setAppliedFilters] = useState<
    ISearchResponseFiltersApplied[]
  >([]);

  useEffect(() => {
    setAppliedFilters(searchResponse.filtersApplied);
  }, [searchResponse.filtersApplied]);

  const handleSearch = async (newFilters: ISearchFilters) => {
    try {
      setLoading(true);
      const newParams = { ...searchPayload, filters: newFilters };
      const { data } = await availability(newParams);
      setSearchPayload(newParams);
      setSearchResponse(data);
    } catch (err) {
      //
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveFilter = (id: Filters, value: string) => {
    const updatedFilters = { ...searchPayload.filters };

    if (id === 'cancellationPolicyRefundable') {
      updatedFilters.cancellationPolicyRefundable = false;
    } else if (id === 'price' || id === 'hotelName') {
      updatedFilters[id] = null;
    } else {
      updatedFilters[id]?.splice(
        updatedFilters[id].findIndex(filterValue => value === filterValue),
        1
      );
    }

    handleSearch(updatedFilters);
  };

  return appliedFilters.length > 0 ? (
    <div className='w-full flex flex-col mt-4 gap-3'>
      <div className='flex flex-wrap gap-1'>
        {appliedFilters.map(filter => (
          <div
            key={filter.display}
            className='px-3 py-1 text-xs rounded-[8px] bg-white text-primary-900 flex items-center gap-1'
          >
            {filter.id === 'stars' ? (
              <Stars rate={parseFloat(filter.display)} />
            ) : (
              filter.display
            )}
            <X
              onClick={() =>
                handleRemoveFilter(filter.id as Filters, filter.value)
              }
              className='cursor-pointer transition-all hover:scale-[1.3]'
            />
          </div>
        ))}
      </div>
    </div>
  ) : null;
};

export default AppliedFilterBox;
