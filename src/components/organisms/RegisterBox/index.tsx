import Link from 'next/link';
import { useRouter } from 'next/router';
import { Checkbox, Input } from '@components/atoms/Input';
import Button from '@components/atoms/Button';
import { useForm } from 'react-hook-form';
import { useContext, useEffect, useState } from 'react';
import { IRegisterPayload } from 'src/types/customer';
import { toast } from 'react-toastify';
import { AxiosError } from 'axios';
import { DocumentTypeEnum } from 'src/types';
import { useHookFormMask } from 'use-mask-input';
import { UserPlus } from '@phosphor-icons/react';
import { deleteCookie, getCookie } from 'cookies-next';
import { AuthContext } from 'src/context/AuthContext';
import { isCNPJ, isCPF, isValidEmail } from 'src/utils/validation';
import { useTranslation } from 'next-i18next';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@components/atoms/Input/NewSelect';
import PhoneInput from '@components/atoms/Input/Phone';

const RegisterBox = () => {
  const router = useRouter();
  const { t, i18n } = useTranslation(['common', 'register'], {
    nsMode: 'fallback'
  });
  const { signUp } = useContext(AuthContext);
  const [isCpf, setIsCPF] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [terms, setTerms] = useState<boolean>(false);
  const {
    register: formRegister,
    handleSubmit,
    formState: { errors },
    setError,
    clearErrors,
    setFocus,
    setValue,
    watch
  } = useForm<IRegisterPayload>({
    defaultValues: {
      name: '',
      surname: '',
      documentNumber: '',
      documentType: i18n.language.toUpperCase() === 'PT_BR' ? 'CPF' : 'DN',
      email: '',
      password: '',
      phone: {
        ddi: i18n.language.toUpperCase() === 'PT_BR' ? '+55' : '',
        areaCode: '',
        number: ''
      }
    },
    mode: 'onBlur',
    reValidateMode: 'onBlur'
  });

  const registerWithMask = useHookFormMask(formRegister);
  const DDI = watch('phone.ddi');
  const watchDocumentType = watch('documentType');

  const onSubmit = async (payload: IRegisterPayload) => {
    try {
      if (!terms) {
        toast(t('needToAcceptTerms'), {
          type: 'error'
        });
        return;
      }

      if (payload.password !== payload.passwordAgain) {
        setError(
          'passwordAgain',
          {
            message: t('passwordsMustMatch')
          },
          { shouldFocus: true }
        );

        return;
      }

      setLoading(true);
      await signUp?.({
        ...payload,
        lang: i18n?.language.toUpperCase(),
        passwordAgain: undefined
      });

      toast(t('registrationSuccess'), { type: 'success' });
      const redirectUrl = getCookie('ral');
      deleteCookie('ral');

      router.push(redirectUrl || '/');
    } catch (e: unknown) {
      const error = e as AxiosError<any>;

      if (error.response?.status === 400) {
        toast(error.response?.data?.message || '', { type: 'error' });
      } else if (error.response?.status === 404) {
        toast(t('serverNotFound'), { type: 'error' });
      } else if (error.response?.status === 500) {
        toast(error.response?.data?.message || '', { type: 'error' });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleDocumentTypeChange = (value: string) => {
    setValue('documentType', value);
    clearErrors('documentNumber');
  };

  useEffect(() => {
    setIsCPF(watchDocumentType === DocumentTypeEnum.CPF);
  }, [watchDocumentType]);

  return (
    <form
      className='w-[100%] md:w-[40%] m-auto flex flex-col p-6 bg-white rounded-default'
      onSubmit={handleSubmit(data => onSubmit(data))}
    >
      <UserPlus size={38} weight='duotone' />
      <h2 className='text-lg font-medium mt-3 text-primary-900'>
        {t('createYourAccount')}
      </h2>
      <p className='flex gap-1 text-gray-500 text-sm'>
        {t('alreadyHaveAnAccountText')}{' '}
        <Link href={t('routes.signIn')} className='text-primary-900'>
          {t('signInLinkText')}
        </Link>
      </p>
      <div className='flex flex-col gap-2 my-5'>
        <div className='flex flex-col md:flex-row gap-3'>
          <Input
            placeholder={t('firstNameLabel')}
            {...formRegister('name', { required: t('fieldRequired') })}
            error={errors.name?.message}
            color='gray'
          />
          <Input
            placeholder={t('lastNameLabel')}
            {...formRegister('surname', { required: t('fieldRequired') })}
            error={errors.surname?.message}
            color='gray'
          />
        </div>
        <div className='flex gap-2'>
          {i18n.language.toUpperCase() === 'PT_BR' && (
            <Select onValueChange={handleDocumentTypeChange}>
              <SelectTrigger className='w-min'>
                <SelectValue placeholder={t('documentTypeLabel')} />
              </SelectTrigger>
              <SelectContent position='item-aligned'>
                <SelectItem
                  value={DocumentTypeEnum.CPF}
                  onClick={() => setIsCPF(true)}
                >
                  CPF
                </SelectItem>
                <SelectItem
                  value={DocumentTypeEnum.CNPJ}
                  onClick={() => setIsCPF(false)}
                >
                  CNPJ
                </SelectItem>
              </SelectContent>
            </Select>
          )}
          <Input
            placeholder={
              i18n.language.toUpperCase() === 'PT_BR'
                ? isCpf
                  ? 'CPF'
                  : 'CNPJ'
                : t('documentNumberLabel')
            }
            {...registerWithMask(
              'documentNumber',
              i18n.language.toUpperCase() === 'PT_BR'
                ? isCpf
                  ? 'cpf'
                  : '99.999.999/9999-99'
                : '',
              {
                required: t('fieldRequired'),
                validate: value =>
                  i18n.language.toUpperCase() === 'PT_BR'
                    ? isCpf
                      ? isCPF(value) || t('invalidCPF')
                      : isCNPJ(value) || t('invalidCNPJ')
                    : true
              }
            )}
            error={errors.documentNumber?.message}
            color='gray'
          />
        </div>
        <PhoneInput
          DDI={DDI}
          phoneDDIKey='phone.ddi'
          phoneAreaCodeKey='phone.areaCode'
          phoneNumberKey='phone.number'
          registerWithMask={registerWithMask}
          setFocus={setFocus}
          errors={errors}
        />
        <Input
          placeholder={t('emailLabel')}
          {...formRegister('email', {
            required: t('fieldRequired'),
            validate: value => isValidEmail(value) || t('invalidEmail')
          })}
          error={errors.email?.message}
          color='gray'
        />
        <div className='flex flex-col md:flex-row gap-3'>
          <Input
            placeholder={t('passwordLabel')}
            type='password'
            {...formRegister('password', { required: t('fieldRequired') })}
            error={errors.password?.message}
            color='gray'
          />
          <Input
            placeholder={t('passwordAgainLabel')}
            type='password'
            {...formRegister('passwordAgain', {
              required: t('fieldRequired')
            })}
            error={errors.passwordAgain?.message}
            color='gray'
          />
        </div>
        <Checkbox
          value={terms}
          onChange={e => setTerms(e)}
          label={
            <>
              {t('acceptTermsLabelStart')}
              <Link
                className='underline'
                href={t('routes.termsAndConditions')}
                target='_blank'
              >
                {t('termsLinkText')}
              </Link>
              {t('acceptTermsLabelMiddle')}
              <Link
                className='underline'
                href={t('routes.privacyPolicy')}
                target='_blank'
              >
                {t('privacyLinkText')}
              </Link>
            </>
          }
        />
      </div>
      <Button
        color='primary'
        size='large'
        fontWeight='bold'
        type='submit'
        loading={loading}
      >
        {t('registerButton')}
      </Button>
    </form>
  );
};

export default RegisterBox;
