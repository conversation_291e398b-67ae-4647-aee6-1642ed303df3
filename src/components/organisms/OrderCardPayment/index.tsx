import Image from 'next/image';
import { useState } from 'react';
import ExternalPaymentCard from '@components/molecules/ExternalPaymentCard';
import PaymentCard from '@components/molecules/PaymentCard';
import PixPaymentCard from '@components/molecules/PixPaymentCard';
import { CreditCard, HandCoins, PixLogo } from '@phosphor-icons/react';
import { useTranslation } from 'next-i18next';
import { PaymentMethodIdEnum } from 'src/types/checkout';
import { IOrderPayment } from 'src/types/order';

interface IOrderCardPayment extends IOrderPayment {
  totalValue: string;
  instalmentFormatted: string;
  orderStatus: string;
}

const OrderCardPayment = ({
  method,
  paymentResponse,
  paymentMethodId,
  installments,
  totalValue,
  instalmentFormatted,
  status
}: IOrderCardPayment) => {
  const { t } = useTranslation('bookings');
  const [isExternalPaymentOpen, setIsExternalPaymentOpen] =
    useState<boolean>(false);

  const paymentMethod = {
    PIX:
      status === 'CONFIRMED' || status === 'CANCELED' ? (
        <div className='bg-gray-100 px-4 py-3 rounded-button'>
          <PaymentCard
            icon={<PixLogo size={22} />}
            title={t('orderCardPayment.pix.title')}
            description={t('orderCardPayment.pix.description', {
              amount: totalValue
            })}
          />
        </div>
      ) : (
        <PixPaymentCard
          base64={paymentResponse.qrCodeBase64 || ''}
          qrcodeLink={paymentResponse.qrCode || ''}
          price={totalValue}
          secondsRemaining={paymentResponse.secondsRemaining}
          priceDescription={t('orderCardPayment.pix.priceDescription')}
        />
      ),
    CREDIT_CARD: (
      <div className='bg-gray-100 px-4 py-3 rounded-button'>
        <PaymentCard
          icon={
            PaymentMethodIdEnum[paymentMethodId] ? (
              <Image
                width={30}
                height={30}
                src={`https://ourtrips3.s3.amazonaws.com/images/credit_card_${paymentMethodId}.png`}
                alt={`Ícone da forma de pagamento ${paymentMethodId}`}
              />
            ) : (
              <CreditCard size={22} weight='duotone' />
            )
          }
          title={t('orderCardPayment.creditCard.title')}
          description={t('orderCardPayment.creditCard.description', {
            installments,
            amount: instalmentFormatted
          })}
        />
      </div>
    ),
    TRIPCASH: (
      <div className='bg-gray-100 px-4 py-3 rounded-button'>
        <PaymentCard
          icon={<HandCoins size={22} weight='duotone' />}
          title={t('orderCardPayment.tripcash.title')}
          description={t('orderCardPayment.tripcash.description')}
        />
      </div>
    ),
    EXTERNAL:
      status === 'CONFIRMED' ||
      status === 'CANCELED' ||
      status === 'PAYMENT_EXPIRED' ? (
        <div className='bg-gray-100 px-4 py-3 rounded-button'>
          <PaymentCard
            icon={<CreditCard size={22} />}
            title={t('orderCardPayment.external.title')}
            description={t('orderCardPayment.external.description', {
              amount: totalValue
            })}
            expired={status === 'PAYMENT_EXPIRED'}
          />
        </div>
      ) : (
        <ExternalPaymentCard
          iframeUrl={paymentResponse.iframe}
          price={totalValue}
          isPaymentOpen={isExternalPaymentOpen}
          setPaymentOpen={setIsExternalPaymentOpen}
          priceDescription={t('orderCardPayment.external.priceDescription')}
          secondsRemaining={paymentResponse.secondsRemaining}
          onTimeout={() => setIsExternalPaymentOpen(false)}
          status={status}
        />
      )
  };

  return paymentMethod[method];
};

export default OrderCardPayment;
