/* eslint-disable @next/next/no-img-element */
import Stars from '@components/atoms/Stars';
import { FC, useState } from 'react';
import Divider from '@components/atoms/Divider';
import { IBookingRoom, IBookingStatusEnum } from 'src/types/booking';
import ReservationCardRoom from '@components/molecules/ReservationCardRoom';
import Badge from '@components/atoms/Badge';
import { Room } from 'src/types/buy';
import { Bed, CaretDown, MapPin } from '@phosphor-icons/react';
import ImageCarousel from '@components/molecules/ImageCarousel';
import { useTranslation } from 'next-i18next';

interface IReservationCard {
  stars?: number;
  name: string;
  address: string;
  reservationCode?: string;
  distributionText?: string;
  totalPrice?: string;
  status?: IBookingStatusEnum;
  statusDisplay?: string;
  images?: string[];
  checkin: string;
  checkout: string;
  rooms: IBookingRoom[] | Room[];
  border?: boolean;
  expandable?: boolean;
}

const ReservationCard: FC<IReservationCard> = ({
  stars,
  name,
  images,
  address,
  reservationCode,
  distributionText,
  totalPrice,
  status,
  statusDisplay,
  checkin,
  checkout,
  rooms,
  border = true,
  expandable
}: IReservationCard) => {
  const { t } = useTranslation('bookings');

  const [expanded, setExpanded] = useState<boolean>(!expandable);
  const getStatusType = () => {
    switch (status) {
      case IBookingStatusEnum.CREATED:
        return 'info';
      case IBookingStatusEnum.BOOKING:
        return 'info';
      case IBookingStatusEnum.BOOKED:
        return 'success';
      case IBookingStatusEnum.FAILED:
        return 'danger';
      case IBookingStatusEnum.DISCONTINUED:
        return 'warning';
      case IBookingStatusEnum.PENDING_CANCEL:
        return 'warning';
      case IBookingStatusEnum.CANCELED:
        return 'danger';
      default:
        return 'info';
    }
  };

  const displayStatus =
    status !== IBookingStatusEnum.PENDING_CANCEL &&
    status !== IBookingStatusEnum.CANCELED;

  return (
    <>
      {expandable && (
        <div
          className='flex flex-col justify-between bg-white rounded-default'
          onClick={() => setExpanded(prev => !prev)}
        >
          <div className='flex flex-col md:flex-row justify-between md:items-center'>
            <div className='flex gap-3'>
              <div className='w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center'>
                <Bed size={18} />
              </div>
              <div className='flex flex-col justify-center'>
                <p className='text-gray-500 text-sm'>
                  {t('reservationCard.hotelReservation')}
                </p>
                <h3 className='font-semibold text-primary-900'>{name}</h3>
              </div>
            </div>
            <div className='flex items-center mt-4 md:mt-0 self-center md:self-auto flex-col-reverse md:flex-row gap-4'>
              <div className='flex items-center justify-between md:justify-start gap-4'>
                <div className='flex flex-col md:items-end md:text-right gap-1'>
                  <p className='text-gray-500 text-sm'>
                    {t('reservationCard.checkinDay')}
                  </p>
                  <h4 className='text-primary-900 text-sm font-medium'>
                    {checkin}
                  </h4>
                </div>
                <Divider />
                <div className='flex flex-col md:items-end md:text-right gap-1'>
                  <p className='text-gray-500 text-sm'>
                    {t('reservationCard.checkoutDay')}
                  </p>
                  <h4 className='text-primary-900 text-sm font-medium'>
                    {checkout}
                  </h4>
                </div>
              </div>
              {reservationCode && displayStatus ? (
                <div className='bg-gray-100 p-2 rounded-button w-full md:w-auto'>
                  <div className='flex flex-col items-start md:items-end text-right gap-1'>
                    <p className='text-gray-500 text-sm'>
                      {t('reservationCard.reservationCode')}
                    </p>
                    <h4 className='text-primary-900 text-sm font-medium'>
                      {reservationCode}
                    </h4>
                  </div>
                </div>
              ) : null}
            </div>
          </div>
          <div className='flex flex-col justify-center items-center mt-4 bg-gray-100 rounded-button pt-2 pb-1 cursor-pointer'>
            <p className='text-gray-500 text-sm'>
              {t(
                `reservationCard.${
                  expanded ? 'clickToSeeLess' : 'clickToSeeMore'
                }`
              )}
            </p>
            <CaretDown size={14} />
          </div>
        </div>
      )}
      {expanded && (
        <div className='flex flex-col gap-6 overflow-hidden'>
          <div className={`${border ? 'p-6 mt-4' : ''}`}>
            <div className='flex flex-col gap-6'>
              <div className='flex flex-col-reverse md:flex-row gap-4 md:gap-3'>
                <div className='flex gap-4 md:gap-6'>
                  <ImageCarousel
                    images={images || []}
                    className='w-[100px] md:w-[200px] rounded-button overflow-hidden'
                  />
                  <div className='flex flex-col justify-between items-start gap-2 flex-1 md:flex-4'>
                    <div className='w-full flex flex-row-reverse justify-between items-start md:flex-col'>
                      {stars ? <Stars rate={stars} /> : null}
                      <div className='flex flex-col'>
                        <h3 className='text-primary-900 text-base md:text-lg font-semibold'>
                          {name}
                        </h3>
                        <div className='flex items-center gap-1'>
                          <MapPin className='text-primary-900' />
                          <p className='text-gray-500 text-sm'>{address}</p>
                        </div>
                      </div>
                    </div>
                    <div className='hidden md:flex flex-col gap-1 mt-3'>
                      <div className='flex flex-col flex-1 whitespace-nowrap'>
                        <p className='text-gray-500 text-sm'>
                          {t('reservationCard.checkinDay')}
                        </p>
                        <h4 className='text-primary-900 font-medium'>
                          {checkin}
                        </h4>
                      </div>
                      <div className='flex flex-col flex-1 whitespace-nowrap'>
                        <p className='text-gray-500 text-sm'>
                          {t('reservationCard.checkoutDay')}
                        </p>
                        <h4 className='text-primary-900 font-medium'>
                          {checkout}
                        </h4>
                      </div>
                    </div>
                  </div>
                </div>
                {(statusDisplay ||
                  distributionText ||
                  totalPrice ||
                  reservationCode) && (
                  <div className='flex flex-row-reverse md:flex-col md:items-end justify-between flex-1'>
                    <div className='flex flex-col items-start md:items-end justify-center md:mb-2'>
                      {statusDisplay ? (
                        <Badge type={getStatusType()}>{statusDisplay}</Badge>
                      ) : null}
                      {distributionText && (
                        <p className='text-sm text-gray-500 mt-3 hidden md:block'>
                          {distributionText}
                        </p>
                      )}
                      {totalPrice && (
                        <h4 className='text-primary-900 text-xl font-semibold hidden md:block'>
                          {totalPrice}
                        </h4>
                      )}
                    </div>
                    {reservationCode && displayStatus && (
                      <div className='flex flex-col items-start md:items-end text-right'>
                        <p className='text-gray-500 text-sm'>
                          {t('reservationCard.reservationCode')}
                        </p>
                        <h4 className='text-primary-900 text-sm font-medium'>
                          {reservationCode}
                        </h4>
                      </div>
                    )}
                  </div>
                )}
              </div>
              <div className='flex md:hidden items-center gap-4'>
                <div className='flex flex-col flex-1'>
                  <p className='text-gray-500 text-xs'>
                    {t('reservationCard.checkinDay')}
                  </p>
                  <h4 className='text-primary-900 text-sm font-medium'>
                    {checkin}
                  </h4>
                </div>
                <Divider />
                <div className='flex flex-col flex-1'>
                  <p className='text-gray-500 text-xs'>
                    {t('reservationCard.checkoutDay')}
                  </p>
                  <h4 className='text-primary-900 text-sm font-medium'>
                    {checkout}
                  </h4>
                </div>
              </div>
              <div className='flex flex-col gap-1'>
                <div className='flex flex-col gap-2'>
                  {rooms.map(room => (
                    <ReservationCardRoom
                      key={room.accommodationId}
                      room={room}
                    />
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ReservationCard;
