import { useCallback, useEffect, useRef, useState } from 'react';
import CheckboxFilter from '@components/molecules/Filters/Checkbox';
import Divider from '@components/atoms/Divider';
import RangeFilter from '@components/molecules/Filters/Range';
import { useSearch } from 'src/context/SearchContext';
import {
  ISearchFilters,
  ISearchPayload,
  ISearchResponseMainFilters
} from 'src/types/search';
import { availability } from 'src/server/search';
import { CheckboxFilters } from 'src/types/filters';
import HotelFilter from '@components/molecules/Filters/Hotel';
import Skeleton from 'react-loading-skeleton';
import useBlockScroll from 'src/hooks/useBlockScroll';
import useOnClickOutside from 'src/hooks/useClickOutside';

interface IFilterBox {
  pageLoading?: boolean;
}

const FilterBox = ({ pageLoading }: IFilterBox) => {
  const {
    searchResponse,
    setSearchResponse,
    loading,
    setLoading,
    searchPayload,
    setSearchPayload
  } = useSearch();
  const filterRef = useRef(null);
  const [filterOpen, setFilterOpen] = useState<boolean>(false);
  const [rangeValue, setRangeValue] = useState<number[]>(
    searchResponse.filtersMain.find(filter => filter.id === 'price')?.applied
      ? [
          searchResponse.filtersMain.find(filter => filter.id === 'price')
            ?.appliedMin || 0,
          searchResponse.filtersMain.find(filter => filter.id === 'price')
            ?.appliedMax || 0
        ]
      : [
          searchResponse.filtersMain.find(filter => filter.id === 'price')
            ?.min || 0,
          searchResponse.filtersMain.find(filter => filter.id === 'price')
            ?.max || 0
        ]
  );
  const [responseFilters, setResponseFilters] = useState<
    ISearchResponseMainFilters[]
  >(searchResponse.filtersMain);

  useBlockScroll(filterOpen);
  useOnClickOutside(filterRef, () => {
    setFilterOpen(false);
  });

  useEffect(() => {
    setResponseFilters(searchResponse.filtersMain);
  }, [searchResponse.filtersMain]);

  const handleSearch = useCallback(
    async (payload: ISearchPayload) => {
      try {
        setLoading(true);
        const { data } = await availability(payload);

        setSearchResponse(data);
        setResponseFilters(data.filtersMain);
      } catch (err) {
        //
      } finally {
        setLoading(false);
      }
    },
    [setLoading, setSearchResponse]
  );

  const handleRangeFilter = (range: number[] = [0, 0]) => {
    const rangePayload = range ? `${range[0]}-${range[1]}` : '';
    const newFilters = {
      ...searchPayload.filters,
      price: rangePayload
    } as ISearchFilters;
    const newPaylaod = { ...searchPayload, filters: newFilters };
    handleSearch(newPaylaod);
    setSearchPayload(newPaylaod);
    setRangeValue(range);
  };

  const handleCheckboxFilter = (
    id: CheckboxFilters,
    value: string,
    apply: boolean
  ) => {
    const newFilters = { ...searchPayload.filters };
    if (id === 'cancellationPolicyRefundable') {
      newFilters[id] = apply;
    } else if (apply) {
      newFilters[id]?.push(value);
    } else {
      newFilters[id]?.splice(
        newFilters[id].findIndex(filterValue => value === filterValue),
        1
      );
    }
    const newPaylaod = { ...searchPayload, filters: newFilters };
    handleSearch(newPaylaod);
    setSearchPayload(newPaylaod);
  };

  const handleHotelNameFilter = (hotelName: string) => {
    const newFilters = { ...searchPayload.filters, hotelName };
    const newPaylaod = { ...searchPayload, filters: newFilters };
    handleSearch(newPaylaod);
    setSearchPayload(newPaylaod);
  };

  useEffect(() => {
    if (!responseFilters.find(filter => filter.id === 'price')?.applied) {
      setRangeValue([
        searchResponse.filtersMain.find(filter => filter.id === 'price')?.min ||
          0,
        searchResponse.filtersMain.find(filter => filter.id === 'price')?.max ||
          0
      ]);
    }
  }, [responseFilters, searchResponse.filtersMain]);

  return (
    <div className='w-full'>
      {!pageLoading ? (
        <div className='w-full flex-col gap-6'>
          <HotelFilter loading={loading} applyFilter={handleHotelNameFilter} />
          <Divider orientation='horizontal' />
          {responseFilters.map((filter, index) => (
            <div
              key={`${filter.type}-${filter.id}`}
              className='flex flex-col gap-6'
            >
              {filter.type === 'checkbox' &&
              (filter.options?.length || 0) > 0 ? (
                <CheckboxFilter
                  id={filter.id as CheckboxFilters}
                  display={filter.display}
                  applied={filter.applied}
                  options={filter.options}
                  onChange={handleCheckboxFilter}
                  step={10}
                />
              ) : null}{' '}
              {filter.type === 'range' ? (
                <RangeFilter
                  id={filter.id}
                  disabled={loading}
                  display={filter.display}
                  min={filter.min}
                  max={filter.max}
                  initialValues={rangeValue}
                  applied={filter.applied}
                  onChange={handleRangeFilter}
                />
              ) : null}
              {responseFilters.length > 1 &&
                index < responseFilters.length - 1 && (
                  <Divider orientation='horizontal' />
                )}
            </div>
          ))}
        </div>
      ) : (
        <Skeleton count={10} />
      )}
    </div>
  );
};

export default FilterBox;
