/* eslint-disable @next/next/no-img-element */
import { FC, useState } from 'react';
import { IHotelPhoto } from 'src/types/hotel';
import { AnimatePresence, MotionConfig, motion } from 'framer-motion';
import { range, variants } from 'src/utils/gallery';
import { CaretLeft, CaretRight, X } from '@phosphor-icons/react';

interface IGallery {
  open: boolean;
  images?: IHotelPhoto[];
  onClose: Function;
}

const Gallery: FC<IGallery> = ({ open, images, onClose }: IGallery) => {
  const [currentImage, setCurrentImage] = useState<IHotelPhoto>(images![0]);
  const [index, setIndex] = useState<number>(0);
  const [direction, setDirection] = useState(1);

  const handleClose = (e: any) => {
    e.stopPropagation();
    onClose?.();
  };

  const getImageIndex = (image: IHotelPhoto) => {
    return images?.findIndex(img => image.url === img.url) || 0;
  };

  const bottomNavigationImages = images?.filter((_, i) =>
    range(index - 15, index + 15).includes(i)
  );

  const handleSelectImage = (image: IHotelPhoto) => {
    if (getImageIndex(image) < index) setDirection(-1);
    else setDirection(1);

    setCurrentImage(image);
    setIndex(getImageIndex(image));
  };

  const handlePreviousImage = () => {
    if (index - 1 >= 0) {
      const prevImage = images?.at(index - 1) || currentImage;
      handleSelectImage(prevImage);
    }
  };

  const handleNextImage = () => {
    if (index + 1 <= (images?.length || 0) - 1) {
      const nextImage = images?.at(index + 1) || currentImage;
      handleSelectImage(nextImage);
    }
  };

  return open ? (
    <MotionConfig
      transition={{
        x: { type: 'spring', stiffness: 300, damping: 30 },
        opacity: { duration: 0.2 }
      }}
    >
      <div className='w-screen h-screen fixed top-0 left-0 backdrop-blur-md bg-black/80 z-[1000000] flex items-center justify-center flex-col overflow-hidden'>
        <div className='z-[10000] w-full h-[60%] md:h-[60%] absolute top-0 left-0 flex flex-col justify-between p-6 md:p-12'>
          <div
            className='w-8 h-8 bg-primary-100 rounded-default flex items-center justify-center cursor-pointer'
            onClick={e => handleClose(e)}
          >
            <X />
          </div>
          <div className='w-full flex justify-between items-center'>
            <div
              className='w-9 h-9 md:w-12 md:h-12 bg-primary-500 rounded-default flex items-center justify-center cursor-pointer text-white'
              onClick={() => handlePreviousImage()}
            >
              <CaretLeft size={24} weight='bold' className='w-4 md:w-auto' />
            </div>
            <div
              className='w-9 h-9 md:w-12 md:h-12 bg-primary-500 rounded-default flex items-center justify-center cursor-pointer text-white'
              onClick={() => handleNextImage()}
            >
              <CaretRight size={24} weight='bold' className='w-4 md:w-auto' />
            </div>
          </div>
        </div>
        <div className='w-full h-full flex items-center justify-center relative aspect-[3/2]'>
          <AnimatePresence initial={false} custom={direction}>
            <motion.div
              key={index}
              custom={direction}
              variants={variants}
              initial='enter'
              animate='center'
              exit='exit'
              className='absolute'
            >
              <img
                src={currentImage?.url || ''}
                alt='Imagem de destaque na tela'
                className='object-contain md:w-auto w-full'
              />
            </motion.div>
          </AnimatePresence>
        </div>
        <div className='w-full fixed overflow-hidden bottom-0'>
          <motion.div
            className='flex aspect-[3/2] h-14 mx-auto my-8'
            initial={false}
          >
            <AnimatePresence initial={false}>
              {bottomNavigationImages?.map(image => (
                <motion.button
                  key={image.url}
                  onClick={() => handleSelectImage(image)}
                  initial={{
                    width: '0%',
                    x: `${Math.max((index - 1) * -100, 15 * -100)}%`
                  }}
                  animate={{
                    scale: image.url === currentImage.url ? 1.5 : 1,
                    width: '100%',
                    x: `${Math.max(index * -100, 15 * -100)}%`
                  }}
                  exit={{ width: '0%' }}
                  className={`w-full flex flex-shrink-0 relative border-none overflow-hidden cursor-pointer bg-transparent ${
                    getImageIndex(image) === index
                      ? 'rounded-default overflow-hidden z-[1000]'
                      : ''
                  }`}
                >
                  <img
                    alt={`Imagem pequena do hotel na parte inferior da tela ${
                      index + 1
                    }`}
                    width={100}
                    height={80}
                    src={image.url || ''}
                    className='w-full h-full block object-cover transition-all z-[1000]'
                  />
                </motion.button>
              ))}
            </AnimatePresence>
          </motion.div>
        </div>
      </div>
    </MotionConfig>
  ) : null;
};

export default Gallery;
