import { FC } from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'next-i18next';
import SearchBox from '@components/organisms/SearchBox';

interface IHeader {}

const Header: FC<IHeader> = () => {
  const { t } = useTranslation('home');

  return (
    <div className='relative flex w-full h-auto py-20 md:py-0 md:h-[80vh] items-center justify-center bg-primary-900'>
      <div
        className='absolute top-0 w-full h-full bg-center bg-no-repeat bg-cover opacity-50'
        style={{ backgroundImage: "url('/images/bg_2.png')" }}
      />
      <div className='container relative flex flex-col items-start justify-start mt-[35px] md:items-center md:mt-0'>
        <motion.h2
          initial={{ opacity: 0, y: 15 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className='mb-4 text-4xl font-semibold text-white lg:text-5xl'
        >
          {t('header.title')}
        </motion.h2>
        <motion.p
          initial={{ opacity: 0, y: 15 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.5 }}
          className='mb-6 text-base text-white'
        >
          {t('header.subtitle')}
        </motion.p>
        <motion.div
          initial={{ opacity: 0, y: 15 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6, duration: 0.5 }}
          className='w-full lg:w-[85%]'
        >
          <SearchBox />
        </motion.div>
      </div>
      <div className='absolute bottom-[-1px] w-full h-4 bg-gray-100 rounded-t-default' />
    </div>
  );
};

export default Header;
