import Link from 'next/link';
import { Input } from '@components/atoms/Input';
import Button from '@components/atoms/Button';
import { ILoginPayload } from 'src/types/auth';
import { ReactNode, useContext, useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { useForm } from 'react-hook-form';
import { useRouter } from 'next/router';
import { AuthContext } from 'src/context/AuthContext';
import { UserCircle } from '@phosphor-icons/react';
import { useTranslation } from 'next-i18next';

interface ILoginBox {
  email?: string;
  expanded?: boolean;
  onLogin?: Function;
  title?: ReactNode;
  description?: ReactNode;
  footer?: ReactNode;
}

const LoginBox = ({
  email,
  expanded = false,
  onLogin,
  title,
  description,
  footer
}: ILoginBox) => {
  const router = useRouter();
  const auth = useContext(AuthContext);
  const { t } = useTranslation(['common', 'login'], { nsMode: 'fallback' });
  const { referrer } = router.query;

  const [loading, setLoading] = useState<boolean>(false);
  const {
    register,
    handleSubmit,
    formState: { errors },
    setFocus
  } = useForm<ILoginPayload>({
    defaultValues: {
      username: email || '',
      password: ''
    },
    mode: 'onBlur',
    reValidateMode: 'onBlur'
  });

  const onSubmit = async (payload: ILoginPayload) => {
    try {
      setLoading(true);
      await auth?.signIn?.(payload);

      if (onLogin) {
        onLogin();
      } else {
        router.push((referrer as string) || '/');
      }
    } catch (e: unknown) {
      toast(t('errors.incorrectPassword'), {
        type: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (email) {
      setFocus('password');
    }
  }, [email, setFocus]);

  return (
    <form
      className={`${
        expanded ? 'w-full md:w-[90%]' : 'w-[100%] md:w-[35%]'
      } m-auto flex flex-col p-6 bg-white rounded-default`}
      onSubmit={handleSubmit(data => onSubmit(data))}
    >
      <UserCircle size={38} className='text-primary-900' />
      {title || (
        <h2 className='text-lg font-medium mt-3 text-primary-900'>
          {t('welcome')}
        </h2>
      )}
      {description || (
        <span className='flex gap-1 text-gray-500 text-sm'>
          {t('dontHaveAccount')}{' '}
          <Link href={t('routes.signUp')} className='text-primary-900'>
            {t('createAccount')}
          </Link>
        </span>
      )}
      <div className='flex flex-col gap-2 mt-5 mb-2'>
        <Input
          placeholder={t('email')}
          {...register('username', { required: t('errors.required') })}
          color='gray'
          error={errors.username?.message}
          focus={() => setFocus('username')}
          disabled={Boolean(email)}
        />
        <Input
          placeholder={t('password')}
          {...register('password', { required: t('errors.required') })}
          color='gray'
          error={errors.password?.message}
          focus={() => setFocus('password')}
          type='password'
        />
      </div>
      <Link
        className='underline'
        href={t('routes.passwordRecovery')}
        target={expanded ? '_blank' : ''}
      >
        {t('forgotPassword')}
      </Link>
      <Button
        color='primary'
        size='large'
        fontWeight='bold'
        disabled={loading}
        loading={loading}
        className='mt-5'
        type='submit'
      >
        {t('enter')}
      </Button>
      <div className='flex flex-col mt-2'>{footer}</div>
    </form>
  );
};

export default LoginBox;
