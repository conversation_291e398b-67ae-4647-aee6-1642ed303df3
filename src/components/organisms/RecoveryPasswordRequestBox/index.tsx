import { Input } from '@components/atoms/Input';
import Button from '@components/atoms/Button';
import { IRecoveryPasswordRequestPayload } from 'src/types/auth';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { useForm } from 'react-hook-form';
import { Password, WarningCircle } from '@phosphor-icons/react';
import { AxiosError } from 'axios';
import { recoveryPasswordRequest } from 'src/server/auth';
import { useTranslation } from 'next-i18next';

const RecoveryPasswordRequestBox = () => {
  const { t, i18n } = useTranslation('password-recovery');
  const [loading, setLoading] = useState<boolean>(false);
  const [sentTimes, setSentTimes] = useState<number>(0);
  const [countdown, setCountdown] = useState<number>(0);
  const {
    register,
    handleSubmit,
    formState: { errors },
    setFocus
  } = useForm<IRecoveryPasswordRequestPayload>({
    defaultValues: {
      email: ''
    },
    mode: 'onBlur',
    reValidateMode: 'onBlur'
  });

  const onSubmit = async (payload: IRecoveryPasswordRequestPayload) => {
    try {
      setLoading(true);
      await recoveryPasswordRequest?.({
        ...payload,
        lang: i18n.language.toUpperCase()
      });

      toast(t('request.successMessage'), {
        type: 'success'
      });

      setCountdown(30);
      setSentTimes(prev => prev + 1);
    } catch (e: unknown) {
      const error = e as AxiosError<any>;

      if (error.response?.status === 400) {
        toast(error.response?.data?.message || t('request.error400'), {
          type: 'error'
        });
      } else if (error.response?.status === 404) {
        toast(t('request.error404'), { type: 'error' });
      } else if (error.response?.status === 500) {
        toast(error.response?.data?.message || t('request.error500'), {
          type: 'error'
        });
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const timer = setInterval(() => {
      if (countdown > 0) setCountdown(prev => prev - 1);
    }, 1000);

    return () => clearInterval(timer);
  }, [countdown]);

  return (
    <form
      className='w-[90%] md:w-[35%] m-auto flex flex-col p-6 bg-white rounded-default gap-1'
      onSubmit={handleSubmit(data => onSubmit(data))}
    >
      <Password size={38} weight='duotone' />
      <h2 className='font-medium mt-3 text-primary-900'>
        {t('request.title')}
      </h2>
      <p className='flex gap-1 text-gray-500 text-sm'>
        {t('request.description')}
      </p>
      <div className='flex flex-col gap-2 my-5'>
        <Input
          placeholder={t('request.emailField')}
          {...register('email', { required: t('request.required') })}
          error={errors.email?.message}
          focus={() => setFocus('email')}
          type='email'
          color='gray'
        />
      </div>
      {sentTimes > 3 ? (
        <div className='flex gap-1 mb-1'>
          <WarningCircle size={20} className='flex-none text-red-600 mt-0.5' />
          <p className='text-sm text-red-600'>{t('request.spanWarning')}</p>
        </div>
      ) : null}
      <Button
        color='primary'
        size='large'
        fontWeight='bold'
        loading={loading}
        disabled={Boolean(countdown)}
        type='submit'
      >
        {!countdown
          ? t('request.send')
          : t('request.sendAgainIn', { countdown })}
      </Button>
    </form>
  );
};

export default RecoveryPasswordRequestBox;
