/* eslint-disable no-shadow */
/* eslint-disable no-unused-vars */
import { FC, useState } from 'react';
import { useSearch } from 'src/context/SearchContext';
import {
  mountRoomDistributionStringFromDistribution,
  mountSearchUrlByPayload
} from 'src/utils/search';
import { useRouter } from 'next/router';
import {
  IDestination,
  ISearchPayload,
  ISearchPayloadRooms
} from 'src/types/search';
import { format } from 'date-fns';
import { useTranslation } from 'next-i18next';
import { MagnifyingGlass } from '@phosphor-icons/react';
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTrigger
} from '@components/atoms/Sheet';
import { convertStringToUTCDate, getDateFNSLocale } from 'src/utils/date';
import SearchInputGroup from '@components/molecules/SearchInputGroup';
import { DatesType } from '@components/molecules/DateSearch';

interface ISearch {
  searchFunction?: (payload: ISearchPayload) => void;
}

const Search: FC<ISearch> = ({ searchFunction }: ISearch) => {
  const { searchPayload, setSearchPayload, loading } = useSearch();

  const router = useRouter();
  const { t, i18n } = useTranslation(['search', 'common'], {
    nsMode: 'fallback'
  });
  const [overlayOpen, setOverlayOpen] = useState<boolean>(false);

  const setDestination = (destination: IDestination) => {
    setSearchPayload(prevState => {
      return { ...prevState, destination };
    });
  };

  const setDates = (dates: DatesType) => {
    const [checkinDate, checkoutDate] = dates;
    const newCheckin = checkinDate ? format(checkinDate, 'yyyy-MM-dd') : null;
    const newCheckout = checkoutDate
      ? format(checkoutDate, 'yyyy-MM-dd')
      : null;

    setSearchPayload(prevState => {
      return {
        ...prevState,
        checkin: newCheckin,
        checkout: newCheckout
      };
    });
  };

  const setDistribution = (distribution: ISearchPayloadRooms[]) => {
    setSearchPayload(prevState => {
      return { ...prevState, distribution };
    });
  };

  const handleSubmit = () => {
    if (searchFunction) {
      searchFunction(searchPayload);
    }

    const [url] = mountSearchUrlByPayload(searchPayload);
    router.push(
      {
        pathname: t('routes.search'),
        query:
          url +
          (router.query.utm_source
            ? `&${new URLSearchParams(router.query as any).toString()}`
            : '')
      },
      '',
      {
        locale: i18n.language
      }
    );

    setOverlayOpen(false);
  };

  const { checkin, checkout, destination, distribution } = searchPayload;
  const disableSearch =
    !checkin ||
    !checkout ||
    distribution.length === 0 ||
    !destination.id ||
    !destination.display ||
    loading;

  return (
    <>
      <div className='w-full hidden md:flex flex-col md:flex-row items-stretch bg-white rounded-default p-4 z-10 md:items-center'>
        <SearchInputGroup
          destination={destination}
          setDestination={setDestination}
          checkin={checkin}
          checkout={checkout}
          setDates={setDates}
          distribution={distribution}
          setDistribution={setDistribution}
          handleSubmit={handleSubmit}
          disableSearch={disableSearch}
        />
      </div>
      <div className='w-full flex md:hidden'>
        <Sheet open={overlayOpen} onOpenChange={setOverlayOpen}>
          <SheetTrigger className='w-full'>
            <div className='w-full flex justify-between bg-white text-gray-500 rounded-default pt-2 pr-2 pb-2 pl-4 h-12 z-10 overflow-hidden text-ellipsis'>
              <div className='w-full flex flex-none items-center gap-2'>
                <MagnifyingGlass size={18} />
                <p className='text-sm text-ellipsis line-clamp-1 text-start'>
                  {destination && checkin && checkout && distribution
                    ? `${destination?.display?.split(',')[0]}, ${format(
                        convertStringToUTCDate(checkin),
                        'dd MMM',
                        {
                          locale: getDateFNSLocale(i18n.language)
                        }
                      )} - ${format(
                        convertStringToUTCDate(checkout),
                        'dd MMM',
                        {
                          locale: getDateFNSLocale(i18n.language)
                        }
                      )}, ${mountRoomDistributionStringFromDistribution(
                        t,
                        distribution
                      )}`
                    : t('search.inputPlaceholder')}
                </p>
              </div>
            </div>
          </SheetTrigger>
          <SheetContent side='bottom' className='rounded-t-default'>
            <SheetHeader className='text-start'>
              <SearchInputGroup
                destination={destination}
                setDestination={setDestination}
                checkin={checkin}
                checkout={checkout}
                setDates={setDates}
                distribution={distribution}
                setDistribution={setDistribution}
                handleSubmit={handleSubmit}
                disableSearch={disableSearch}
              />
            </SheetHeader>
          </SheetContent>
        </Sheet>
      </div>
    </>
  );
};

export default Search;
