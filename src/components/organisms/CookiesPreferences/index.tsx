import { useEffect, useState } from 'react';
import Button from '@components/atoms/Button';
import { getCookie, setCookie } from 'cookies-next';

import CookiePreferencesModal from '@components/molecules/CookiePreferencesModal';
import { COOKIE_PREFERENCE, defaultAcceptedCookies } from '@consts/cookie';

import { useTranslation } from 'next-i18next';

const CookiesPreferences = () => {
  const { t } = useTranslation('common');
  const [displayPreferences, setDisplayPreferences] = useState(false);
  const [preferencesModalOpen, setPreferencesModalOpen] = useState(false);
  const handleAcceptCookies = (cookies?: string) => {
    setCookie(COOKIE_PREFERENCE, cookies ?? defaultAcceptedCookies);
    setDisplayPreferences(false);
  };

  useEffect(() => {
    const preference = !getCookie(COOKIE_PREFERENCE);
    setDisplayPreferences(preference);
  }, []);

  return displayPreferences ? (
    <div className='sticky bottom-5 w-full px-6 md:px-[50px] z-[999999]'>
      <div className='bg-white rounded-default shadow-lg w-full p-6 flex gap-3 flex-col xl:flex-row'>
        <div>
          <h3 className='text-primary-900 font-medium text-base mb-1'>
            {t('cookies.title')}
          </h3>
          <p className='text-gray-500 font-normal text-sm md:line-clamp-2'>
            {t('cookies.description')}
          </p>
        </div>
        <div className='flex flex-col-reverse md:flex-row items-center gap-3'>
          <Button
            variant='outline'
            className='w-full md:w-auto'
            onClick={() => setPreferencesModalOpen(true)}
          >
            {t('cookies.myPreferences')}
          </Button>
          <Button
            className='w-full md:w-auto'
            onClick={() => handleAcceptCookies()}
          >
            {t('cookies.accept')}
          </Button>
        </div>
      </div>
      <CookiePreferencesModal
        onClose={() => setPreferencesModalOpen(false)}
        savePreferences={cookie => handleAcceptCookies(cookie)}
        open={preferencesModalOpen}
      />
    </div>
  ) : null;
};

export default CookiesPreferences;
