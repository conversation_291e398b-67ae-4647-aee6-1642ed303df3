import { Sheet, SheetContent, SheetHeader } from '@components/atoms/Sheet';
import {
  House,
  MagnifyingGlass,
  Sidebar as SidebarIcon,
  User
} from '@phosphor-icons/react';
import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import { useState } from 'react';
import SearchInputGroup from '@components/molecules/SearchInputGroup';
import { useSearch } from 'src/context/SearchContext';
import { IDestination, ISearchPayloadRooms } from 'src/types/search';
import { DatesType } from '@components/molecules/DateSearch';
import Sidebar from '@components/molecules/Sidebar';
import { format } from 'date-fns';
import { mountSearchUrlByPayload } from '@utils/search';

const BottomNavigationBar = () => {
  const router = useRouter();
  const { t, i18n } = useTranslation(['search', 'common'], {
    nsMode: 'fallback'
  });
  const [overlayOpen, setOverlayOpen] = useState<boolean>(false);
  const [sidebarOpen, setSidebarOpen] = useState<boolean>(false);
  const { searchPayload, setSearchPayload, loading } = useSearch();

  const setDestination = (destination: IDestination) => {
    setSearchPayload(prevState => {
      return { ...prevState, destination };
    });
  };

  const setDates = (dates: DatesType) => {
    const [checkinDate, checkoutDate] = dates;
    const newCheckin = checkinDate
      ? format(checkinDate! ?? '', 'yyyy-MM-dd')
      : '';
    const newCheckout = checkoutDate
      ? format(checkoutDate! ?? '', 'yyyy-MM-dd')
      : '';

    setSearchPayload(prevState => {
      return { ...prevState, checkin: newCheckin, checkout: newCheckout };
    });
  };

  const setDistribution = (distribution: ISearchPayloadRooms[]) => {
    setSearchPayload(prevState => {
      return { ...prevState, distribution };
    });
  };

  const handleSubmit = () => {
    const [url] = mountSearchUrlByPayload(searchPayload);
    router.push(
      {
        pathname: t('routes.search'),
        query:
          url +
          (router.query.utm_source
            ? `&${new URLSearchParams(router.query as any).toString()}`
            : '')
      },
      '',
      {
        locale: i18n.language
      }
    );

    setOverlayOpen(false);
  };

  const handleSearchClick = () => {
    if (
      searchPayload.destination.id &&
      searchPayload.checkin &&
      searchPayload.checkout
    ) {
      handleSubmit();
      return;
    }

    setOverlayOpen(true);
  };

  const { checkin, checkout, destination, distribution } = searchPayload;
  const disableSearch =
    !checkin ||
    !checkout ||
    distribution.length === 0 ||
    !destination.id ||
    !destination.display ||
    loading;

  return (
    <>
      <div className='w-full flex items-center justify-around fixed bottom-0 right-0 bg-white px-5 h-[75px] md:hidden shadow-2xl rounded-t-2xl z-10'>
        <div
          className={`flex items-center justify-center p-2 rounded-full ${
            router.asPath === '/' ? 'bg-primary-100' : 'bg-transparent'
          }`}
          onClick={() => router.push('/', '', { locale: i18n.language })}
        >
          <House size={24} />
        </div>
        <div
          className={`flex items-center justify-center p-2 rounded-full ${
            router.asPath.split('?')[0] === t('routes.search')
              ? 'bg-primary-100'
              : 'bg-transparent'
          }`}
          onClick={() => handleSearchClick()}
        >
          <MagnifyingGlass size={24} />
        </div>
        <div>
          <img
            src='https://ourtrips3.s3.amazonaws.com/new-logos/icon_blue.png'
            className='w-8'
            alt='OurTrip Logo'
          />
        </div>
        <div
          className={`flex items-center justify-center p-2 rounded-full ${
            router.asPath === t('routes.myProfile') ||
            router.asPath === t('routes.myReservations') ||
            router.asPath === t('routes.myTripcash')
              ? 'bg-primary-100'
              : 'bg-transparent'
          }`}
          onClick={() =>
            router.push(t('routes.myProfile', '', { locale: i18n.language }))
          }
        >
          <User size={24} />
        </div>
        <div
          className='flex items-center justify-center p-2 rounded-full bg-transparent'
          onClick={() => setSidebarOpen(true)}
        >
          <SidebarIcon size={24} mirrored />
        </div>
      </div>
      <Sheet open={overlayOpen} onOpenChange={setOverlayOpen}>
        <SheetContent side='bottom' className='rounded-t-default'>
          <SheetHeader className='text-start'>
            <SearchInputGroup
              destination={destination}
              setDestination={setDestination}
              checkin={checkin}
              checkout={checkout}
              setDates={setDates}
              distribution={distribution}
              setDistribution={setDistribution}
              handleSubmit={handleSubmit}
              disableSearch={disableSearch}
            />
          </SheetHeader>
        </SheetContent>
      </Sheet>
      <Sidebar open={sidebarOpen} onClose={() => setSidebarOpen(false)} />
    </>
  );
};

export default BottomNavigationBar;
