/* eslint-disable no-unused-vars */
/* eslint-disable no-nested-ternary */
import { ReactNode } from 'react';
import CashbackCard from '@components/molecules/CashbackCard';
import PurchasePricing, {
  PurchasePricingItemType
} from '@components/molecules/PurchasePricing';
import PurchaseResume, {
  PurchaseResumeConfigType
} from '@components/molecules/PurchaseResume';
import { AnimatePresence, motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { ITripcashOffer } from 'src/types/tripcash';
import { convertNumberToCurrency } from 'src/utils/currency';
import useWindowWith from 'src/hooks/useWindowSize';
import { useTranslation } from 'next-i18next';
import { useCurrency } from 'src/context/CurrencyContext';
import { IHotelOfferAdditionalTaxes } from 'src/types/hotel';
import { AdditionalTaxes } from 'src/types/cart';

interface IPurchaseDetailsResume {
  hotelName?: string;
  hotelAddress?: string;
  hotelImage?: string;
  hotelStars?: number;
  checkinDate: string;
  checkoutDate: string;
  distribution: PurchaseResumeConfigType[];
}

interface IPurchaseDetailsTripcash extends ITripcashOffer {
  currencySymbol: string;
}

interface IPurchaseDetailsPricing {
  totalPrice: number;
  userCurrency?: string;
  userTotalPrice?: number;
  details: PurchasePricingItemType[];
  onPurchase?: Function;
  disabled?: boolean;
  loading?: boolean;
  additionalTaxes: (IHotelOfferAdditionalTaxes | AdditionalTaxes)[];
  withDiscount: boolean;
  currencySymbol: string;
  userCurrencySymbol?: string;
  applyUserPrice?: boolean;
  position: 'sticky' | 'top';
}

interface IPurchaseDetails {
  header?: ReactNode;
  resume?: IPurchaseDetailsResume;
  tripcash?: IPurchaseDetailsTripcash;
  pricing?: IPurchaseDetailsPricing;
  onAction?: Function;
}

const PurchaseDetails = ({
  header,
  resume,
  tripcash,
  pricing,
  onAction
}: IPurchaseDetails) => {
  const { i18n } = useTranslation();
  const { currency } = useCurrency();
  const windowWidth = useWindowWith();
  const [ref, inView] = useInView({ threshold: 0 });

  const handleAction = () => {
    onAction?.();
  };

  return (
    <div className='h-full flex flex-col gap-6'>
      <div className='h-full flex flex-col gap-2 md:gap-6'>
        {header && <div className='w-full flex'>{header}</div>}
        {pricing && pricing.position === 'top' && (
          <PurchasePricing
            items={pricing.details}
            totalPrice={convertNumberToCurrency(
              i18n!.language,
              currency.code,
              pricing.totalPrice
            )}
            userTotalPrice={
              pricing.applyUserPrice
                ? `${pricing.userCurrencySymbol} ${convertNumberToCurrency(
                    i18n.language,
                    currency.code,
                    pricing.userTotalPrice || 0
                  )}`
                : undefined
            }
            onFinish={
              pricing.onPurchase ? () => pricing.onPurchase?.() : undefined
            }
            additionalTaxes={pricing.additionalTaxes}
            disabled={pricing.disabled}
            loading={pricing.loading}
            withDiscount={pricing.withDiscount}
            currencySymbol={pricing.currencySymbol}
          />
        )}
        {resume && (
          <div ref={ref}>
            <PurchaseResume
              hotelName={resume.hotelName}
              hotelAddress={resume.hotelAddress}
              hotelImage={resume.hotelImage}
              hotelStars={resume.hotelStars}
              checkinDate={resume.checkinDate}
              checkoutDate={resume.checkoutDate}
              configs={resume.distribution}
            />
          </div>
        )}
        <div className='sticky flex flex-col gap-6 top-6'>
          <AnimatePresence>
            {!inView && windowWidth > 768 && resume?.distribution.length && (
              <motion.div
                initial={{ marginTop: -25, opacity: 0, height: 0 }}
                animate={{ marginTop: 0, opacity: 1, height: 'auto' }}
                exit={{ marginTop: -25, opacity: 0, height: 0 }}
                transition={{
                  ease: [0.165, 0.84, 0.44, 1]
                }}
                className='flex flex-col gap-6 bg-white rounded-default overflow-hidden'
              >
                <div className='flex flex-col gap-1 p-6'>
                  {resume.distribution.map(config => (
                    <div key={config.index} className='flex flex-col'>
                      <p className='text-sm text-gray-500'>{config.title}</p>
                      <div className='flex justify-between items-start mt-1 text-ellipsis gap-2'>
                        <h3 className='font-semibold text-primary-900 line-clamp-2'>
                          {config.text}
                        </h3>
                        <p className='text-gray-500 text-sm whitespace-nowrap'>
                          {config.value}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
          {tripcash && (
            <div className='hidden md:flex'>
              <CashbackCard
                percentage={tripcash.formatedPercent}
                currencySymbol={tripcash.currencySymbol}
                value={
                  tripcash.value
                    ? convertNumberToCurrency(
                        i18n.language,
                        currency.code,
                        tripcash.value
                      )
                    : null
                }
              />
            </div>
          )}
          {pricing && pricing.position === 'sticky' && (
            <PurchasePricing
              items={pricing.details}
              totalPrice={convertNumberToCurrency(
                i18n.language,
                currency.code,
                pricing.totalPrice
              )}
              onFinish={
                pricing.onPurchase ? () => pricing.onPurchase?.() : undefined
              }
              additionalTaxes={pricing.additionalTaxes}
              disabled={pricing.disabled}
              loading={pricing.loading}
              withDiscount={pricing.withDiscount}
              currencySymbol={pricing.currencySymbol}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default PurchaseDetails;
