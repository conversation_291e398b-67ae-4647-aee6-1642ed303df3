import { Input } from '@components/atoms/Input';
import Button from '@components/atoms/Button';
import { IChangePasswordPayload } from 'src/types/auth';
import { useContext, useState } from 'react';
import { toast } from 'react-toastify';
import { useForm } from 'react-hook-form';
import { useRouter } from 'next/router';
import { AuthContext } from 'src/context/AuthContext';
import { Password } from '@phosphor-icons/react';
import { AxiosError } from 'axios';
import { useTranslation } from 'next-i18next';
import Link from 'next/link';

const ChangePasswordBox = () => {
  const router = useRouter();
  const { t, i18n } = useTranslation(['password-change', 'common'], {
    nsMode: 'fallback'
  });
  const { changePassword } = useContext(AuthContext);

  const [loading, setLoading] = useState<boolean>(false);
  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
    setFocus
  } = useForm<IChangePasswordPayload>({
    defaultValues: {
      oldPassword: '',
      newPassword: '',
      newPasswordAgain: ''
    },
    mode: 'onBlur',
    reValidateMode: 'onBlur'
  });

  const onSubmit = async (payload: IChangePasswordPayload) => {
    try {
      if (payload.newPassword !== payload.newPasswordAgain) {
        setError(
          'newPasswordAgain',
          {
            message: t('password.passwordMustBeSameError')
          },
          { shouldFocus: true }
        );

        return;
      }

      setLoading(true);
      await changePassword?.(payload);

      toast(t('password.success'), { type: 'success' });
      router.push({ pathname: t('routes.myProfile') }, '', {
        locale: i18n.language
      });
    } catch (e: unknown) {
      const error = e as AxiosError<any>;

      if (error.response?.status === 400) {
        toast(error.response?.data?.message || t('password.error400'), {
          type: 'error'
        });
      } else if (error.response?.status === 404) {
        toast(t('password.error404'), { type: 'error' });
      } else if (error.response?.status === 500) {
        toast(error.response?.data?.message || t('password.error500'), {
          type: 'error'
        });
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <form
      className='w-[90%] md:w-[35%] m-auto flex flex-col p-6 bg-white rounded-default gap-1'
      onSubmit={handleSubmit(data => onSubmit(data))}
    >
      <Password size={38} weight='duotone' />
      <h2 className='font-medium mt-3 text-primary-900'>
        {t('password.title')}
      </h2>
      <p className='flex gap-1 text-gray-500 text-sm'>
        {t('password.description')}
      </p>
      <div className='flex flex-col gap-2 my-5'>
        <Input
          placeholder={t('password.oldPasswordField')}
          {...register('oldPassword', { required: t('password.required') })}
          error={errors.oldPassword?.message}
          focus={() => setFocus('oldPassword')}
          type='password'
          color='gray'
        />
        <Input
          placeholder={t('password.newPasswordField')}
          {...register('newPassword', { required: t('password.required') })}
          error={errors.newPassword?.message}
          focus={() => setFocus('newPassword')}
          color='gray'
          type='password'
        />
        <Input
          placeholder={t('password.newPasswordAgainField')}
          {...register('newPasswordAgain', {
            required: t('password.required')
          })}
          error={errors.newPasswordAgain?.message}
          focus={() => setFocus('newPasswordAgain')}
          color='gray'
          type='password'
        />
      </div>
      <Link className='underline' href={t('routes.passwordRecovery')}>
        {t('password.forgotPassword')}
      </Link>
      <Button
        color='primary'
        size='large'
        fontWeight='bold'
        loading={loading}
        type='submit'
      >
        {t('password.changePassword')}
      </Button>
    </form>
  );
};

export default ChangePasswordBox;
