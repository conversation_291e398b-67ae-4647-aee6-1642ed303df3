import { ReactNode } from 'react';
import { useTranslation } from 'next-i18next';
import Button from '../../atoms/Button';
import Modal from '../../molecules/Modal';

interface IOrderCancelConfirmModal {
  icon?: ReactNode;
  title: string;
  text: ReactNode;
  show: boolean;
  onCancel: Function;
  onConfirm: Function;
  loading: boolean;
}

const OrderCancelConfirmModal = ({
  icon,
  title,
  text,
  show,
  onCancel,
  onConfirm,
  loading
}: IOrderCancelConfirmModal) => {
  const { t } = useTranslation('bookings');
  return (
    <Modal
      icon={icon}
      title={title}
      show={show}
      onClose={() => onCancel()}
      footer={
        <>
          <Button color='white' onClick={() => onCancel()}>
            {t('orderCard.modal.notNow')}
          </Button>
          <Button color='danger' onClick={() => onConfirm()} loading={loading}>
            {t('orderCard.modal.yesCancel')}
          </Button>
        </>
      }
    >
      <div className='flex flex-col md:flex-row gap-3'>
        <div className='text-lg text-gray-500'>{text}</div>
      </div>
    </Modal>
  );
};

export default OrderCancelConfirmModal;
