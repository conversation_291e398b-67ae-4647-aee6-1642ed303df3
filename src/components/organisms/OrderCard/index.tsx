import { FC, useMemo, useState } from 'react';
import { useRouter } from 'next/router';
import { useTranslation } from 'next-i18next';
import { formatDistance } from 'date-fns';
import {
  CaretDown,
  Check,
  ClockCountdown,
  Download,
  Prohibit,
  Question,
  Receipt,
  Warning,
  XCircle
} from '@phosphor-icons/react';
import { cancelOrder } from 'src/server/order';
import { toast } from 'react-toastify';
import Button from '@components/atoms/Button';
import Divider from '@components/atoms/Divider';
import Dropdown from '@components/molecules/Dropdown';
import ReservationCard from '@components/organisms/ReservationCard';
import OrderCardPayment from '@components/organisms/OrderCardPayment';
import OrderCancelConfirmModal from '@components/organisms/OrderCancelConfirmModal';
import DownloadReceiptModal from '@components/organisms/DownloadReceiptModal';
import { convertStringToFormatedDate, getDateFNSLocale } from 'src/utils/date';
import { IOrder } from 'src/types/order';

interface IOrderCard extends IOrder {
  onCancel?: Function;
}

const OrderCard: FC<IOrderCard> = ({
  id,
  status,
  sentDate,
  orderCode,
  price,
  payment,
  cancellationInfo,
  onCancel,
  itens
}: IOrderCard) => {
  const { t, i18n } = useTranslation(['bookings', 'common'], {
    nsMode: 'fallback'
  });
  const [cancelationConfirmModalOpen, setCancelationConfirmModalOpen] =
    useState<boolean>(false);
  const [downloadReceiptModalOpen, setDownloadReceiptModalOpen] =
    useState(false);
  const [cancelLoading, setCancelLoading] = useState<boolean>(false);
  const [optionsOpen, setOptionsOpen] = useState<boolean>(false);

  const router = useRouter();

  const orderStatus = {
    CANCELED: {
      text: t('orderCard.status.canceled'),
      icon: <Prohibit size={18} weight='bold' />,
      type: 'danger'
    },
    PENDING_CANCEL: {
      text: t('orderCard.status.pendingCancellation'),
      icon: <ClockCountdown size={18} weight='bold' />,
      type: 'warning'
    },
    CONFIRMED: {
      text: t('orderCard.status.confirmed'),
      icon: <Check size={18} weight='bold' />,
      type: 'success'
    },
    PENDING_PAYMENT: {
      text: t('orderCard.status.pendingPayment'),
      icon: <ClockCountdown size={18} weight='bold' />,
      type: 'warning'
    },
    PENDING_ANTIFRAUD: {
      text: t('orderCard.status.pendingAntifraud'),
      icon: <ClockCountdown size={18} weight='bold' />,
      type: 'warning'
    },
    PAYMENT_ERROR: {
      text: t('orderCard.status.paymentError'),
      icon: <Prohibit size={18} weight='bold' />,
      type: 'danger'
    },
    PAYMENT_EXPIRED: {
      text: t('orderCard.status.paymentExpired'),
      icon: <XCircle size={18} weight='bold' />,
      type: 'danger'
    },
    PAYMENT_REJECTED: {
      text: t('orderCard.status.paymentRejected'),
      icon: <XCircle size={18} weight='bold' />,
      type: 'danger'
    },
    PENDING_CONFIRMATION: {
      text: t('orderCard.status.pendingConfirmation'),
      icon: <ClockCountdown size={18} weight='bold' />,
      type: 'info'
    },
    BOOKING_ERROR: {
      text: t('orderCard.status.bookingError'),
      icon: <XCircle size={18} weight='bold' />,
      type: 'danger'
    }
  };

  const handleAskForHelp = (code: string) => {
    router.push({ pathname: t('routes.contact'), query: { order: code } });
  };

  const handleDownloadReceipt = (responsable: string) => {
    window.open(
      `/${i18n.language}${t(
        'routes.receipt'
      )}/${id}?responsable=${responsable}`,
      '_blank'
    );
    setDownloadReceiptModalOpen(false);
  };

  const optionsItems = [
    ...(cancellationInfo.isCancellable
      ? [
          {
            icon: <Prohibit weight='bold' size={22} />,
            text: t('orderCard.buttons.requestCancellation'),
            onClick: () => setCancelationConfirmModalOpen(true)
          }
        ]
      : []),
    {
      icon: <Question weight='bold' size={22} />,
      text: t('orderCard.buttons.askForHelp'),
      onClick: () => handleAskForHelp(orderCode)
    }
  ];

  const handleCancelOrder = async () => {
    setCancelLoading(true);
    try {
      await cancelOrder(id);
      onCancel?.(id);
      toast(t('orderCard.modal.cancelRequestSuccess'), {
        type: 'success'
      });
    } catch (err) {
      toast(t('orderCard.modal.cancelRequestError'), {
        type: 'error'
      });
    } finally {
      setCancelLoading(false);
      setCancelationConfirmModalOpen(false);
    }
  };

  const time = useMemo(
    () =>
      formatDistance(new Date(sentDate), new Date(), {
        locale: getDateFNSLocale(i18n.language),
        addSuffix: true
      }),
    [i18n.language, sentDate]
  );

  return (
    <div className='bg-white p-6 flex flex-col gap-4 rounded-default'>
      <div className='flex justify-between'>
        <div className='flex items-start md:items-center flex-col md:flex-row gap-6'>
          <div
            className={`
              flex items-center gap-2 py-2 pr-5 pl-4 -ml-6 font-semibold
              rounded-l-default rounded-r-full
              ${
                orderStatus[status].type === 'success'
                  ? 'bg-green-100 text-green-600'
                  : orderStatus[status].type === 'warning'
                  ? 'bg-yellow-100 text-yellow-600'
                  : orderStatus[status].type === 'danger'
                  ? 'bg-red-100 text-red-600'
                  : 'bg-primary-100 text-primary-900'
              }
            `}
          >
            {orderStatus[status].icon}
            {orderStatus[status].text}
          </div>
          <div className='flex items-center gap-4'>
            <div className='flex flex-col'>
              <h2 className='text-lg font-semibold text-primary-900'>
                {t('orderCard.order')}{' '}
                <span className='text-sm text-gray-500'>{orderCode}</span>
              </h2>
              <p className='text-sm text-gray-500'>{time}</p>
            </div>
            <Divider />
            <div className='flex flex-col'>
              <p className='text-sm text-gray-500'>
                {t('orderCard.labels.totalValue')}
              </p>
              <h3 className='text-xl text-primary-900 font-semibold'>
                {price.currencySymbol} {price.finalPrice.formattedValue}
              </h3>
            </div>
          </div>
        </div>
      </div>
      <div className='flex flex-col gap-6 py-4 md:px-4'>
        {itens.map(item => (
          <ReservationCard
            key={item.booking.id}
            stars={parseFloat(item.booking.hotel.stars)}
            name={item.booking.hotel.name}
            images={[item.booking.hotel?.photoCover?.url!]}
            address={item.booking.hotel.address}
            reservationCode={item.booking.reference}
            distributionText={item.booking.price.price.description}
            totalPrice={`${price.currencySymbol} ${price.finalPrice.formattedValue}`}
            status={item.booking.status}
            statusDisplay={item.booking.statusDisplay}
            checkin={convertStringToFormatedDate(
              getDateFNSLocale(i18n.language),
              item.booking.checkin
            )}
            checkout={convertStringToFormatedDate(
              getDateFNSLocale(i18n.language),
              item.booking.checkout
            )}
            rooms={item.booking.rooms}
            border={false}
            expandable
          />
        ))}
        <OrderCardPayment
          {...payment}
          status={status}
          totalValue={`${price.currencySymbol} ${price.finalPrice.formattedValue}`}
          instalmentFormatted={`${price.currencySymbol} ${price.installmentAmount.formattedValue}`}
          orderStatus={orderStatus[status].text}
        />
      </div>
      <OrderCancelConfirmModal
        icon={<Warning size={26} weight='duotone' />}
        title={t('orderCard.modal.title')}
        loading={cancelLoading}
        text={
          <>
            {cancellationInfo.infos.map(info => (
              <p>{info}</p>
            ))}
            <br />
            <p>{t('orderCard.modal.confirmText')}</p>
          </>
        }
        onCancel={() => setCancelationConfirmModalOpen(false)}
        onConfirm={() => handleCancelOrder()}
        show={cancelationConfirmModalOpen}
      />
      <DownloadReceiptModal
        show={downloadReceiptModalOpen}
        onCancel={() => setDownloadReceiptModalOpen(false)}
        onConfirm={responsable => handleDownloadReceipt(responsable)}
      />
      <div className='flex flex-col md:flex-row justify-between gap-3'>
        {status === 'CONFIRMED' ? (
          <div className='flex gap-3 flex-col md:flex-row'>
            <Button
              variant='outline'
              onClick={() =>
                window.open(`/${i18n.language}/voucher/${id}`, '_blank')
              }
            >
              <Download weight='bold' size={22} />
              {t('orderCard.buttons.downloadVoucher')}
            </Button>
            <Button
              variant='outline'
              onClick={() => setDownloadReceiptModalOpen(true)}
            >
              <Receipt weight='bold' size={22} />
              {t('orderCard.buttons.downloadReceipt')}
            </Button>
          </div>
        ) : (
          <div />
        )}
        {status === 'CONFIRMED' ? (
          <div className='relative'>
            <Dropdown
              open={optionsOpen}
              items={optionsItems}
              onToggle={setOptionsOpen}
              className='w-full md:w-auto'
            >
              <Button className='w-full md:w-auto'>
                <CaretDown size={18} />
                {t('orderCard.buttons.moreOptions')}
              </Button>
            </Dropdown>
          </div>
        ) : (
          <div>
            {cancellationInfo.isCancellable && (
              <Button
                color='danger'
                onClick={() => setCancelationConfirmModalOpen(true)}
                variant='outline'
              >
                <Prohibit weight='bold' size={22} />
                {t('orderCard.buttons.requestCancellation')}
              </Button>
            )}
            <Button
              size='small'
              onClick={() => handleAskForHelp(orderCode)}
              className='w-full md:w-auto'
              variant='outline'
            >
              <Question weight='bold' size={18} />
              {t('orderCard.buttons.askForHelp')}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default OrderCard;
