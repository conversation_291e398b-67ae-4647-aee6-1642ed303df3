import { FC } from 'react';
import Button from '@components/atoms/Button';
import { useRouter } from 'next/router';
import { useTranslation } from 'next-i18next';

interface ITripcashTripcashHeader {}

const TripcashHeader: FC<ITripcashTripcashHeader> = () => {
  const { t, i18n } = useTranslation(['tripcash', 'common'], {
    nsMode: 'fallback'
  });
  const router = useRouter();

  const handleRedirect = () => {
    router.push({ pathname: t('routes.myTripcash') }, '', {
      locale: i18n.language
    });
  };

  const handleScroll = () => {
    document
      .querySelector('.how-works')
      ?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <div className='w-full md:h-[80vh] h-auto flex justify-center items-center bg-primary-900 relative md:py-0 py-[50px]'>
      <div className="w-full h-full absolute top-0 opacity-30 bg-[url('/images/tripcash_header_bg.webp')] bg-center bg-no-repeat bg-cover" />
      <div className='flex flex-col justify-center items-center relative'>
        <h2 className='text-2xl md:text-5xl font-bold text-white text-center mb-4 leading-snug'>
          {t('headerTitle')}
        </h2>
        <h1
          className='text-lg md:text-4xl font-medium text-white text-center [&>span]:text-secondary-500'
          dangerouslySetInnerHTML={{ __html: t('headerSubtitle') }}
        />
        <div className='flex items-center mt-6 gap-4'>
          <Button
            shape='default'
            color='primary'
            onClick={() => handleRedirect()}
          >
            {t('headerButtonGo')}
          </Button>
          <Button shape='default' color='white' onClick={() => handleScroll()}>
            {t('headerButtonHowItWorks')}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default TripcashHeader;
