import { SealCheck, SealWarning } from '@phosphor-icons/react';
import Button from '@components/atoms/Button';
import { useRouter } from 'next/router';
import { useTranslation } from 'next-i18next';

interface IEmailVerificationBox {
  status: 'VERIFIED' | 'ERROR' | 'TOKEN_NOT_FOUND';
}

const EmailVerificationBox = ({ status }: IEmailVerificationBox) => {
  const { t } = useTranslation('verify-account');
  const router = useRouter();

  const getIconByStatus = () => {
    switch (status) {
      case 'VERIFIED':
        return (
          <SealCheck size={38} weight='duotone' className='text-primary-900' />
        );
      case 'ERROR':
        return (
          <SealWarning
            size={38}
            weight='duotone'
            className='text-primary-900'
          />
        );
      default:
        return (
          <SealWarning
            size={38}
            weight='duotone'
            className='text-primary-900'
          />
        );
    }
  };

  const getStatusDisplay = () => {
    switch (status) {
      case 'VERIFIED':
        return t('verification.verifiedDisplay');
      case 'ERROR':
        return t('verification.errorDisplay');
      default:
        return t('verification.tokenNotFoundDisplay');
    }
  };

  return (
    <div className='w-[90%] md:w-2/5 mx-auto flex flex-col items-start p-12 bg-white rounded-default'>
      {getIconByStatus()}
      <h2 className='text-primary-900 font-medium mt-4'>
        {t('verification.title')}
      </h2>
      <span className='flex gap-1 text-gray-500 mt-1'>
        {getStatusDisplay()}
      </span>
      <Button onClick={() => router.push('/')} color='primary' className='mt-4'>
        {t('verification.back')}
      </Button>
    </div>
  );
};

export default EmailVerificationBox;
