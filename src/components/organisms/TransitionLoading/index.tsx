/* eslint-disable import/no-extraneous-dependencies */
import { useEffect, useState } from 'react';
import useBlockScroll from 'src/hooks/useBlockScroll';
import { useRouter } from 'next/router';
import { useRive } from '@rive-app/react-canvas';
import { useTranslation } from 'next-i18next';

const Loading = () => {
  const router = useRouter();
  const { t } = useTranslation('common');
  const [loading, setLoading] = useState(false);

  const { RiveComponent } = useRive({
    src: '/animations/loading.riv',
    stateMachines: 'Loading',
    autoplay: true
  });

  useBlockScroll(loading);

  useEffect(() => {
    const handleStart = (url: string) =>
      url !== router.asPath &&
      url.includes(t('routes.hotel')) &&
      setLoading(true);
    const handleComplete = () => setLoading(false);

    router.events.on('routeChangeStart', handleStart);
    router.events.on('routeChangeComplete', handleComplete);
    router.events.on('routeChangeError', handleComplete);

    return () => {
      router.events.off('routeChangeStart', handleStart);
      router.events.off('routeChangeComplete', handleComplete);
      router.events.off('routeChangeError', handleComplete);
    };
  });

  return loading ? (
    <div className='fixed flex flex-col justify-center items-center z-100000 bg-white/80 top-0 left-0 w-full h-full backdrop-blur-xl gap-2'>
      <div className='w-12 h-12'>
        <RiveComponent />
      </div>
    </div>
  ) : null;
};

export default Loading;
