import { ReactNode } from 'react';
import Button from '@components/atoms/Button';
import { HandCoins } from '@phosphor-icons/react';
import { cn } from '@utils/classes';

interface ITripcashHero {
  title: string;
  description: ReactNode;
  action: Function;
  ctaText: string;
  className?: string;
}

const TripcashHero = ({
  title,
  description,
  ctaText,
  action,
  className
}: ITripcashHero) => {
  return (
    <div className='container'>
      <div
        className={cn(
          'w-full flex flex-col-reverse md:flex-row items-center mt-24 mb-24 gap-12 md:gap-[100px] bg-[url("/images/tripcash_hero.webp")] bg-no-repeat bg-cover bg-center rounded-default overflow-hidden relative',
          className
        )}
      >
        <div className='w-full h-full absolute bg-gradient-to-r from-primary-900/90 via-primary-900/60 to-primary-900/0 md:to-transparent md:via-primary-900/30 md:from-primary-900/90' />
        <div className='w-full md:w-1/2 flex flex-col items-start gap-3 p-12 md:p-12 z-[1]'>
          <HandCoins size={42} className='text-secondary-500' />
          <div className='flex items-center gap-[15px]'>
            <h2
              className='text-[28px] md:text-[28px] font-semibold leading-[1.4] text-primary-100'
              dangerouslySetInnerHTML={{ __html: title }}
            />
          </div>
          <p className='leading-[1.8] text-gray-200 font-light mb-[15px]'>
            {description}
          </p>
          <Button color='gray' onClick={() => action?.()}>
            {ctaText}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default TripcashHero;
