/* eslint-disable no-unused-vars */
import { Check, Receipt } from '@phosphor-icons/react';
import { useState } from 'react';
import { useTranslation } from 'next-i18next';
import Button from '../../atoms/Button';
import Modal from '../../molecules/Modal';
import { EReceiptResponsable } from './types';

interface IDownloadReceiptModalProps {
  show: boolean;
  onCancel: Function;
  onConfirm: (responsable: EReceiptResponsable) => void;
}

const DownloadReceiptModal = ({
  show,
  onCancel,
  onConfirm
}: IDownloadReceiptModalProps) => {
  const { t } = useTranslation('bookings');
  const [receiptResponsable, setReceiptResponsable] = useState(
    EReceiptResponsable.PAYER
  );
  return (
    <Modal
      icon={<Receipt />}
      title={t('orderCard.receiptModal.title')}
      show={show}
      onClose={() => onCancel()}
      footer={
        <>
          <Button color='white' onClick={() => onCancel()}>
            {t('orderCard.receiptModal.cancel')}
          </Button>
          <Button color='primary' onClick={() => onConfirm(receiptResponsable)}>
            {t('orderCard.receiptModal.download')}
          </Button>
        </>
      }
    >
      <div className='flex flex-col gap-4'>
        <p className='text-gray-500'>
          {t('orderCard.receiptModal.description')}
        </p>
        <div
          className={`flex gap-2 items-center bg-gray-100 rounded-button px-4 cursor-default py-3 ${
            receiptResponsable === EReceiptResponsable.PAYER
              ? 'border-2 border-primary-500'
              : ''
          }`}
          onClick={() => setReceiptResponsable(EReceiptResponsable.PAYER)}
        >
          {receiptResponsable === EReceiptResponsable.PAYER && (
            <Check size={18} />
          )}
          <h3>{t('orderCard.receiptModal.financeResponsable')}</h3>
        </div>
        <div
          className={`flex gap-2 items-center bg-gray-100 rounded-button px-4 py-3 ${
            receiptResponsable === EReceiptResponsable.GUEST
              ? 'border-2 border-primary-500'
              : ''
          }`}
          onClick={() => setReceiptResponsable(EReceiptResponsable.GUEST)}
        >
          {receiptResponsable === EReceiptResponsable.GUEST && (
            <Check size={18} />
          )}
          <h3>{t('orderCard.receiptModal.guest')}</h3>
        </div>
      </div>
    </Modal>
  );
};

export default DownloadReceiptModal;
