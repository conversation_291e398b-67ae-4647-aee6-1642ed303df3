import { FC } from 'react';
import { Check } from '@phosphor-icons/react';
import classNames from 'classnames';

type StepMarkerProps = {
  step: number;
  title: string;
  subtitle?: string;
  checked: boolean;
  active: boolean;
  onClick?: () => void;
};

const StepMarker: FC<StepMarkerProps> = ({
  step,
  title,
  subtitle,
  checked,
  active,
  onClick
}) => {
  const classes = classNames(
    'w-full md:w-12 h-12 flex-none rounded-default flex justify-center items-center font-medium',
    {
      'bg-green-500 text-white': checked,
      'bg-white text-gray-500': !checked,
      'border-2 bg-gray-100 border-primary-500': active
    }
  );

  return (
    <div
      className='w-full flex flex-col items-center text-center gap-3 md:flex-row md:items-center md:text-left'
      onClick={onClick}
    >
      <div className={classes}>{checked ? <Check size={22} /> : step}</div>
      <div>
        <h3 className='text-sm md:text-base whitespace-nowrap'>{title}</h3>
        {subtitle && (
          <p className='text-sm text-gray-500 whitespace-nowrap'>{subtitle}</p>
        )}
      </div>
    </div>
  );
};

export default StepMarker;
