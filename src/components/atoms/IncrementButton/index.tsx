import { FC } from 'react';
import { Minus, Plus } from '@phosphor-icons/react';
import Button from '../Button';

interface IIncrementButton {
  label: string;
  value: number;
  // eslint-disable-next-line no-unused-vars
  increment: (value: number) => void;
  minValue?: number;
  maxValue?: Number;
  description?: string;
}

const IncrementButton: FC<IIncrementButton> = ({
  label,
  increment,
  description,
  minValue = 0,
  maxValue,
  value
}) => {
  return (
    <div className='flex items-center justify-between bg-white'>
      <div>
        <span className='text-base text-black'>{label}</span>
        {description ? (
          <p className='text-sm text-black'>{description}</p>
        ) : null}
      </div>
      <div className='flex items-center gap-4'>
        <Button
          disabled={value === minValue}
          color='white'
          onClick={() => increment(-1)}
          size='icon'
        >
          <Minus />
        </Button>
        <span className='font-medium'>{value}</span>
        <Button
          onClick={() => increment(1)}
          disabled={value === maxValue}
          color='white'
          size='icon'
        >
          <Plus />
        </Button>
      </div>
    </div>
  );
};

export default IncrementButton;
