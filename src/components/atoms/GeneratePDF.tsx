/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable react/jsx-no-useless-fragment */
import * as html2pdf from 'html2pdf.js';
import { FC, useEffect } from 'react';

interface IGeneratePDF {
  fileName: string;
}

const GeneratePDF: FC<IGeneratePDF> = ({ fileName }) => {
  useEffect(() => {
    html2pdf
      .default()
      .from(document.querySelector(`.${fileName}`))
      .set({
        filename: `${fileName}.pdf`,
        image: { type: 'jpeg', quality: 1 },
        html2canvas: { scale: 2, useCORS: true },
        jsPDF: { unit: 'in', format: 'letter', orientation: 'portrait' }
      })
      .save()
      .then(() => {
        //
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return <></>;
};

export default GeneratePDF;
