import { FC, ReactNode } from 'react';

type BadgeSize = 'small' | 'normal' | 'large';

interface IBadgeProps {
  icon?: ReactNode;
  children: ReactNode;
  type:
    | 'success'
    | 'warning'
    | 'danger'
    | 'info'
    | 'white'
    | 'primary'
    | 'secondary';
  size?: BadgeSize;
  mobile?: boolean;
  className?: string;
}

const Badge: FC<IBadgeProps> = ({
  children,
  icon,
  type,
  size = 'normal',
  mobile,
  className
}) => {
  const baseClasses = `flex items-center rounded-button ${
    mobile ? 'lg:hidden md:flex' : ''
  } ${className || ''}`;
  let typeClasses = '';

  const sizeClasses = {
    small: 'px-2 py-1 gap-0',
    normal: 'py-2 px-3 gap-2',
    large: 'py-3 px-4 gap-3'
  }[size];

  const textSizesClasses = {
    small: 'text-xs font-normal',
    normal: 'text-sm font-semibold',
    large: 'text-base font-semibold'
  }[size];

  switch (type) {
    case 'success':
      typeClasses = 'bg-success-100 text-success-600';
      break;
    case 'warning':
      typeClasses = 'bg-warning-100 text-warning-600';
      break;
    case 'danger':
      typeClasses = 'bg-danger-100 text-danger-600';
      break;
    case 'info':
      typeClasses = 'bg-info-100 text-info-900';
      break;
    case 'white':
      typeClasses = 'bg-white text-gray-900';
      break;
    case 'primary':
      typeClasses = 'bg-primary-500 text-white';
      break;
    case 'secondary':
      typeClasses = 'bg-gray-100 text-primary-500';
      break;
    default:
      typeClasses = '';
  }

  const textClasses = `font-semibold whitespace-nowrap ${textSizesClasses}`;

  return (
    <div className={`${baseClasses} ${typeClasses} ${sizeClasses}`}>
      {icon && <span className='mr-2'>{icon}</span>}
      <p className={textClasses}>{children}</p>
    </div>
  );
};

export default Badge;
