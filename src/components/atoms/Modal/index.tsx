import { FC, ReactNode, useRef } from 'react';
import useOnClickOutside from 'src/hooks/useClickOutside';

interface IModalProps {
  isOpen: boolean;
  children: ReactNode;
  showCloseButton?: boolean;
  onClose?: Function;
  closeOnClickOutside?: boolean;
}

const Modal: FC<IModalProps> = ({
  isOpen,
  children,
  onClose,
  showCloseButton = false,
  closeOnClickOutside = false
}) => {
  const ref = useRef(null);

  useOnClickOutside(ref, () => {
    if (closeOnClickOutside) onClose?.();
  });

  return isOpen ? (
    <div className='fixed top-0 left-0 w-full h-full bg-black/50 flex justify-center items-center'>
      <div
        ref={ref}
        className='bg-white rounded-default shadow-lg min-w-[350px] relative'
      >
        {showCloseButton ? (
          <button
            type='button'
            className='absolute top-[10px] right-[10px] cursor-pointer'
            onClick={() => onClose?.()}
          >
            Fechar
          </button>
        ) : null}
        <div className='modal-content'>{children}</div>
      </div>
    </div>
  ) : null;
};

export default Modal;
