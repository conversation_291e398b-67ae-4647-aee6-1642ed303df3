/* eslint-disable react/no-array-index-key */
import { FC } from 'react';
import { Star } from '@phosphor-icons/react';

interface IStars {
  rate: number;
}

const Stars: FC<IStars> = ({ rate }: IStars) => {
  return (
    <div className='flex items-center gap-0.5'>
      {Array.from({ length: 5 }).map((_, index) => (
        <Star
          key={`${rate}${index}`}
          weight={rate > index ? 'fill' : 'light'}
          className={`text-yellow-300 ${
            rate > index ? 'text-yellow-300' : ''
          } ${index > 0 ? 'hidden md:block' : ''}`}
          size={12}
        />
      ))}
      <p className='text-sm text-primary-900 font-medium block md:hidden'>
        {rate}
      </p>
    </div>
  );
};

export default Stars;
