/* eslint-disable no-unused-vars */
import { FC } from 'react';
import { motion } from 'framer-motion';

interface ISwitch {
  onChange?: (on: boolean) => void;
  on: boolean;
  disabled?: boolean;
  label: string;
}

const spring = {
  type: 'spring',
  stiffness: 700,
  damping: 30
};
const Switch: FC<ISwitch> = ({ on, onChange, disabled, label }: ISwitch) => {
  return (
    <button
      className='flex flex-row items-center cursor-pointer select-none border-none bg-transparent'
      disabled={disabled}
      onClick={() => onChange?.(!on)}
      aria-label={label}
    >
      <div
        className={`w-[35px] h-[20px] flex flex-row items-center p-[5px] rounded-[50px] transition-colors duration-200 ${
          on ? 'bg-primary-500 justify-end' : 'bg-gray-200 justify-start'
        }`}
      >
        <motion.div
          className={`w-[10px] h-[10px] rounded-full ${
            on ? 'bg-white' : 'bg-gray-500'
          }`}
          transition={spring}
        />
      </div>
    </button>
  );
};

export default Switch;
