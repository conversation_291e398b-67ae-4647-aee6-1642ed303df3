import { FC } from 'react';
import Stars from '@components/atoms/Stars';

interface HotelInfoResumeProps {
  address?: string;
  stars?: number;
  name: string;
  image?: string;
}

const HotelInfoResume: FC<HotelInfoResumeProps> = ({
  address,
  stars,
  name,
  image
}) => {
  return (
    <div className='w-full flex gap-3'>
      <img
        src={image || '/images/hotel-placeholder.webp'}
        alt={`Imagem do ${name}`}
        className='w-[125px] rounded-button object-cover'
      />
      <div className='w-full flex flex-col'>
        <div className='w-full flex justify-between items-center gap-2 flex-row-reverse md:flex-col md:items-start'>
          {stars ? <Stars rate={stars} /> : null}
          <h3 className='text-lg font-medium text-primary-900 leading-6'>
            {name}
          </h3>
        </div>
        {address ? <p className='text-gray-500 text-sm'>{address}</p> : null}
      </div>
    </div>
  );
};

export default HotelInfoResume;
