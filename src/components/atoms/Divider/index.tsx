import { HTMLAttributes } from 'react';
import classNames from 'classnames';

interface IDivider extends HTMLAttributes<HTMLDivElement> {
  orientation?: 'horizontal' | 'vertical';
  fullHeight?: boolean;
  className?: string;
}

const Divider = ({
  orientation = 'vertical',
  fullHeight,
  className,
  ...props
}: IDivider) => {
  const dividerClass = classNames({
    'w-px h-10 bg-gray-200': orientation === 'vertical' && !fullHeight,
    'w-full h-px bg-gray-200': orientation === 'horizontal',
    'w-px h-full bg-gray-200': orientation === 'vertical' && fullHeight,
    [className as string]: className
  });

  return <div {...props} className={dividerClass} />;
};

export default Divider;
