import { FC } from 'react';

interface ICityCard {
  image?: string;
  title?: string;
  description?: string;
  onClick?: Function;
}

const CityCard: FC<ICityCard> = ({
  image,
  title,
  description,
  onClick
}: ICityCard) => {
  return (
    <div
      className='group w-full h-full relative cursor-pointer overflow-hidden rounded-default nth-[2]:col-span-1 nth-[4]:col-span-1 md:nth-[2]:col-span-2 md:nth-[4]:col-span-2'
      onClick={() => onClick?.()}
    >
      <img
        src={image || '/images/hotel-placeholder.webp'}
        alt={`Imagem da cidade ${title}`}
        className='top-0 left-0 w-[101%] h-[101%] relative transition-transform duration-500 object-cover group-hover:scale-110'
      />
      <div className='absolute top-0 left-0 w-full h-full p-[25px] flex flex-col bg-linear-to-b from-[rgba(5,16,54,0.6)] to-[rgba(5,16,54,0)]'>
        {title && (
          <h2 className='mb-1 text-white text-lg md:text-2xl'>{title}</h2>
        )}
        {description && <p className='text-white text-sm'>{description}</p>}
      </div>
    </div>
  );
};

export default CityCard;
