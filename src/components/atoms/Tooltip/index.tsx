import { FC, ReactNode } from 'react';

interface ITooltip {
  text: string;
  active: boolean;
  children: ReactNode;
}

const Tooltip: FC<ITooltip> = ({ text, active, children }: ITooltip) => {
  return (
    <div className='relative flex flex-col justify-center items-center group'>
      <div className='w-full'>{children}</div>
      <div
        className={`
          absolute top-[calc(100%+8px)] p-[5px_10px] text-white bg-primary-900
          text-[14px] rounded-[8px] flex-col items-center gap-[5px] transition-all
          ${active ? 'group-hover:flex' : 'hidden'}
          before:content-[''] before:absolute before:w-2 before:h-2 before:bg-primary-900
          before:rotate-45 before:top-[-4px]
        `}
      >
        {text}
      </div>
    </div>
  );
};

export default Tooltip;
