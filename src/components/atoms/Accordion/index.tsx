import { useState } from 'react';

interface IAccordionProps {
  title: string;
  text: string;
}

const Accordion = ({ title, text }: IAccordionProps) => {
  const [open, setOpen] = useState<boolean>(false);

  return (
    <div
      className='w-full flex flex-col p-6 border border-gray-200 rounded-default cursor-pointer'
      onClick={() => setOpen(prev => !prev)}
    >
      <div className='flex items-center gap-6'>
        <div
          className={`relative w-10 h-10 rounded-full flex justify-center items-center transition-colors duration-300 ${
            open ? 'bg-primary-900' : 'bg-gray-100'
          }`}
        >
          <div
            className={`absolute w-4 h-0.5 rounded transition-transform duration-300 ${
              open
                ? 'bg-primary-900 transform rotate-[-90deg]'
                : 'bg-primary-900 transform rotate-[-180deg]'
            }`}
          />
          <div
            className={`absolute w-4 h-0.5 rounded transition-transform duration-300 ${
              open
                ? 'bg-white transform rotate-0'
                : 'bg-primary-900 transform rotate-[-90deg]'
            }`}
          />
        </div>
        <h2 className='font-medium text-lg text-primary-900'>{title}</h2>
      </div>
      <div
        className={`pl-16 overflow-hidden transition-max-height duration-500 ease-in-out ${
          open ? 'max-h-56' : 'max-h-0'
        }`}
      >
        <p className='pt-2.5 text-base text-gray-500 leading-7'>{text}</p>
      </div>
    </div>
  );
};

export default Accordion;
