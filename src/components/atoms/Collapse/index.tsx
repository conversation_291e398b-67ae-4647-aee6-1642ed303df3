import { FC, ReactNode, useState } from 'react';
import { CaretDown, CaretUp } from '@phosphor-icons/react';

type TToggleType = 'bottom' | 'right';
type IToggleTextType = {
  open: string;
  close: string;
};

interface ICollapseProps {
  title: ReactNode;
  content: ReactNode;
  showPreview?: boolean;
  toggleText?: IToggleTextType;
  togglePosition?: TToggleType;
  icon?: ReactNode;
}

const Collapse: FC<ICollapseProps> = ({
  title,
  content,
  showPreview = false,
  togglePosition = 'right',
  toggleText,
  icon
}) => {
  const [open, setOpen] = useState(false);

  return (
    <div
      className={`relative flex items-start ${
        togglePosition === 'right' ? 'flex-row' : 'flex-col'
      } gap-3`}
      onClick={() => setOpen(prev => !prev)}
    >
      <div className='mt-1'>
        <div className='flex items-center gap-2'>
          <h3 className='text-sm text-primary-900 font-medium'>{title}</h3>
          {icon && icon}
        </div>
        <div
          className={`overflow-hidden transition-max-height duration-300 ease-in-out ${
            open ? 'max-h-[1000px]' : showPreview ? 'max-h-12' : 'max-h-0'
          }`}
        >
          <p className='mt-1 text-gray-500'>{content}</p>
        </div>
      </div>
      <div className='flex items-center gap-2'>
        {toggleText && <span>{open ? toggleText.close : toggleText.open}</span>}
        <div className='w-7 h-7 rounded-full bg-gray-100 flex items-center justify-center'>
          {open ? <CaretUp size={18} /> : <CaretDown size={18} />}
        </div>
      </div>
    </div>
  );
};

export default Collapse;
