import { InputHTMLAttributes, forwardRef } from 'react';

type RadioSize = 'small' | 'normal' | 'large';

interface IRadioProps
  extends Omit<InputHTMLAttributes<HTMLInputElement>, 'size'> {
  label: string;
  size?: RadioSize;
}

const Radio = forwardRef(
  (
    { id, label, onChange, size = 'normal', ...props }: IRadioProps,
    ref: any
  ) => {
    const sizeClasses = {
      small: 'w-3 h-3 text-xs',
      normal: 'w-4 h-4 text-base',
      large: 'w-6 h-6 text-lg'
    };

    const inputSizeClass = sizeClasses[size];

    return (
      <div className={`flex items-center gap-4 ${sizeClasses[size]}`}>
        <input
          {...props}
          id={id}
          ref={ref}
          type='radio'
          className={`${inputSizeClass} border-green-500`}
        />
        <label htmlFor={id} className='cursor-pointer'>
          {label}
        </label>
      </div>
    );
  }
);
Radio.displayName = 'Radio';

export default Radio;
