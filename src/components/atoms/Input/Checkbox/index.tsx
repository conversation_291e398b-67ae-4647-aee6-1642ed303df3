import { ReactNode, useEffect, useState } from 'react';
import { Check } from '@phosphor-icons/react';
import Stars from '@components/atoms/Stars';

interface ICheckbox {
  value?: boolean;
  // eslint-disable-next-line no-unused-vars
  onChange?: (checked: boolean) => void;
  label?: ReactNode;
  trailing?: string | number;
  textColor?: 'primary';
  type?: string;
}

const Checkbox = ({
  value,
  onChange,
  label,
  trailing,
  textColor,
  type
}: ICheckbox) => {
  const [checked, setChecked] = useState<boolean>(value || false);
  useEffect(() => {
    if (value !== undefined) setChecked(value);
  }, [value]);

  return (
    <div
      className='w-full flex items-center justify-between cursor-pointer gap-2 select-none'
      onClick={() => {
        setChecked(prev => !prev);
        onChange?.(!checked);
      }}
    >
      <div className='flex items-center gap-2'>
        <div
          className={`w-5 h-5 flex items-center justify-center border border-primary-500 rounded-md flex-none mt-[3px] hover:bg-primary-500 ${
            checked ? 'bg-primary-500' : ''
          }`}
        >
          {checked && <Check size={12} color='white' weight='bold' />}
        </div>
        {label && (
          <p
            className={`flex items-center gap-1 text-sm mt-1 ${
              textColor === 'primary' ? 'text-primary-900' : 'text-gray-500'
            }`}
          >
            {type === 'stars' ? (
              <Stars rate={parseFloat(label.toString())} />
            ) : (
              label
            )}
          </p>
        )}
      </div>
      {trailing !== undefined && (
        <p className='text-sm text-gray-500'>{trailing}</p>
      )}
    </div>
  );
};

export default Checkbox;
