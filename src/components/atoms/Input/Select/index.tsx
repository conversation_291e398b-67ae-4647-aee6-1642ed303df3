import { SelectHTMLAttributes, forwardRef, memo } from 'react';

export type OptionType = {
  label: string;
  value: string | number | null;
  [key: string]: any;
};

type SelectProps = Omit<SelectHTMLAttributes<HTMLSelectElement>, 'type'> & {
  id?: string;
  label?: string;
  options: OptionType[];
  selectedOption?: string | number;
  error?: string;
  focus?: Function;
};

const Select = forwardRef(
  (
    {
      id,
      label,
      options,
      selectedOption,
      error,
      disabled,
      focus,
      ...props
    }: SelectProps,
    ref: any
  ) => {
    return (
      <div className={`flex flex-col ${props.className}`}>
        <select
          id={id}
          ref={ref}
          disabled={disabled}
          value={selectedOption}
          className='flex flex-col justify-center items-center py-3 px-2 bg-white rounded-button disabled:cursor-not-allowed'
          {...props}
        >
          {options.map(option => (
            <option key={option.value} value={option.value?.toString()}>
              {option.label}
            </option>
          ))}
        </select>
        {error && <span className='text-red-600 text-sm'>{error}</span>}
      </div>
    );
  }
);

const sameOptions = (prevProps: SelectProps, nextProps: SelectProps) => {
  return (
    prevProps.options === nextProps.options &&
    prevProps.selectedOption === nextProps.selectedOption &&
    prevProps.value === nextProps.value
  );
};

export default memo(Select, sameOptions);
