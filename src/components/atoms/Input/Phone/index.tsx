import { i18n, useTranslation } from 'next-i18next';
import Input from '@components/atoms/Input/Input';
import { isValidDDD, isValidPhoneNumber } from '@utils/validation';
import {
  FieldErrors,
  FieldValues,
  Path,
  UseFormSetFocus
} from 'react-hook-form';

interface IPhoneInputProps<T extends FieldValues> {
  DDI?: string;
  phoneDDIKey: Path<T>;
  phoneAreaCodeKey: Path<T>;
  phoneNumberKey: Path<T>;
  registerWithMask: any;
  setFocus: UseFormSetFocus<T>;
  errors: FieldErrors<T>;
  ddiClassName?: string;
  areaCodeClassName?: string;
  phoneClassName?: string;
}

const PhoneInput = <T extends FieldValues>({
  DDI,
  phoneDDIKey,
  phoneAreaCodeKey,
  phoneNumberKey,
  registerWithMask,
  setFocus,
  errors,
  ddiClassName,
  areaCodeClassName,
  phoneClassName
}: IPhoneInputProps<T>) => {
  const { t } = useTranslation('common');

  return (
    <div className='w-full flex flex-col gap-1'>
      <div className='flex flex-col md:flex-row gap-0.5'>
        <Input
          className={`flex-2 rounded-b-none md:rounded-r-none md:rounded-l-button ${ddiClassName}`}
          placeholder={t('fieldsLabels.ddi')}
          {...registerWithMask(phoneDDIKey, ['+9', '+99', '+999'], {
            value: DDI,
            required: t('validations.required')
          })}
          defaultValue={i18n?.language.toLowerCase() === 'pt_br' ? '+55' : ''}
          color='gray'
        />
        <Input
          className={`flex-2 rounded-none ${areaCodeClassName}`}
          placeholder={t('fieldsLabels.areaCode')}
          {...registerWithMask(phoneAreaCodeKey, DDI === '+55' ? '(99)' : '', {
            required: t('validations.required'),
            validate: (value: any) =>
              DDI === '+55'
                ? isValidDDD(value)
                : true || t('validations.invalidPhoneAreaCode'),
            oncomplete: () => {
              if (DDI === '+55') {
                setFocus(phoneNumberKey);
              }
            }
          })}
          color='gray'
        />
        <Input
          className={`flex-5 rounded-t-none md:rounded-l-none md:rounded-r-button ${phoneClassName}`}
          placeholder={t('fieldsLabels.number')}
          {...registerWithMask(
            phoneNumberKey,
            DDI === '+55' ? '99999-9999' : '',
            {
              required: t('validations.required'),
              validate: (value: any) =>
                DDI === '+55'
                  ? isValidPhoneNumber(value)
                  : true || t('validations.invalidPhoneNumber')
            }
          )}
          color='gray'
        />
      </div>
      {((errors?.phone as any)?.ddi?.message ||
        (errors?.phone as any)?.areaCode?.message ||
        (errors?.phone as any)?.number?.message) && (
        <span className='text-red-600 text-sm'>
          {(errors?.phone as any)?.ddi?.message ||
            (errors?.phone as any)?.areaCode?.message ||
            (errors?.phone as any)?.number?.message}
        </span>
      )}
    </div>
  );
};

export default PhoneInput;
