import { Check } from '@phosphor-icons/react';
import { FC, ReactNode } from 'react';

interface IMenuButton {
  icon?: ReactNode;
  text: string;
  onClick?: Function;
  checked?: boolean;
}

const MenuButton: FC<IMenuButton> = ({
  icon,
  text,
  onClick,
  checked
}: IMenuButton) => {
  return (
    <div
      onClick={() => onClick?.()}
      className='flex gap-2 items-center justify-between whitespace-nowrap overflow-hidden cursor-pointer py-3 px-4 hover:bg-primary-100 rounded-button w-full'
    >
      <div className='flex items-center gap-2'>
        {icon}
        <p className='text-primary-900 font-medium'>{text}</p>
      </div>
      {checked && <Check size={18} />}
    </div>
  );
};

export default MenuButton;
