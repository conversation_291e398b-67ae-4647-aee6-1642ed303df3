import { FC, ReactNode, useEffect, useState } from 'react';
import Head from 'next/head';
import Navbar, { NavbarStyle } from '@components/molecules/Navbar';
import Footer from '@components/molecules/Footer';
import { useRouter } from 'next/router';
import { MetaDataTag } from 'src/types';
import BottomNavigationBar from '@components/organisms/BottomNavigationBar';
import { cva } from 'class-variance-authority';
import { cn } from '@utils/classes';

const containerVariants = cva('relative', {
  variants: {
    backgroundColor: {
      white: 'bg-white',
      gray: 'bg-gray-100'
    }
  },
  defaultVariants: {
    backgroundColor: 'gray'
  }
});

const headerVariants = cva('flex items-center gap-4 mb-12', {
  variants: {
    spacing: {
      true: 'mt-12'
    }
  }
});

const contentVariants = cva('flex gap-12', {
  variants: {
    spacing: {
      true: 'mt-12'
    }
  }
});

type TemplateProps = {
  children: ReactNode;
  title: string;
  image?: string;
  description: string;
  navbar: NavbarStyle;
  navbarHiddenOnMobile?: boolean;
  backgroundColor?: 'white' | 'gray';
  mobileBottomNavigationBar?: boolean;
  leftContent?: ReactNode;
  headerContent?: ReactNode;
  preContent?: ReactNode;
  pageTitle?: string;
  pageDescription?: string;
  pageIcon?: ReactNode;
  extraTags?: MetaDataTag[];
  host: string;
};

const Template: FC<TemplateProps> = ({
  title,
  description,
  image,
  children,
  navbar,
  navbarHiddenOnMobile,
  backgroundColor,
  mobileBottomNavigationBar,
  leftContent,
  headerContent,
  preContent,
  pageTitle,
  pageDescription,
  pageIcon,
  extraTags,
  host,
  ...props
}) => {
  const router = useRouter();
  const [scrolling, setScrolling] = useState<boolean>(false);

  const canonicalUrl = `https://${host}${router.asPath}`;

  useEffect(() => {
    const onScroll = () => {
      const scrollCheck = window.scrollY > 10;
      if (scrollCheck !== scrolling) {
        setScrolling(!scrolling);
      }
    };
    document.addEventListener('scroll', onScroll);
    return () => {
      document.removeEventListener('scroll', onScroll);
    };
  }, [scrolling]);

  return (
    <div className={cn(containerVariants({ backgroundColor }))} {...props}>
      <Head>
        <title>{title}</title>
        <meta charSet='utf-8' />
        <meta
          name='viewport'
          content='width=device-width, initial-scale=1, shrink-to-fit=no'
        />
        <meta name='description' content={description} />
        <meta name='author' content='OurTrip' />

        <link rel='canonical' href={canonicalUrl} />

        <meta property='og:type' content='website' />
        <meta property='og:url' content={canonicalUrl} />
        <meta property='og:title' content={title} />
        <meta property='og:description' content={description} />
        <meta
          property='og:image'
          content={
            image ||
            'https://ourtrips3.s3.amazonaws.com/new-logos/horizontal_icon_text_blue_1_1_white.png'
          }
        />
        <meta property='og:site_name' content='OurTrip' />
        <meta property='og:locale' content={router.locale || 'pt-BR'} />

        <meta name='twitter:card' content='summary_large_image' />
        <meta name='twitter:url' content={canonicalUrl} />
        <meta name='twitter:title' content={title} />
        <meta name='twitter:description' content={description} />
        <meta
          name='twitter:image'
          content={
            image ||
            'https://ourtrips3.s3.amazonaws.com/new-logos/horizontal_icon_text_blue_1_1_white.png'
          }
        />

        <link rel='icon' href='/favicon.ico' />
        <link
          rel='apple-touch-icon'
          sizes='180x180'
          href='/apple-touch-icon.png'
        />
        <link rel='manifest' href='/manifest.json' />
        <meta name='theme-color' content='#3554D1' />

        {/* Alternate Languages */}
        {router.locales?.map(locale => (
          <link
            key={locale}
            rel='alternate'
            hrefLang={locale.replace('_', '-')}
            href={
              locale === 'pt_BR'
                ? `https://ourtrip.com.br${router.asPath}`
                : `https://ourtrip.global${
                    locale === router.defaultLocale ? '' : `/${locale}`
                  }${router.asPath}`
            }
          />
        ))}

        {/* Custom Extra Tags */}
        {extraTags?.map(tag => (
          <meta key={tag.content} name={tag.name} content={tag.content} />
        ))}
      </Head>
      <div className={`${navbarHiddenOnMobile ? 'hidden md:block' : ''}`}>
        <Navbar
          fixed={navbar.fixed}
          backgroundColor={navbar.backgroundColor}
          foregroundColor={navbar.foregroundColor}
          scrollingBackgroundColor={navbar.scrollingBackgroundColor}
          scrollingForegroundColor={navbar.scrollingForegroundColor}
        />
      </div>
      {preContent && preContent}
      {leftContent && (
        <div className='container py-14 bg-gray-100'>
          {headerContent && (
            <div
              className={cn(
                headerVariants({
                  spacing: navbar.fixed || navbar.fixed == null
                })
              )}
            >
              {headerContent}
            </div>
          )}
          {pageTitle && pageDescription && pageIcon && (
            <div
              className={cn(
                headerVariants({
                  spacing: navbar.fixed || navbar.fixed == null
                })
              )}
            >
              {pageIcon && (
                <div className='w-[50px] h-[50px] rounded-full bg-gray-200 flex items-center justify-center'>
                  <div className='text-primary-900'>{pageIcon}</div>
                </div>
              )}
              <div>
                {pageTitle && (
                  <h2 className='text-2xl font-semibold text-primary-900 mb-[7px] leading-none'>
                    {pageTitle}
                  </h2>
                )}
                {pageDescription && (
                  <p className='text-gray-500 text-base leading-none'>
                    {pageDescription}
                  </p>
                )}
              </div>
            </div>
          )}
          <div
            className={cn(
              contentVariants({
                spacing:
                  (navbar.fixed || navbar.fixed == null) &&
                  !pageTitle &&
                  !pageDescription &&
                  !pageIcon
              })
            )}
          >
            {leftContent && (
              <div className='flex-2 hidden md:block'>{leftContent}</div>
            )}
            <div className='flex-6'>{children}</div>
          </div>
        </div>
      )}
      {!leftContent && children}
      <Footer host={host} />
      {mobileBottomNavigationBar && <BottomNavigationBar />}
    </div>
  );
};

export default Template;
