/* eslint-disable react/no-array-index-key */
import React, { FC, ReactNode, useContext } from 'react';
import Head from 'next/head';
import Navbar, { NavbarStyle } from '@components/molecules/Navbar';
import Footer from '@components/molecules/Footer';
import Menu, { MenuItem } from '@components/molecules/Menu';
import { HandCoins, Lock, UserCircle, UserList } from '@phosphor-icons/react';
import { useRouter } from 'next/router';
import TripcashCard from '@components/molecules/TripcashCard';
import { AuthContext } from 'src/context/AuthContext';
import { GetServerSideProps, GetServerSidePropsContext } from 'next';
import { MetaDataTag } from 'src/types';
import { useTranslation } from 'next-i18next';
import BottomNavigationBar from '@components/organisms/BottomNavigationBar';
import TripcashCardBalance from '@components/molecules/TripcashCardBalance';

type AccountTemplateProps = {
  children: ReactNode;
  title: string;
  description: string;
  navbar: NavbarStyle;
  leftContent?: ReactNode;
  headerContent?: ReactNode;
  preContent?: ReactNode;
  pageTitle?: string;
  pageDescription?: string;
  pageIcon?: ReactNode;
  url?: string;
  extraTags?: MetaDataTag[];
  host: string;
};

const AccountTemplate: FC<AccountTemplateProps> = ({
  title,
  description,
  children,
  navbar,
  leftContent,
  headerContent,
  preContent,
  pageTitle,
  pageDescription,
  pageIcon,
  url,
  extraTags,
  host,
  ...props
}) => {
  const { t, i18n } = useTranslation(['account', 'common'], {
    nsMode: 'fallback'
  });
  const router = useRouter();
  const { user } = useContext(AuthContext);
  const canonicalUrl =
    process.env.NODE_ENV === 'development'
      ? `https://hml.ourtrip.com.br${router.asPath}`
      : `https://ourtrip.com.br${router.asPath}`;

  const accountMenuItems: MenuItem[] = [
    {
      text: t('account.menu.profile'),
      icon: <UserCircle size={20} />,
      route: t('routes.myProfile'),
      onClick: () =>
        router.push({ pathname: t('routes.myProfile') }, '', {
          locale: i18n.language
        })
    },
    {
      text: t('account.menu.reservations'),
      icon: <UserList size={20} />,
      route: t('routes.myReservations'),
      onClick: () =>
        router.push({ pathname: t('routes.myReservations') }, '', {
          locale: i18n.language
        })
    },
    {
      text: t('account.tripcash.title'),
      icon: <HandCoins size={20} />,
      route: t('routes.myTripcash'),
      onClick: () =>
        router.push({ pathname: t('routes.myTripcash') }, '', {
          locale: i18n.language
        })
    },
    {
      text: t('account.menu.changePassword'),
      icon: <Lock size={20} />,
      route: t('routes.passwordChange'),
      onClick: () =>
        router.push({ pathname: t('routes.passwordChange') }, '', {
          locale: i18n.language
        })
    }
  ];

  return (
    <div className='bg-gray-100' {...props}>
      <Head>
        <title>{title}</title>
        <meta name='description' content={description} />
        {extraTags?.map(tag => (
          <meta key={tag.content} name={tag.name} content={tag.content} />
        ))}
        <link rel='canonical' href={canonicalUrl} />
      </Head>
      <div className='hidden md:block'>
        <Navbar
          fixed={navbar.fixed}
          backgroundColor={navbar.backgroundColor}
          foregroundColor={navbar.foregroundColor}
          scrollingBackgroundColor={navbar.scrollingBackgroundColor}
          scrollingForegroundColor={navbar.scrollingForegroundColor}
        />
      </div>
      <div className='container pt-7 md:pt-0 md:mt-12 pb-24 md:pb-0'>
        <div className='flex flex-col md:flex-row gap-6'>
          <div className='hidden md:flex flex-col gap-6 flex-2'>
            <TripcashCard tripcashBalance={user!.tripcashBalances} />
            <Menu title={t('account.menu.title')} items={accountMenuItems} />
          </div>
          <div className='flex md:hidden flex-col gap-2'>
            <div className='flex flex-col bg-white p-4 rounded-default gap-4'>
              <div className='flex items-center gap-4'>
                <div className='w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center'>
                  <UserCircle size={24} className='text-primary-900' />
                </div>
                <div>
                  <h3 className='text-primary-900 font-medium'>{`${user.name} ${user.surname}`}</h3>
                  <p className='text-sm text-gray-500'>{user!.email}</p>
                </div>
              </div>
              <TripcashCardBalance
                label={t('navbar.tripcash.balance')}
                values={user!.tripcashBalances}
              />
            </div>
            <div className='flex gap-2'>
              {accountMenuItems.map((item, index) => (
                <div
                  key={index}
                  className={`flex flex-col items-center bg-white gap-2 w-full p-4 rounded-default ${
                    router.asPath === item.route
                      ? 'border-b-4 border-primary-500 rounded-b-md'
                      : ''
                  }`}
                  onClick={() => item.onClick?.()}
                >
                  {item.icon}
                  <p className='text-center text-sm'>{item.text}</p>
                </div>
              ))}
            </div>
          </div>
          <div className='flex-5'>{children}</div>
        </div>
      </div>
      <div className='hidden md:block'>
        <Footer host={host} />
      </div>
      <BottomNavigationBar />
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async (
  ctx: GetServerSidePropsContext
) => {
  return {
    props: {
      url: ctx.req.url
    }
  };
};

export default AccountTemplate;
