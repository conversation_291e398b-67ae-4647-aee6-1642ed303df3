import Modal from '@components/atoms/Modal';
import { FC, useState } from 'react';
import Switch from '@components/atoms/Switch';
import { ECookieTypes } from 'src/types';
import Divider from '@components/atoms/Divider';
import Button from '@components/atoms/Button';
import { mapCookieTypesToString } from 'src/utils/cookies';
import { useTranslation } from 'next-i18next';
import { ICookiesPreferencesModalProps } from './types';

const CookiePreferencesModal: FC<ICookiesPreferencesModalProps> = ({
  open,
  savePreferences,
  onClose
}) => {
  const { t } = useTranslation('common');
  const [acceptedCookies, setAcceptedCookies] = useState<
    Record<ECookieTypes, boolean>
  >({
    [ECookieTypes.ANALYTICS]: true,
    [ECookieTypes.PUBLICITY]: true,
    [ECookieTypes.FUNCTIONAL]: true,
    [ECookieTypes.REQUIRED]: true
  });
  const handleToggleCookie = (type: ECookieTypes) => {
    setAcceptedCookies(prev => {
      return { ...prev, [type]: !prev[type] };
    });
  };

  const handleSavePreferences = ({ saveAll }: { saveAll: boolean }) => {
    const cookiesToSave = saveAll
      ? undefined
      : mapCookieTypesToString(acceptedCookies);
    savePreferences(cookiesToSave);
  };
  return (
    <Modal closeOnClickOutside isOpen={open} onClose={() => onClose()}>
      <div className='relative'>
        <div className='p-12 flex flex-col max-h-[75vh] max-w-[550px] overflow-y-auto'>
          <div className='text-center flex flex-col gap-3'>
            <h2 className='font-medium text-primary-900 text-xl'>
              {t('cookies.preferences.title')}
            </h2>
            <Divider orientation='horizontal' />
          </div>
          <div className='flex flex-col gap-3'>
            <div className='flex items-center justify-between mt-6'>
              <h3 className='font-medium text-primary-900 text-base'>
                {t('cookies.preferences.necessaryCookies.title')}
              </h3>
              <Switch
                on
                disabled
                label={t('cookies.preferences.necessaryCookies.title')}
              />
            </div>
            <p className='max-w-[90%] font-normal text-gray-500 text-base'>
              {t('cookies.preferences.necessaryCookies.description')}
            </p>
          </div>
          <div className='flex flex-col gap-3'>
            <div className='flex items-center justify-between mt-6'>
              <h3 className='font-medium text-primary-900 text-base'>
                {t('cookies.preferences.analyticsCookies.title')}
              </h3>
              <Switch
                on={acceptedCookies[ECookieTypes.ANALYTICS]}
                onChange={() => handleToggleCookie(ECookieTypes.ANALYTICS)}
                label={t('cookies.preferences.analyticsCookies.title')}
              />
            </div>
            <p className='max-w-[90%] font-normal text-gray-500 text-base'>
              {t('cookies.preferences.analyticsCookies.description')}
            </p>
          </div>
          <div className='flex flex-col gap-3'>
            <div className='flex items-center justify-between mt-6'>
              <h3 className='font-medium text-primary-900 text-base'>
                {t('cookies.preferences.advertisingCookies.title')}
              </h3>
              <Switch
                on={acceptedCookies[ECookieTypes.PUBLICITY]}
                onChange={() => handleToggleCookie(ECookieTypes.PUBLICITY)}
                label={t('cookies.preferences.advertisingCookies.title')}
              />
            </div>
            <p className='max-w-[90%] font-normal text-gray-500 text-base'>
              {t('cookies.preferences.advertisingCookies.description')}
            </p>
          </div>
          <div className='flex flex-col gap-3'>
            <div className='flex items-center justify-between mt-6'>
              <h3 className='font-medium text-primary-900 text-base'>
                {t('cookies.preferences.functionalCookies.title')}
              </h3>
              <Switch
                on={acceptedCookies[ECookieTypes.FUNCTIONAL]}
                onChange={() => handleToggleCookie(ECookieTypes.FUNCTIONAL)}
                label={t('cookies.preferences.functionalCookies.title')}
              />
            </div>
            <p className='max-w-[90%] font-normal text-gray-500 text-base'>
              {t('cookies.preferences.functionalCookies.description')}
            </p>
          </div>
          <div className='flex flex-col gap-3'>
            <div className='flex items-center justify-between mt-6'>
              <h4 className='font-medium text-primary-900 text-base'>
                {t('cookies.preferences.whatIsCookies.title')}
              </h4>
            </div>
            <p className='max-w-[90%] font-normal text-gray-500 text-base'>
              {t('cookies.preferences.whatIsCookies.description')}
            </p>
          </div>
        </div>
        <div className='sticky bottom-0 left-0 bg-white w-full py-6 px-12 flex items-center justify-between shadow-lg rounded-b-default'>
          <Button
            variant='outline'
            onClick={() => handleSavePreferences({ saveAll: false })}
          >
            {t('cookies.preferences.save')}
          </Button>
          <Button onClick={() => handleSavePreferences({ saveAll: true })}>
            {t('cookies.preferences.acceptAll')}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default CookiePreferencesModal;
