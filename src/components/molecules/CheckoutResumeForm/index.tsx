/* eslint-disable react/no-array-index-key */
/* eslint-disable no-unused-vars */
import Link from 'next/link';
import { FC, useMemo, useState } from 'react';
import {
  CheckoutFields,
  CheckoutStepEnum,
  PaymentMethodEnum,
  PaymentMethodType
} from 'src/types/checkout';
import { mountInstallmentOptions } from 'src/utils/checkout';
import { ICartInstallments, ICartRoomDetails } from 'src/types/cart';
import Button from '@components/atoms/Button';
import { At, CreditCard, HandCoins, PixLogo } from '@phosphor-icons/react';
import Switch from '@components/atoms/Switch';
import { Checkbox, Select } from '@components/atoms/Input';
import CheckoutRoomResumeForm from '@components/molecules/CheckoutRoomResumeForm';
import { i18n, useTranslation } from 'next-i18next';

interface ICheckoutResumeForm {
  totalPrice: string;
  checkoutFields: CheckoutFields;
  roomDetails: ICartRoomDetails[];
  paymentMethod: PaymentMethodType;
  handleSubmit: Function;
  loading: boolean;
  appliedTripcash: boolean;
  handleEditStep: (step: number) => void;
  onChangeTripcash: (value: boolean) => void;
  tripcashDiscount?: string;
  installments: ICartInstallments[];
  setInstallmentsValues: (value?: number) => void;
  selectedInstallment: number;
}

const CheckoutResumeForm: FC<ICheckoutResumeForm> = ({
  totalPrice,
  checkoutFields,
  roomDetails,
  paymentMethod,
  handleSubmit,
  loading,
  appliedTripcash,
  tripcashDiscount,
  installments,
  onChangeTripcash,
  handleEditStep,
  setInstallmentsValues,
  selectedInstallment
}) => {
  const { t } = useTranslation(['common', 'checkout'], { nsMode: 'fallback' });
  const [terms, setTerms] = useState<boolean>(false);
  const [roomTerms, setRoomTerms] = useState<boolean>(
    roomDetails.flatMap(room => room.rateComments ?? []).length === 0
  );

  const installmentOptions = useMemo(
    () => mountInstallmentOptions(installments),
    [installments]
  );

  const paymentMethods = {
    [PaymentMethodEnum.CREDIT_CARD]: (
      <div className='w-full flex flex-col md:flex-row gap-2 items-start text-left md:gap-6'>
        <div className='w-full flex gap-2 items-center'>
          <div className='w-12 h-12 flex-none flex justify-center items-center rounded-full bg-primary-100'>
            <CreditCard
              size={32}
              weight='duotone'
              className='text-primary-900'
            />
          </div>
          <div className='w-full flex flex-col md:flex-row gap-1 justify-between'>
            <div className='flex flex-col gap-1'>
              <h4 className='font-medium text-primary-900'>
                {t('payment.method.creditCard')}
              </h4>
              <p className='text-sm text-gray-500'>
                {`**** **** **** ${checkoutFields.creditCard.number.substring(
                  checkoutFields.creditCard.number.length - 4
                )}`}
              </p>
            </div>
            <div className='hidden md:block'>
              <Select
                label={t('payment.method.installment')}
                options={installmentOptions}
                selectedOption={selectedInstallment}
                onChange={ev =>
                  setInstallmentsValues(parseInt(ev.target.value, 10))
                }
              />
            </div>
          </div>
        </div>
        <div className='block md:hidden w-full'>
          <Select
            label={t('payment.method.installment')}
            options={installmentOptions}
            selectedOption={selectedInstallment}
            className='bg-gray-100 rounded-button p-1'
            onChange={ev =>
              setInstallmentsValues(parseInt(ev.target.value, 10))
            }
          />
        </div>
      </div>
    ),
    [PaymentMethodEnum.PIX]: (
      <div className='flex gap-2 items-center text-left md:gap-6'>
        <div className='w-12 h-12 flex justify-center items-center rounded-full bg-primary-100'>
          <PixLogo size={24} />
        </div>
        <div className='flex flex-col'>
          <h4 className='font-medium text-primary-900'>
            {t('payment.step.pix')}
          </h4>
          <p className='text-sm text-gray-500'>{checkoutFields.pix.email}</p>
        </div>
      </div>
    ),
    [PaymentMethodEnum.EXTERNAL]: (
      <div className='flex flex-col gap-2 items-start text-left md:gap-6 md:flex-row'>
        <div className='flex flex-col'>
          <h4 className='font-medium text-primary-900'>
            {t('payment.step.external')}
          </h4>
          <p className='text-sm text-gray-500 flex items-center gap-1'>
            <At className='text-primary-900' />
            {checkoutFields.external.email}
          </p>
        </div>
      </div>
    )
  };

  return (
    <div className='flex flex-col gap-6'>
      <div className='flex flex-col gap-3'>
        <div className='flex flex-col gap-1'>
          <h3 className='text-sm text-gray-500'>{t('room.title')}</h3>
          <div className='flex flex-col gap-3'>
            {checkoutFields.room.map((room, index) => (
              <CheckoutRoomResumeForm
                t={t}
                key={index}
                room={room}
                handleEditStep={() => handleEditStep(CheckoutStepEnum.GUESTS)}
                roomName={roomDetails[index].accommodationName}
                loading={loading}
              />
            ))}
          </div>
        </div>
        <div className='flex flex-col gap-1'>
          <h3 className='text-sm text-gray-500'>{t('payment.method.title')}</h3>
          <div className='flex flex-col md:flex-row md:items-center justify-between bg-white p-6 rounded-default gap-3'>
            {paymentMethods[paymentMethod]}
            <Button
              onClick={() => handleEditStep(CheckoutStepEnum.PAYMENT)}
              color='primary'
              variant='outline'
              disabled={loading}
              size='small'
            >
              {t('room.change')}
            </Button>
          </div>
        </div>
        {tripcashDiscount ? (
          <div className='flex flex-col gap-1'>
            <h3 className='text-gray-500'>{t('tripcash.title')}</h3>
            <div
              className='flex flex-col md:flex-row justify-between bg-white p-6 rounded-default gap-3 cursor-pointer'
              onClick={() => onChangeTripcash(!appliedTripcash)}
            >
              <div className='flex flex-col gap-2 items-center text-center md:gap-6 md:flex-row md:text-left'>
                <div className='w-12 h-12 flex justify-center items-center rounded-button bg-primary-100'>
                  <HandCoins size={32} className='text-primary-900' />
                </div>
                <div className='flex flex-col'>
                  <h4 className='font-medium text-primary-900'>
                    {t('tripcash.use')}
                  </h4>
                  <p className='text-gray-500 text-sm'>
                    {t('tripcash.discount')}:{' '}
                    <strong>{tripcashDiscount}</strong>
                  </p>
                </div>
              </div>
              <Switch on={appliedTripcash} label='Tripcash' />
            </div>
          </div>
        ) : null}
      </div>
      <div className='flex flex-col gap-1'>
        <Checkbox
          value={terms}
          onChange={e => setTerms(e)}
          label={
            <>
              {t('terms.accept')}{' '}
              <Link
                href={t('routes.termsAndConditions')}
                className='text-primary-900 underline'
                target='_blank'
              >
                {t('terms.termsAndConditions')}
              </Link>{' '}
              {t('terms.and')}{' '}
              <Link
                href={t('routes.privacyPolicy')}
                className='text-primary-900 underline'
                target='_blank'
              >
                {t('terms.privacyPolicy')}
              </Link>
            </>
          }
        />
        {roomDetails.flatMap(room => room.rateComments ?? []).length > 0 && (
          <>
            <Checkbox
              value={roomTerms}
              onChange={e => setRoomTerms(e)}
              label={t('terms.roomTerms')}
            />
            <div className='text-sm text-gray-500 flex flex-col gap-2 mt-1 bg-white p-3 rounded-default'>
              {roomDetails.map((room, index) => (
                <div key={index} className='flex flex-col gap-1'>
                  <p className='text-primary-900 font-medium'>
                    {t('room.index', { index: index + 1 })}
                  </p>
                  {room.rateComments?.map((comment, commentIndex) => (
                    <p key={commentIndex}>{comment}</p>
                  ))}
                </div>
              ))}
            </div>
          </>
        )}
      </div>
      <div className='flex justify-between'>
        <div />
        <Button
          onClick={() => handleSubmit()}
          style={{ width: '300px' }}
          size='large'
          loading={loading}
          disabled={!terms || !roomTerms}
          className='w-full md:w-auto'
        >
          {t('buttons.book')} <span>{totalPrice}</span>
        </Button>
      </div>
    </div>
  );
};

export default CheckoutResumeForm;
