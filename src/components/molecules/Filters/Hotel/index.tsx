/* eslint-disable no-unused-vars */
import { FC, useRef } from 'react';
import { Input } from '@components/atoms/Input';
import Button from '@components/atoms/Button';
import { useTranslation } from 'next-i18next';

interface IHotelFilter {
  loading: boolean;
  applyFilter: (value: string) => void;
}

const HotelFilter: FC<IHotelFilter> = ({ loading, applyFilter }) => {
  const { t } = useTranslation('hotel');
  const inputRef = useRef<HTMLInputElement>(null);

  return (
    <div className='flex flex-col gap-4'>
      <Input
        ref={inputRef}
        disabled={loading}
        placeholder={t('filters.hotelName')}
      />
      <Button
        color='primary'
        variant='outline'
        disabled={loading}
        onClick={() => applyFilter(inputRef?.current?.value ?? '')}
      >
        {t('filters.apply')}
      </Button>
    </div>
  );
};

export default HotelFilter;
