/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable react/no-array-index-key */
import { Checkbox } from '@components/atoms/Input';
import { ICheckBoxFilter } from 'src/types/filters';
import { useState } from 'react';
import { useTranslation } from 'next-i18next';

const CheckboxFilter = ({
  id,
  display,
  options,
  onChange,
  step
}: ICheckBoxFilter & { step?: number }) => {
  const { t } = useTranslation('common');
  const [page, setPage] = useState<number>(1);
  const [pages] = useState<number>(
    Math.ceil((options?.length || 10) / (step || 7))
  );

  return (
    <div className='w-full flex flex-col gap-2.5'>
      <h3 className='font-medium text-primary-900'>{display}</h3>
      {options?.slice(0, page * (step || 7)).map(option => (
        <Checkbox
          key={`${option.display}`}
          label={option.display}
          value={option.applied}
          trailing={option.quantity}
          textColor='primary'
          onChange={checked => onChange(id, option.id, checked)}
          type={id}
        />
      ))}
      <div className='flex items-center justify-between'>
        {options && options?.length > (step || 7) ? (
          <p
            className='text-primary-900 text-sm leading-8 underline cursor-pointer hover:text-primary-500'
            onClick={() => {
              if (page !== pages) {
                setPage(prev => prev + 1);
              } else {
                setPage(1);
              }
            }}
          >
            {page !== pages
              ? `${t('search.filters.expand')} ${
                  options.length - page * (step || 7) > (step || 7)
                    ? step || 7
                    : options.length - page * (step || 7)
                }`
              : t('search.filters.collapse')}
          </p>
        ) : null}
        {page > 1 ? (
          <p
            className='text-primary-500 text-xs leading-8 cursor-pointer hover:text-primary-500'
            onClick={() => setPage(1)}
          >
            {t('search.filters.collapseAll')}
          </p>
        ) : null}
      </div>
    </div>
  );
};

export default CheckboxFilter;
