import { Range } from '@components/atoms/Input';
import { IRangeFilter } from 'src/types/filters';

const RangeFilter = ({
  display,
  min,
  max,
  onChange,
  disabled,
  initialValues
}: IRangeFilter) => {
  const handleChange = (value: number[]) => {
    const [newMinValue, newMaxValue] = value;
    onChange(newMinValue === min && newMaxValue === max ? undefined : value);
  };

  return (
    <div className='flex flex-col'>
      {display && (
        <h3 className='font-medium mb-3 text-primary-900'>{display}</h3>
      )}
      <Range
        initialValues={initialValues}
        disabled={disabled}
        min={min}
        max={max}
        onChange={handleChange}
      />
    </div>
  );
};

export default RangeFilter;
