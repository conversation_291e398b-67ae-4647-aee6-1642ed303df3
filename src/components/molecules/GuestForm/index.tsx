/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/no-array-index-key */
/* eslint-disable no-unused-vars */
import Button from '@components/atoms/Button';
import { Checkbox } from '@components/atoms/Input';
import React, { FC } from 'react';
import { useForm } from 'react-hook-form';
import { useHookFormMask } from 'use-mask-input';
import { isCNPJ } from 'src/utils/validation';
import {
  GuestFormType,
  GuestRoomType,
  PaymentMethodType
} from 'src/types/checkout';
import { ArrowRight } from '@phosphor-icons/react';
import { ICartRoomDetails } from 'src/types/cart';
import { useTranslation } from 'next-i18next';
import { ICustomer } from 'src/types/customer';
import CheckoutRoomGuestForm from '../CheckoutRoomGuestForm';

interface IGuestFormProps {
  user: ICustomer;
  handleGuestSubmit: (data: GuestFormType) => void;
  toPreviousStep: Function;
  rooms: ICartRoomDetails[];
  initialValues: GuestRoomType[];
  isAuthenticated: boolean;
  paymentMethod: PaymentMethodType;
  userAsFirstGuest: boolean;
  setUserAsFirstGuest: (value: boolean) => void;
}

const GuestForm: FC<IGuestFormProps> = ({
  user,
  rooms,
  handleGuestSubmit,
  toPreviousStep,
  initialValues,
  isAuthenticated,
  paymentMethod,
  userAsFirstGuest,
  setUserAsFirstGuest
}) => {
  const { t } = useTranslation('checkout');
  const {
    register,
    handleSubmit,
    reset,
    watch,
    getValues,
    formState: { errors }
  } = useForm<GuestFormType & { userAsFirstGuest: boolean }>({
    defaultValues: { room: initialValues, userAsFirstGuest },
    mode: 'onBlur',
    reValidateMode: 'onBlur'
  });

  const localUserAsFirstGuest = watch('userAsFirstGuest');
  const registerWithMask = useHookFormMask(register);

  const handleUserAsFirstGuest = (checked: boolean) => {
    let updatedRoomData: GuestRoomType[];

    if (checked) {
      updatedRoomData = getValues('room');

      updatedRoomData[0].adult[0] = {
        name: `${user.name} ${user.surname}`,
        birthday: user.birthday,
        age: undefined
      };
      updatedRoomData[0].document = user.documentNumber;
    } else {
      const emptyAdultGuest = {
        name: '',
        birthday: undefined,
        age: undefined
      };
      const emptyDocumentValue = '';

      updatedRoomData = getValues('room').map((room, roomIndex) => {
        const newAdultsArray = room.adult.map((adult, adultIndex) => {
          if (adultIndex === 0) {
            return emptyAdultGuest;
          }
          return adult;
        });

        if (roomIndex === 0) {
          return {
            ...room,
            document: emptyDocumentValue,
            adult: newAdultsArray
          };
        }
        return {
          ...room,
          adult: newAdultsArray
        };
      });
    }

    reset({
      room: updatedRoomData,
      userAsFirstGuest: checked
    });
  };

  const onSubmit = (data: GuestFormType) => {
    setUserAsFirstGuest(getValues('userAsFirstGuest'));
    handleGuestSubmit(data);
  };

  return (
    <form onSubmit={handleSubmit(data => onSubmit(data))}>
      {isAuthenticated && !isCNPJ(initialValues[0]?.document) ? (
        <div className='flex flex-col mt-4'>
          <h3 className='font-medium text-primary-900'>{t('guests.data')}</h3>
          <Checkbox
            label={t('payer.useMyInfo')}
            value={localUserAsFirstGuest}
            onChange={value => handleUserAsFirstGuest(value)}
          />
        </div>
      ) : null}
      {rooms.map((room, roomIndex) => {
        return (
          <CheckoutRoomGuestForm
            t={t}
            key={roomIndex}
            room={room}
            index={roomIndex}
            paymentMethod={paymentMethod}
            registerWithMask={registerWithMask}
            register={register}
            errors={errors}
          />
        );
      })}
      <div className='flex justify-between gap-4'>
        <Button
          size='large'
          type='button'
          color='primary'
          variant='outline'
          onClick={() => toPreviousStep()}
        >
          {t('buttons.back')}
        </Button>
        <Button size='large' type='submit' className='w-full md:w-auto'>
          {t('buttons.next')}
          <ArrowRight />
        </Button>
      </div>
    </form>
  );
};

export default GuestForm;
