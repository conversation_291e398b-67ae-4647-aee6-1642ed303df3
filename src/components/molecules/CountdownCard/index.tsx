/* eslint-disable no-unused-vars */
import { ReactElement, useEffect, useState } from 'react';
import { convertSecondsToMinutes } from 'src/utils/date';
import { Timer } from '@phosphor-icons/react';
import { useTranslation } from 'next-i18next';

interface ICountdownCard {
  start: number;
  onCountdownEnd: Function;
  render?: (time: string) => ReactElement;
}

const CountdownCard = ({ start, onCountdownEnd, render }: ICountdownCard) => {
  const { t } = useTranslation('checkout');
  const [checkoutCountdown, setCheckoutCountdown] = useState<number>(
    start > 0 ? start : 0
  );

  useEffect(() => {
    const timer = setInterval(() => {
      if (checkoutCountdown > 0) setCheckoutCountdown(prev => prev - 1);
      if (checkoutCountdown === 0) onCountdownEnd();
    }, 1000);

    return () => clearInterval(timer);
  }, [checkoutCountdown, onCountdownEnd]);

  if (render) {
    return render(convertSecondsToMinutes(checkoutCountdown));
  }

  return (
    <div className='flex items-center justify-between p-6 bg-white grow rounded-default'>
      <div className='flex items-center gap-2'>
        <Timer size={22} weight='duotone' />
        <h2 className='text-primary-900'>{t('timeRemaining')}</h2>
      </div>
      <p className='text-gray-500'>
        {convertSecondsToMinutes(checkoutCountdown)}
      </p>
    </div>
  );
};

export default CountdownCard;
