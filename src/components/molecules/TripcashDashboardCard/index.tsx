/* eslint-disable react/no-array-index-key */
import { FC, ReactNode } from 'react';
import Divider from '@components/atoms/Divider';
import classNames from 'classnames';
import { TripcashAmount } from 'src/types/tripcash';

interface ITripcashDashboardCard {
  icon: ReactNode;
  label: string;
  description: string;
  values: TripcashAmount[];
  color: 'success' | 'warning' | 'info';
}

const TripcashDashboardCard: FC<ITripcashDashboardCard> = ({
  icon,
  label,
  description,
  values,
  color
}: ITripcashDashboardCard) => {
  const classes = classNames({
    'text-green-600': color === 'success',
    'text-orange-600': color === 'warning',
    'text-primary-600': color === 'info'
  });

  return (
    <div className='w-full flex flex-col bg-white p-6 rounded-default gap-4'>
      <div className='flex justify-start items-center gap-3'>
        <div className='w-[40px] h-[40px] rounded-full flex justify-center items-center bg-gray-100'>
          {icon}
        </div>
        <div className='flex flex-col'>
          <p className='text-gray-500 text-sm'>{label}</p>
          <h3 className={`font-medium whitespace-nowrap ${classes}`}>
            {description}
          </h3>
        </div>
      </div>
      <div className='w-full flex justify-between items-center'>
        {!values.length && (
          <p className='text-primary-900 text-lg font-semibold'>-</p>
        )}
        {values.map((value, index) => (
          <div key={index} className='flex items-center gap-2'>
            <p className='font-semibold whitespace-nowrap text-primary-900 ml-2'>
              {value.formattedValue}
            </p>
            {index !== values.length - 1 && (
              <Divider orientation='vertical' className='h-6' />
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default TripcashDashboardCard;
