import { FC, useContext, useEffect } from 'react';
import Divider from '@components/atoms/Divider';
import { IHotelAmenitiesGroup, IHotelOffer } from 'src/types/hotel';
import { OffersContext } from 'src/context/OffersContext';
import { convertNumberToCurrency } from 'src/utils/currency';
import Badge from '@components/atoms/Badge';

import {
  convertStringToDefaultFormatedDate,
  getDateFNSLocale
} from 'src/utils/date';
import {
  Check,
  CheckSquare,
  Fire,
  ForkKnife,
  Tag,
  Square,
  Warning
} from '@phosphor-icons/react';
import { i18n, useTranslation } from 'next-i18next';
import Button from '@components/atoms/Button';
import ImageCarousel from '@components/molecules/ImageCarousel';
import { useCurrency } from 'src/context/CurrencyContext';

export type HotelAvailableRoomType = {
  amenitiesGroups: IHotelAmenitiesGroup[];
  accommodationName: string;
  photoCover?: string;
  photos: string[];
  cancellationDate?: string;
  mealPlan: string;
  offers: IHotelOffer[];
  refundable: boolean;
};

interface IHotelAvailableRoom extends HotelAvailableRoomType {}

const HotelAvailableRoom: FC<IHotelAvailableRoom> = ({
  photoCover,
  photos,
  amenitiesGroups,
  accommodationName,
  cancellationDate,
  mealPlan,
  offers,
  refundable
}: IHotelAvailableRoom) => {
  const { currency } = useCurrency();
  const { t } = useTranslation('hotel');
  const { selectedOffers, setSelectedOffer } = useContext(OffersContext);

  const cancellation = t('availableRooms.content.freeCancellation', {
    date: convertStringToDefaultFormatedDate(
      getDateFNSLocale(i18n?.language!),
      cancellationDate
    )
  });

  useEffect(() => {
    offers.forEach(offer => {
      if (offer.isSelected && !selectedOffers[offer.accommodationIndex]) {
        setSelectedOffer?.(offer.accommodationIndex, offer.token);
      }
    });
  }, [offers, selectedOffers, setSelectedOffer]);

  const offersSelector = (
    <div className='flex flex-col gap-2 grow md:grow-0 md:items-start'>
      {offers
        ?.sort((a, b) => a.accommodationIndex - b.accommodationIndex)
        .map(offer => (
          <div
            key={offer.token}
            className='flex grow md:grow-0 flex-col items-start gap-4 bg-gray-100 p-2 md:pr-3 rounded-button mt-2'
          >
            <div className='flex w-full md:w-auto flex-col items-start'>
              {offer.appliedPromotions?.map(promotion => (
                <Badge type='danger' size='small' icon={<Fire weight='bold' />}>
                  {promotion}
                </Badge>
              ))}
              <div className='flex w-full items-start flex-col-reverse md:flex-row gap-3'>
                <Button
                  color={
                    selectedOffers[offer.accommodationIndex] === offer.token
                      ? 'primary'
                      : 'white'
                  }
                  className='pl-4 pr-4 w-full md:w-fit md:h-full'
                  onClick={() =>
                    setSelectedOffer?.(offer.accommodationIndex, offer.token)
                  }
                >
                  {selectedOffers[offer.accommodationIndex] === offer.token ? (
                    <CheckSquare size={16} weight='bold' />
                  ) : (
                    <Square size={16} weight='bold' />
                  )}
                  {selectedOffers[offer.accommodationIndex] === offer.token
                    ? t('availableRooms.content.selected')
                    : t('availableRooms.content.select')}
                  {offer?.prices.pricePerNightPromotional ? (
                    <Badge
                      className='md:hidden'
                      icon={<Tag weight='bold' size={14} />}
                      type='success'
                      size='small'
                    >
                      {t('availableRooms.content.pixDiscount')}
                    </Badge>
                  ) : null}
                </Button>
                <div className='flex items-center gap-4'>
                  <div className='flex flex-col'>
                    <p className='text-xs text-gray-500 font-light whitespace-nowrap'>
                      {t('availableRooms.content.roomOption')}
                    </p>
                    <h3 className='text-primary-900 font-medium text-base whitespace-nowrap'>
                      {t('availableRooms.content.room', {
                        number: offer.accommodationIndex + 1
                      })}
                    </h3>
                  </div>
                  <Divider className='divider' />
                  <div className='flex flex-col justify-center items-start'>
                    <p className='text-gray-500 text-sm'>
                      {t('availableRooms.content.perNight')}
                    </p>
                    {offer?.prices.pricePerNightPromotional ? (
                      <div className='flex gap-1 items-baseline'>
                        <p className='text-gray-500 text-xs line-through'>
                          {`${
                            offer?.prices.currencySymbol
                          } ${convertNumberToCurrency(
                            i18n!.language,
                            currency.code,
                            offer?.prices?.pricePerNight
                          )}`}
                        </p>
                        <h3 className='text-primary-900 font-medium text-base'>
                          {`${
                            offer?.prices.currencySymbol
                          } ${convertNumberToCurrency(
                            i18n!.language,
                            currency.code,
                            offer?.prices?.pricePerNightPromotional
                          )}`}
                        </h3>
                        <Badge
                          className='hidden md:flex'
                          icon={<Tag weight='bold' size={14} />}
                          type='success'
                          size='small'
                        >
                          {t('availableRooms.content.pixDiscount')}
                        </Badge>
                      </div>
                    ) : (
                      <div className='flex gap-2 items-baseline'>
                        <h3 className='text-primary-900 font-medium text-base'>
                          {`${
                            offer?.prices.currencySymbol
                          } ${convertNumberToCurrency(
                            i18n!.language,
                            currency.code,
                            offer?.prices?.pricePerNight
                          )}`}
                        </h3>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
    </div>
  );

  return (
    <div className='flex flex-col gap-4 p-4 rounded-default bg-white'>
      <div className='flex flex-wrap gap-2 md:hidden'>
        {refundable ? (
          <div className='flex flex-wrap items-start gap-1'>
            <Badge type='success' icon={<Check size={14} />} size='small'>
              {t('availableRooms.content.refundable')}
            </Badge>
            <Badge type='success' size='small'>
              {cancellation}
            </Badge>
          </div>
        ) : (
          <Badge
            icon={<Warning weight='bold' size={14} />}
            type='warning'
            size='small'
          >
            {t('availableRooms.content.nonRefundable')}
          </Badge>
        )}
      </div>
      <div className='flex gap-4'>
        <div className='flex flex-col items-start justify-start gap-3'>
          <ImageCarousel
            className='w-[100px] h-full min-h-[100px] md:w-[200px] md:h-[200px] rounded-button overflow-hidden'
            cover={photoCover}
            images={photos || []}
          />
        </div>
        <div className='flex items-start flex-col gap-2'>
          <div className='hidden gap-2 md:flex'>
            {refundable ? (
              <div className='flex flex-wrap items-start gap-1'>
                <Badge type='success' icon={<Check size={14} />} size='small'>
                  {t('availableRooms.content.refundable')}
                </Badge>
                <Badge type='success' size='small'>
                  {cancellation}
                </Badge>
              </div>
            ) : (
              <Badge
                icon={<Warning weight='bold' size={14} />}
                type='warning'
                size='small'
              >
                {t('availableRooms.content.nonRefundable')}
              </Badge>
            )}
          </div>
          <div className='flex flex-col items-start'>
            <h3 className='flex items-center flex-wrap capitalize gap-2 text-primary-900 font-semibold text-base md:text-lg'>
              {accommodationName}
              <Badge
                type='info'
                size='small'
                icon={<ForkKnife weight='bold' size={14} />}
              >
                {t('availableRooms.content.mealPlan', { plan: mealPlan })}
              </Badge>
            </h3>
            {amenitiesGroups && amenitiesGroups.length > 0 && (
              <div className='flex items-center flex-wrap gap-2 p-4 pb-0'>
                {amenitiesGroups?.map((group, groupIndex) =>
                  group.amenities?.map((amenity, index) => (
                    <>
                      <p className='text-gray-500 text-sm font-light'>
                        {amenity.description}
                      </p>
                      {group.amenities?.length !== index + 1 &&
                      amenitiesGroups.length !== groupIndex ? (
                        <div className='w-2 h-2 rounded-full bg-gray-200' />
                      ) : null}
                    </>
                  ))
                )}
              </div>
            )}
            <div className='w-full hidden flex-col gap-1 mt-2 md:flex'>
              {offersSelector}
            </div>
          </div>
        </div>
      </div>
      <div className='w-full flex md:hidden'>{offersSelector}</div>
    </div>
  );
};

export default HotelAvailableRoom;
