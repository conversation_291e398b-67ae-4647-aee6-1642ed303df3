import { FC, ReactNode } from 'react';

interface IEmptyState {
  icon?: ReactNode;
  title?: string;
  description?: string;
  action?: ReactNode;
  fullscreen?: boolean;
}

const EmptyState: FC<IEmptyState> = ({
  icon,
  title,
  description,
  action,
  fullscreen
}: IEmptyState) => {
  return (
    <div
      className={`w-full flex flex-col items-center justify-center py-[50px]
        ${
          fullscreen
            ? 'fixed top-0 left-0 w-full h-full bg-white/85 backdrop-blur-md overflow-hidden z-[10000]'
            : ''
        }
      `}
    >
      {icon && <div className='text-secondary-500'>{icon}</div>}
      {title ? (
        <h3 className='mt-2 text-lg font-semibold text-primary-900'>{title}</h3>
      ) : null}
      {description ? (
        <p className='text-base text-center text-gray-500 mb-[15px]'>
          {description}
        </p>
      ) : null}
      {action}
    </div>
  );
};

export default EmptyState;
