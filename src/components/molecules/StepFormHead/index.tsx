/* eslint-disable no-unused-vars */
import { FC } from 'react';
import StepMarker from '@components/atoms/StepMarker';
import { PaymentMethodType } from 'src/types/checkout';
import { useTranslation } from 'next-i18next';
import Divider from '@components/atoms/Divider';
import {
  buildStyles,
  CircularProgressbarWithChildren
} from 'react-circular-progressbar';
import 'react-circular-progressbar/dist/styles.css';

type StepFormHeadProps = {
  step: number;
  username?: string;
  paymentMethod?: PaymentMethodType;
  onStepClick?: (step: number) => void;
};

const StepFormHead: FC<StepFormHeadProps> = ({
  step,
  username,
  paymentMethod,
  onStepClick
}) => {
  const { t } = useTranslation('checkout');
  const stepTitle = [
    t('steper.account'),
    t('steper.payment'),
    t('steper.guests'),
    t('steper.resume'),
    t('steper.finish')
  ];

  const paymentMethods = {
    PIX: t('payment.step.pix'),
    CREDIT_CARD: t('payment.step.creditCard'),
    EXTERNAL: t('payment.step.external')
  };

  return (
    <div className='w-full flex'>
      <div className='w-full flex md:hidden items-center gap-4 mb-2 md:mb-4 bg-white rounded-default p-4'>
        <div className='w-1/6'>
          <CircularProgressbarWithChildren
            value={step * 25}
            strokeWidth={12}
            counterClockwise
            styles={buildStyles({
              pathColor: '#3554D1'
            })}
          >
            <p className='text-xs text-gray'>
              {t('steper.stepOf', { current: step + 1, total: '4' })}
            </p>
          </CircularProgressbarWithChildren>
        </div>
        <div>
          <h3 className='text-lg text-primary-900 font-semibold'>
            {stepTitle[step]}
          </h3>
          <p className='text-sm text-gray'>
            {t('steper.next', {
              title: stepTitle[step + 1]
            })}
          </p>
        </div>
      </div>
      <div className='hidden md:flex w-full items-start md:items-center justify-between py-8 gap-2 md:gap-12'>
        <StepMarker
          step={1}
          title={stepTitle[0]}
          subtitle={username}
          checked={step > 0}
          active={step === 0}
          onClick={() => onStepClick?.(0)}
        />
        <Divider className='hidden md:block' orientation='horizontal' />
        <StepMarker
          step={2}
          title={stepTitle[1]}
          subtitle={paymentMethods[paymentMethod!]}
          checked={step > 1}
          active={step === 1}
          onClick={() => onStepClick?.(1)}
        />
        <Divider className='hidden md:block' orientation='horizontal' />
        <StepMarker
          step={3}
          title={stepTitle[2]}
          checked={step > 2}
          active={step === 2}
          onClick={() => onStepClick?.(2)}
        />
        <Divider className='hidden md:block' orientation='horizontal' />
        <StepMarker
          step={4}
          title={stepTitle[3]}
          active={step === 3}
          checked={false}
          onClick={() => onStepClick?.(3)}
        />
      </div>
    </div>
  );
};

export default StepFormHead;
