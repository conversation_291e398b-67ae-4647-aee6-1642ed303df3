import { ReactNode } from 'react';

interface IHero {
  image: string;
  title: string;
  description: string;
  action: ReactNode;
}

const Hero = ({ image, title, description, action }: IHero) => {
  return (
    <div className='w-full flex flex-col-reverse md:flex-row items-center gap-[100px]'>
      <img
        src={image}
        className='w-full md:w-1/2 flex items-center flex-1'
        alt=''
      />
      <div className='flex flex-col items-start flex-1'>
        <div className='flex text-primary-900 text-[100px] font-bold leading-none mb-[25px]'>
          <span>4</span>
          <span>0</span>
          <span className='text-primary-500'>4</span>
        </div>
        <h2 className='text-primary-900 text-[32px] leading-[1.2] mb-[15px]'>
          {title}
        </h2>
        <p className='mb-[25px]'>{description}</p>
        {action}
      </div>
    </div>
  );
};

export default Hero;
