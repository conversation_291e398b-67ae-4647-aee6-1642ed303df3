import { FC, useState } from 'react';
import { useTranslation } from 'next-i18next';

interface IHotelOverview {
  content?: string;
}

const HotelOverview: FC<IHotelOverview> = ({ content }: IHotelOverview) => {
  const { t } = useTranslation('hotel');
  const [expanded, setExpanded] = useState<boolean>(false);

  return (
    <div className='flex flex-col items-start'>
      <h3 className='text-md text-primary-900 font-medium mb-3'>
        {t('descriptionSection.title')}
      </h3>
      <div
        dangerouslySetInnerHTML={{ __html: content || '' }}
        className={`text-sm md:text-base text-primary-900 mb-2 font-light leading-6 ${
          expanded ? 'line-clamp-none' : 'line-clamp-3 md:line-clamp-6'
        }`}
      />
      <div
        onClick={() => setExpanded(prev => !prev)}
        className='text-sm underline text-primary-900 hover:text-primary-500 cursor-pointer'
      >
        {expanded
          ? t('descriptionSection.expandMore')
          : t('descriptionSection.expandLess')}
      </div>
    </div>
  );
};

export default HotelOverview;
