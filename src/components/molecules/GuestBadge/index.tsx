import { IBookingG<PERSON> } from 'src/types/booking';
import { Baby, User } from '@phosphor-icons/react';
import { useTranslation } from 'next-i18next';

interface IGuestBadge extends IBookingGuest {}

const GuestBadge = ({ age, child, document, name, surname }: IGuestBadge) => {
  const { t } = useTranslation('bookings');

  return (
    <div className='flex flex-col py-1 px-2 bg-white rounded-button w-full md:w-auto'>
      <h3 className='flex gap-1 items-center font-medium text-primary-900'>
        {child ? <Baby size={16} /> : <User size={16} />} {name} {surname}
      </h3>
      <p className='text-sm text-gray-500'>
        {child
          ? t('reservationCard.reservationCardRoom.yearsOld', { age })
          : document}
      </p>
    </div>
  );
};

export default GuestBadge;
