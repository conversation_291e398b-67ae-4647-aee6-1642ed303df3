/* eslint-disable react/no-array-index-key */
import Button from '@components/atoms/Button';
import { FC } from 'react';
import Tooltip from '@components/atoms/Tooltip';

import { AdditionalTaxes } from 'src/types/cart';
import { ArrowSquareOut, Tag } from '@phosphor-icons/react';
import Badge from '@components/atoms/Badge';
import Divider from '@components/atoms/Divider';
import { useTranslation } from 'next-i18next';
import { IHotelOfferAdditionalTaxes } from 'src/types/hotel';

export type PurchasePricingItemType = {
  text: string;
  value: string;
  discount?: boolean;
  type?: string;
};

interface IPurchasePricing {
  totalPrice: string;
  userTotalPrice?: string;
  onFinish?: Function;
  items: PurchasePricingItemType[];
  additionalTaxes: (IHotelOfferAdditionalTaxes | AdditionalTaxes)[];
  loading?: boolean;
  disabled?: boolean;
  withDiscount: boolean;
  currencySymbol?: string;
}

const PurchasePricing: FC<IPurchasePricing> = ({
  totalPrice,
  userTotalPrice,
  onFinish,
  items,
  additionalTaxes,
  loading = false,
  disabled = false,
  withDiscount,
  currencySymbol
}: IPurchasePricing) => {
  const { t } = useTranslation('purchase');

  return (
    <div className='flex flex-col p-2 md:p-6 bg-white rounded-default'>
      <h2 className='text-base text-primary-900 font-medium mb-4'>
        {t('purchasePricing.title')}
      </h2>
      {!disabled && (
        <>
          <div className='flex flex-col mb-4 gap-1'>
            {items.map((item, index) => {
              return (
                <div
                  key={`${item.value}${index}`}
                  className={`flex items-center justify-between text-sm ${
                    item.discount
                      ? 'text-success-500 font-medium'
                      : 'text-gray-500 font-normal'
                  }`}
                >
                  <p>{item.text}</p>
                  <p>
                    {item.type === 'price'
                      ? `${currencySymbol} ${item.value}`
                      : item.value}
                  </p>
                </div>
              );
            })}
          </div>
          <Divider orientation='horizontal' />
        </>
      )}
      <div className='flex flex-col gap-4'>
        {!disabled && (
          <div className='flex flex-col'>
            {userTotalPrice && (
              <div>
                <div className='flex items-center justify-between pt-4'>
                  <p className='text-sm text-gray-500'>
                    {t('purchasePricing.userTotal')}
                  </p>
                  <p className='text-primary-900 text-lg font-semibold'>
                    {userTotalPrice}
                  </p>
                </div>
              </div>
            )}
            {userTotalPrice && (
              <Divider orientation='horizontal' className='mt-3' />
            )}
            <div className='flex items-center justify-between pt-4 text-[20px] text-primary-900 font-semibold'>
              <p>{t('purchasePricing.total')}</p>
              <div className='flex items-center gap-[8px]'>
                {withDiscount && (
                  <Badge
                    icon={<Tag weight='bold' size={14} />}
                    size='small'
                    type='success'
                  >
                    {t('purchasePricing.discountPix')}
                  </Badge>
                )}
                <p>{`${currencySymbol ?? ''} ${totalPrice}`}</p>
              </div>
            </div>
            {additionalTaxes.length === 0 && (
              <p className='text-sm text-gray-500 self-end'>
                {t('purchasePricing.taxesAndFeesIncluded')}
              </p>
            )}
          </div>
        )}
        {additionalTaxes &&
          additionalTaxes.length > 0 &&
          additionalTaxes?.map(
            tax =>
              'description' in tax && (
                <div className='flex flex-col mb-4 gap-1'>
                  <div className='flex items-center justify-between text-sm text-gray-500 mb-16'>
                    <p>
                      <i>{tax.description}</i>
                    </p>
                  </div>
                  <div className='flex items-center justify-between text-sm text-gray-500'>
                    <p>{tax.amountNotIncluded.text}</p>
                    <p>
                      ({tax.originalCurrency} {tax.originalAmountNotIncluded}){' '}
                      {tax.currencySymbol} {tax.amountNotIncluded.value}
                    </p>
                  </div>
                  <div className='flex items-center justify-between text-sm text-gray-500'>
                    <p>{tax.totalWithAmountNotIncluded.text}</p>
                    <p>
                      {tax.currencySymbol}{' '}
                      {tax.totalWithAmountNotIncluded.value}
                    </p>
                  </div>
                </div>
              )
          )}
        {onFinish && (
          <Tooltip
            text={t('purchasePricing.selectRoomsTooltip')}
            active={disabled}
          >
            <Button
              fullWidth
              color='primary'
              onClick={() => onFinish?.()}
              loading={loading}
              disabled={disabled}
              className='h-[48px] mt-[14px]'
            >
              {t('purchasePricing.reserveNow')}
              <ArrowSquareOut size={18} weight='bold' />
            </Button>
          </Tooltip>
        )}
      </div>
    </div>
  );
};

export default PurchasePricing;
