import { FC, useEffect, useState } from 'react';
import Image from 'next/image';
import Button from '@components/atoms/Button';
import { toast } from 'react-toastify';
import { ClockCountdown, PixLogo } from '@phosphor-icons/react';
import { convertSecondsToMinutes } from 'src/utils/date';
import { useTranslation } from 'next-i18next';

interface IPixPaymentCard {
  base64: string;
  qrcodeLink: string;
  price: string;
  priceDescription: string;
  secondsRemaining?: number;
  backgroundColor?: string;
}

const PixPaymentCard: FC<IPixPaymentCard> = ({
  base64,
  qrcodeLink,
  price,
  priceDescription,
  secondsRemaining,
  backgroundColor
}: IPixPaymentCard) => {
  const { t } = useTranslation('bookings');
  const [paymentCountdown, setPaymentCountdown] = useState<number>(
    secondsRemaining && secondsRemaining > 0 ? secondsRemaining : 0
  );
  const handleCopyQRCode = () => {
    navigator.clipboard.writeText(qrcodeLink);
    toast(t('pixPaymentCard.copied'), {
      type: 'success'
    });
  };

  useEffect(() => {
    const timer = setInterval(() => {
      if (paymentCountdown > 0) setPaymentCountdown(prev => prev - 1);
    }, 1000);

    return () => clearInterval(timer);
  }, [paymentCountdown]);

  return (
    <div
      className={`flex flex-col sm:flex-row gap-6 ${
        backgroundColor === 'white' ? 'p-4 bg-white rounded' : ''
      }`}
    >
      <Image
        width={300}
        height={300}
        src={`data:image/png;base64,${base64}`}
        alt={t('pixPaymentCard.title')}
        className={`w-full border border-gray-200 rounded sm:w-[150px] sm:h-[150px] ${
          paymentCountdown === 0 ? 'blur-sm' : ''
        }`}
      />
      <div className='w-full flex flex-col justify-between'>
        <div className='flex flex-col gap-[3px]'>
          <h3 className='font-medium mt-1 text-lg text-primary-900 leading-none'>
            {t('pixPaymentCard.title')}
          </h3>
          <p className='text-sm text-gray-500'>
            {t('pixPaymentCard.description')}
          </p>
        </div>
        {secondsRemaining && (
          <div className='flex items-center gap-1 md:my-0 my-3'>
            <ClockCountdown size={18} className='text-primary-500' />
            {paymentCountdown === 0 ? (
              <p className='text-base text-red-600 leading-none'>
                {t('pixPaymentCard.expired')}
              </p>
            ) : (
              <p className='text-base text-danger leading-none'>
                {t('pixPaymentCard.timeRemaining', {
                  time: convertSecondsToMinutes(paymentCountdown)
                })}
              </p>
            )}
          </div>
        )}
        <div className='flex gap-5 flex-wrap justify-between items-center bg-gray-100 rounded p-3'>
          <div className='flex items-center gap-3'>
            <div className='w-10 h-10 flex justify-center items-center bg-white rounded-full'>
              <PixLogo size={22} weight='duotone' className='text-green-900' />
            </div>
            <div className='flex flex-col gap-1'>
              <p className='text-sm text-gray-500 leading-none'>
                {priceDescription}
              </p>
              <h4 className='text-lg font-medium text-primary-900 leading-none'>
                {price}
              </h4>
            </div>
          </div>
          {paymentCountdown !== 0 ? (
            <Button
              color='primary'
              onClick={() => handleCopyQRCode()}
              disabled={paymentCountdown === 0}
              className='w-full sm:w-auto'
            >
              {t('pixPaymentCard.copyQRCode')}
            </Button>
          ) : null}
        </div>
      </div>
    </div>
  );
};

export default PixPaymentCard;
