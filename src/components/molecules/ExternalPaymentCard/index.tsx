import { FC, useEffect, useState } from 'react';
import { useTranslation } from 'next-i18next';
import { ClockCountdown, CreditCard } from '@phosphor-icons/react';
import { convertSecondsToMinutes } from 'src/utils/date';
import Modal from '@components/molecules/Modal';
import Button from '@components/atoms/Button';

interface IExternalPaymentCard {
  iframeUrl?: string;
  price: string;
  priceDescription: string;
  secondsRemaining?: number;
  isPaymentOpen: boolean;
  setPaymentOpen: Function;
  onTimeout: Function;
  status?: string;
}

const ExternalPaymentCard: FC<IExternalPaymentCard> = ({
  iframeUrl,
  price,
  priceDescription,
  secondsRemaining,
  isPaymentOpen,
  setPaymentOpen,
  onTimeout,
  status
}: IExternalPaymentCard) => {
  const { t } = useTranslation('bookings');
  const [paymentCountdown, setPaymentCountdown] = useState<number>(
    secondsRemaining && secondsRemaining > 0 ? secondsRemaining : 0
  );

  useEffect(() => {
    const timer = setInterval(() => {
      if (paymentCountdown > 0) setPaymentCountdown(prev => prev - 1);
      if (paymentCountdown === 0) {
        clearInterval(timer);
        onTimeout();
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [onTimeout, paymentCountdown]);

  return (
    <div className='flex flex-col justify-between'>
      {!secondsRemaining && (
        <div className='flex flex-col gap-1'>
          <h3 className='font-medium text-primary-900'>
            {t('externalPaymentCard.title')}
          </h3>
          <p className='text-gray-500 text-sm'>
            {t('externalPaymentCard.description')}
          </p>
        </div>
      )}
      {secondsRemaining && status !== 'BOOKING_ERROR' && (
        <div className='flex items-center gap-1 mt-2 mb-1'>
          <ClockCountdown size={18} />
          {paymentCountdown === 0 ? (
            <p className='text-red-600'>{t('externalPaymentCard.expired')}</p>
          ) : (
            <p className='text-primary-500'>
              {t('externalPaymentCard.timeRemaining', {
                time: convertSecondsToMinutes(paymentCountdown)
              })}
            </p>
          )}
        </div>
      )}
      <div className='flex justify-between gap-3 bg-gray-100 rounded-button px-4 py-3 flex-col md:flex-row'>
        <div className='flex items-center gap-3'>
          <div className='flex flex-col'>
            <p className='text-sm text-gray-500'>{priceDescription}</p>
            <h4 className='font-semibold text-xl text-primary-900'>{price}</h4>
          </div>
        </div>
        {paymentCountdown > 0 && (
          <Button
            color='primary'
            onClick={() => setPaymentOpen(true)}
            className='md:self-center'
          >
            {t('externalPaymentCard.buttonText')}
            <CreditCard size={22} />
          </Button>
        )}
      </div>
      <Modal
        show={isPaymentOpen}
        onClose={() => setPaymentOpen(false)}
        title='teste'
        titleHeading={
          <h3 className='font-medium text-lg text-primary-900'>
            {t('orderCardPayment.external.title')}
          </h3>
        }
      >
        <div className='h-[100vh] md:h-[398px] p-4'>
          <iframe width='100%' height='100%' src={iframeUrl} title='payment' />
        </div>
      </Modal>
    </div>
  );
};

export default ExternalPaymentCard;
