import { FC } from 'react';
import Link from 'next/link';
import FooterSection from '@components/molecules/FooterSection';
import Script from 'next/script';
import Image from 'next/image';
import { useTranslation } from 'next-i18next';
import Divider from '@components/atoms/Divider';
import * as Sentry from '@sentry/nextjs';

interface IFooterProps {
  host: string;
}

const Footer: FC<IFooterProps> = ({ host }: IFooterProps) => {
  const { t, i18n } = useTranslation('common');

  return (
    <div className='w-full py-6 px-1 sm:p-12 text-primary-900 bg-gray-100'>
      <div className='container'>
        <div className='flex flex-wrap flex-row gap-x-16 gap-y-12 pb-12'>
          <FooterSection title={t('footer.company.title')}>
            <Link
              color='primary'
              href={t('routes.contact')}
              locale={i18n.language}
            >
              {t('footer.company.contact')}
            </Link>
            <Link
              color='primary'
              href={t('routes.tripcash')}
              locale={i18n.language}
            >
              {t('footer.company.tripcash')}
            </Link>
          </FooterSection>
          <FooterSection title={t('footer.support.title')}>
            <Link
              color='primary'
              href={t('routes.security')}
              locale={i18n.language}
            >
              {t('footer.support.security')}
            </Link>
            <Link
              color='primary'
              href={t('routes.privacyPolicy')}
              locale={i18n.language}
            >
              {t('footer.support.privacy')}
            </Link>
            <Link
              color='primary'
              href={t('routes.termsAndConditions')}
              locale={i18n.language}
            >
              {t('footer.support.terms')}
            </Link>
          </FooterSection>
          <FooterSection title={t('footer.paymentMethods.title')}>
            <div className='flex flex-wrap items-center gap-3 max-w-48'>
              {host.includes('.br') && (
                <p className='w-full text-gray-500 mt-0'>
                  {t('footer.paymentMethods.12x')}
                </p>
              )}
              <Image
                src='/images/cards/mastercard.png'
                alt='Bandeira Mastercard'
                width={40}
                height={25}
              />
              <Image
                src='/images/cards/visa.png'
                alt='Bandeira Visa'
                width={40}
                height={25}
              />
              <Image
                src='/images/cards/amex.png'
                alt='Bandeira Amex'
                width={40}
                height={25}
              />
              <Image
                src='/images/cards/elo.png'
                alt='Bandeira Elo'
                width={40}
                height={25}
              />
              <Image
                src='/images/cards/dinersclub.png'
                alt='Bandeira Diners Club'
                width={40}
                height={25}
              />
              <Image
                src='/images/cards/jcb.png'
                alt='Bandeira JCB'
                width={40}
                height={25}
              />
              <Image
                src='/images/cards/discover.png'
                alt='Bandeira Discover'
                width={40}
                height={25}
              />
              <Image
                src='/images/cards/hipercard.png'
                alt='Bandeira Hipercard'
                width={40}
                height={25}
              />
              {host.includes('.br') && (
                <>
                  <p className='w-full text-gray-500 mt-1'>
                    {t('footer.paymentMethods.transfers')}
                  </p>
                  <Image
                    src='/images/pix.png'
                    alt='Bandeira Pix'
                    width={43}
                    height={15}
                  />
                </>
              )}
            </div>
          </FooterSection>
          <FooterSection title={t('footer.seals.title')}>
            <div className='flex flex-wrap items-center gap-4'>
              <div id='ra-verified-seal'>
                <Script
                  type='text/javascript'
                  id='ra-embed-verified-seal'
                  src='https://s3.amazonaws.com/raichu-beta/ra-verified/bundle.js'
                  data-id='azVaUUo5OFNFYURQTkpvRDpvdXJ0cmlwLXZpYWdlbnMtZS10dXJpc21v'
                  data-target='ra-verified-seal'
                  data-model='2'
                />
              </div>
              <a
                href='https://transparencyreport.google.com/safe-browsing/search?url=https:%2F%2Fourtrip.com.br'
                target='_blank'
                rel='noreferrer'
                className='leading-0'
              >
                <Image
                  src='/images/google.webp'
                  alt='Selo Google'
                  width={110}
                  height={46}
                />
              </a>
              <div id='armored_website'>
                <param id='aw_preload' value='true' />
                <param id='aw_use_cdn' value='true' />
              </div>
              <Script
                type='text/javascript'
                id='armored_website_script'
                src='https://cdn.siteblindado.com/aw.js'
                data-id='armored_website'
                data-target='armored_website'
                data-model='2'
                data-ssl='true'
                data-ssl-target='armored_website'
                data-ssl-model='2'
                strategy='afterInteractive'
                onError={() => {
                  Sentry.captureException(
                    new Error('Failed to load Armored Website script')
                  );
                }}
              />
            </div>
          </FooterSection>
        </div>
        <div className='w-full flex flex-col gap-6'>
          <Divider orientation='horizontal' />
          <div className='w-full flex justify-between items-center'>
            <div className='flex items-center'>
              <p className='text-sm text-primary-900'>
                {t('footer.rights', { year: new Date().getFullYear() })}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Footer;
