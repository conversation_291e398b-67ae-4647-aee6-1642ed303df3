import { AnimatePresence, motion } from 'framer-motion';
import { PropsWithChildren, ReactNode, useRef } from 'react';
import useOnClickOutside from 'src/hooks/useClickOutside';
import { X } from '@phosphor-icons/react';

interface IModal extends PropsWithChildren {
  title?: string;
  titleHeading?: ReactNode;
  icon?: ReactNode;
  show: boolean;
  onClose: Function;
  footer?: ReactNode;
}

const Modal = ({
  title,
  titleHeading,
  icon,
  show,
  onClose,
  footer,
  children
}: IModal) => {
  const modalContainerRef = useRef(null);
  useOnClickOutside(modalContainerRef, () => onClose());

  const variants = {
    visible: {
      opacity: 1,
      transition: {
        when: 'beforeChildren',
        duration: 0.3
      }
    },
    hidden: {
      opacity: 0,
      transition: {
        duration: 0.3
      }
    }
  };

  return (
    <AnimatePresence>
      {show ? (
        <motion.div
          className='fixed w-screen h-screen z-[10000] top-0 left-0 bg-black/15 flex md:items-center items-end justify-center'
          initial='hidden'
          animate='visible'
          exit='hidden'
          variants={variants}
        >
          <motion.div
            className='md:w-[500px] w-full md:h-auto h-full p-6 rounded-default bg-white overflow-auto'
            initial={{ marginTop: -15 }}
            animate={{ marginTop: 0 }}
            exit={{ marginTop: -15 }}
            transition={{ duration: 0.3 }}
            ref={modalContainerRef}
          >
            <div className='w-full flex flex-row justify-between items-start'>
              {titleHeading || (
                <div className='flex items-center gap-3'>
                  {icon && <div className='text-primary-900'>{icon}</div>}
                  <h2 className='text-lg text-primary-900 font-medium'>
                    {title}
                  </h2>
                </div>
              )}
              <div
                className='w-6 h-6 rounded-default flex justify-center items-center transition-all duration-300 cursor-pointer hover:bg-gray-200'
                onClick={() => onClose()}
              >
                <X />
              </div>
            </div>
            <div className='mt-2'>{children}</div>
            <div className='mt-3 w-full flex md:flex-row flex-col justify-end gap-3'>
              {footer}
            </div>
          </motion.div>
        </motion.div>
      ) : null}
    </AnimatePresence>
  );
};

export default Modal;
