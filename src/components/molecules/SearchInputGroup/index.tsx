/* eslint-disable no-unused-vars */
/* eslint-disable no-shadow */
import { useTranslation } from 'next-i18next';
import { IDestination, ISearchPayloadRooms } from 'src/types/search';
import DestinationSearch from '@components/molecules/DestinationSearch';
import DateSearch, { DatesType } from '@components/molecules/DateSearch';
import RoomsSearch from '@components/molecules/RoomsSearch';
import Button from '@components/atoms/Button';

const SearchInputGroup = ({
  destination,
  setDestination,
  checkin,
  checkout,
  setDates,
  distribution,
  setDistribution,
  handleSubmit,
  disableSearch
}: {
  destination: IDestination;
  setDestination: (destination: IDestination) => void;
  checkin: string | null;
  checkout: string | null;
  setDates: (dates: DatesType) => void;
  distribution: ISearchPayloadRooms[];
  setDistribution: (distribution: ISearchPayloadRooms[]) => void;
  handleSubmit: () => void;
  disableSearch: boolean;
}) => {
  const { t } = useTranslation(['search', 'common'], {
    nsMode: 'fallback'
  });

  return (
    <>
      <div className='w-full flex gap-6 md:gap-2 flex-col mb-4 md:mb-0 md:pl-1 md:flex-row'>
        <DestinationSearch
          label={t('search.destination.label')}
          placeholder={t('search.destination.placeholder')}
          value={destination}
          onChange={setDestination}
        />
        <DateSearch
          label={t('search.dates.label')}
          placeholder={t('search.dates.placeholder')}
          handleChange={setDates}
          initialStartDate={checkin}
          initialEndDate={checkout}
        />
        <RoomsSearch initial={distribution} onChange={setDistribution} />
      </div>
      <Button
        color='primary'
        onClick={handleSubmit}
        disabled={disableSearch}
        size='large'
      >
        {t('search.search')}
      </Button>
    </>
  );
};

export default SearchInputGroup;
