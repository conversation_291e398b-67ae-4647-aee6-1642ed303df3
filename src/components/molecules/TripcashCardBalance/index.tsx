import Divider from '@components/atoms/Divider';
import { HandCoins } from '@phosphor-icons/react';
import { useTranslation } from 'next-i18next';

interface ITripcashCardBalance {
  label?: string;
  values?: string[];
}

const TripcashCardBalance = ({ label, values }: ITripcashCardBalance) => {
  const { t } = useTranslation('account');

  return (
    <div className='flex items-center gap-2 bg-gray-100 py-3 px-4 rounded-button'>
      <div className='w-[40px] h-[40px] flex-none bg-white rounded-full flex items-center justify-center'>
        <HandCoins size={22} className='text-primary-900' />
      </div>
      <div className='flex flex-col justify-center whitespace-nowrap'>
        <p className='text-gray-500 text-sm'>
          {label || t('account.tripcash.balance')}
        </p>
        <div
          className={`flex items-center text-primary-900 font-medium gap-2 ${
            values!.length > 2
              ? 'text-xs'
              : values!.length > 1
              ? 'text-sm'
              : 'text-lg'
          }`}
        >
          {values?.map((value, index) => (
            <div key={value} className='flex items-center gap-2'>
              <p>{value}</p>
              {values?.length > 1 && index !== values!.length - 1 && (
                <Divider orientation='vertical' className='h-4' />
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TripcashCardBalance;
