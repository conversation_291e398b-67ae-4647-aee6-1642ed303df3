/* eslint-disable no-unused-vars */
/* eslint-disable import/no-duplicates */
import { FC, MouseEventHandler, useEffect, useRef, useState } from 'react';
import ReactDatePicker from 'react-datepicker';
import { addDays, addYears, format, parse } from 'date-fns';
import { capitalizeFirstLetter } from 'src/utils/strings';
import { CalendarCheck, CaretLeft, CaretRight } from '@phosphor-icons/react';
import useWindowWith from 'src/hooks/useWindowSize';
import { Sheet, SheetContent, SheetTrigger } from '@components/atoms/Sheet';
import useOnClickOutside from 'src/hooks/useClickOutside';
import { convertStringToUTCDate, getDateFNSLocale } from 'src/utils/date';
import { i18n } from 'next-i18next';

export type DatesType = [Date | null, Date | null];

interface DateSearchProps {
  label?: string;
  placeholder?: string;
  initialStartDate: string | null;
  initialEndDate: string | null;
  handleChange?: (dates: DatesType) => void;
}

const DateInput = ({
  label,
  placeholder,
  startDate,
  endDate,
  onClick
}: {
  label?: string;
  placeholder?: string;
  startDate: string | null;
  endDate: string | null;
  onClick?: MouseEventHandler;
}) => {
  return (
    <div className='w-full flex gap-4' onClick={onClick}>
      <div className='flex flex-none items-center justify-center w-12 text-primary-900 bg-gray-100 rounded-button'>
        <CalendarCheck size={22} />
      </div>
      <div className='flex flex-col items-start w-full'>
        <label htmlFor='destination' className='font-medium text-primary-900'>
          {label}
        </label>
        <p className='text-gray-500 text-ellipsis whitespace-nowrap overflow-hidden'>
          {startDate && endDate
            ? `${format(convertStringToUTCDate(startDate), 'P', {
                locale: getDateFNSLocale(i18n?.language!)
              })} - ${format(convertStringToUTCDate(endDate), 'P', {
                locale: getDateFNSLocale(i18n?.language!)
              })}`
            : placeholder}
        </p>
      </div>
    </div>
  );
};

const CustomDatePicker = ({
  startDate,
  endDate,
  onChange,
  minDate,
  maxDate,
  isMobile
}: any) => {
  const [startDateState, setStartDateState] = useState<Date | null>(
    startDate ? convertStringToUTCDate(startDate) : null
  );
  const [endDateState, setEndDateState] = useState<Date | null>(
    endDate ? convertStringToUTCDate(endDate) : null
  );
  const locale = getDateFNSLocale(i18n?.language ?? 'pt-BR');

  const handleChange = (dates: DatesType) => {
    const start = dates[0];
    let end = dates[1];

    setStartDateState(start);

    if (end?.toUTCString() === start?.toUTCString()) {
      const newEnd = addDays(end!, 1);
      setEndDateState(newEnd);
      end = newEnd;
    } else {
      setEndDateState(end);
    }

    onChange([start, end]);
  };

  return (
    <ReactDatePicker
      startDate={startDateState}
      endDate={endDateState}
      onChange={handleChange}
      locale={locale}
      minDate={minDate}
      maxDate={maxDate}
      selectsRange
      monthsShown={isMobile ? 1 : 2}
      formatWeekDay={day => capitalizeFirstLetter(day.substring(0, 3))}
      onFocus={e => e.target.blur()}
      inline
      renderCustomHeader={({
        monthDate,
        prevMonthButtonDisabled,
        nextMonthButtonDisabled,
        decreaseMonth,
        increaseMonth
      }) => (
        <div className='flex items-center justify-between mb-4'>
          {!prevMonthButtonDisabled ? (
            <CaretLeft size={18} onClick={decreaseMonth} />
          ) : (
            <div />
          )}
          <h3 className='text-lg text-primary-900 font-medium'>
            {capitalizeFirstLetter(
              format(monthDate, 'MMMM yyyy', {
                locale
              }).toString()
            )}
          </h3>
          {!nextMonthButtonDisabled ? (
            <CaretRight size={18} onClick={increaseMonth} />
          ) : (
            <div />
          )}
        </div>
      )}
    />
  );
};

const DateSearch: FC<DateSearchProps> = ({
  label,
  placeholder,
  initialStartDate,
  initialEndDate,
  handleChange
}) => {
  const [startDate, setStartDate] = useState<string | null>(initialStartDate);
  const [endDate, setEndDate] = useState<string | null>(initialEndDate);
  const [isMobile, setIsMobile] = useState<boolean>(false);
  const [minDate] = useState<Date>(addDays(new Date(), 1));
  const [maxDate, setMaxDate] = useState<Date>(addYears(new Date(), 1));
  const [overlayOpen, setOverlayOpen] = useState<boolean>(false);
  const windowWidth = useWindowWith();

  const overlayRef = useRef<HTMLDivElement>(null);
  useOnClickOutside(overlayRef, () => setOverlayOpen(false));

  const onChange = (dates: DatesType) => {
    const [start, end] = dates;
    if (start) setStartDate(format(start!, 'yyyy-MM-dd'));
    if (end) setEndDate(format(end!, 'yyyy-MM-dd'));

    handleChange?.(dates);

    if (dates[0] && dates[1]) {
      setOverlayOpen(false);
    }
  };

  useEffect(() => {
    if (startDate && !endDate) {
      setMaxDate(addDays(new Date(startDate), 30));
    } else {
      setMaxDate(addYears(new Date(), 1));
    }
  }, [startDate, endDate]);

  useEffect(() => {
    setIsMobile(windowWidth < 768);
  }, [windowWidth]);

  return (
    <>
      <div className='relative w-full hidden md:flex z-20'>
        <DateInput
          label={label}
          placeholder={placeholder}
          startDate={startDate}
          endDate={endDate}
          onClick={() => setOverlayOpen(true)}
        />
        {overlayOpen && (
          <div
            ref={overlayRef}
            className='absolute flex flex-col top-[100%] mt-6 rounded-default bg-white p-0 md:p-6 w-max shadow-lg'
          >
            <CustomDatePicker
              startDate={startDate}
              endDate={endDate}
              onChange={onChange}
              minDate={minDate}
              maxDate={maxDate}
              isMobile={isMobile}
            />
          </div>
        )}
      </div>
      <div className='w-full flex md:hidden'>
        <Sheet
          open={windowWidth < 768 ? overlayOpen : false}
          onOpenChange={setOverlayOpen}
        >
          <SheetTrigger className='w-full'>
            <DateInput
              label={label}
              placeholder={placeholder}
              startDate={startDate}
              endDate={endDate}
            />
          </SheetTrigger>
          <SheetContent
            ref={overlayRef}
            side='bottom'
            className='h-min rounded-t-default flex justify-center items-center'
          >
            <CustomDatePicker
              startDate={startDate}
              endDate={endDate}
              onChange={onChange}
              minDate={minDate}
              maxDate={maxDate}
              isMobile={isMobile}
            />
          </SheetContent>
        </Sheet>
      </div>
    </>
  );
};

export default DateSearch;
