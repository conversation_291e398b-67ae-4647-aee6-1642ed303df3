import { FC, ReactNode, useState } from 'react';
import { DotOutline } from '@phosphor-icons/react';
import { IHotelAmenitiesGroup } from 'src/types/hotel';
import { useTranslation } from 'next-i18next';

export type HotelAmenitiesType = {
  id: string;
  icon?: ReactNode;
  text: string;
};

interface IHotelAmenities {
  title: string;
  items?: IHotelAmenitiesGroup[];
}

const HotelAmenities: FC<IHotelAmenities> = ({
  title,
  items
}: IHotelAmenities) => {
  const { t } = useTranslation('hotel');
  const [expanded, setExpanded] = useState<boolean>(false);

  return (
    <div className='flex flex-col items-start'>
      <h3 className='text-base text-primary-900 font-medium mb-2'>{title}</h3>
      <div
        className={`flex flex-col gap-2 mb-2 ${
          expanded ? 'max-h-min' : 'max-h-20 md:max-h-24'
        } overflow-hidden`}
      >
        {items?.map(amenitiesGroup => (
          <div
            key={amenitiesGroup.description}
            className='flex flex-col gap-2 mb-3'
          >
            {amenitiesGroup.description && (
              <h3 className='text-md text-primary-900 font-medium'>
                {amenitiesGroup.description}
              </h3>
            )}
            <div className='flex flex-wrap gap-2'>
              {amenitiesGroup.amenities!.map(amenity => (
                <div
                  key={amenity.description}
                  className='text-sm md:text-base flex items-center gap-2 font-light text-primary-900'
                >
                  <DotOutline weight='bold' size={22} /> {amenity.description}
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
      <div
        onClick={() => setExpanded(prev => !prev)}
        className='text-sm underline cursor-pointer text-primary-900 hover:text-primary-500'
      >
        {expanded ? t('amenities.expandLess') : t('amenities.expandMore')}
      </div>
    </div>
  );
};

export default HotelAmenities;
