import { FC, ReactNode } from 'react';
import { motion } from 'framer-motion';

export type ShowcaseProps = {
  title?: string;
  description?: string;
  options?: ReactNode;
  children?: ReactNode;
  className?: string;
};

const Showcase: FC<ShowcaseProps> = ({
  title,
  description,
  options,
  children,
  className
}: ShowcaseProps) => {
  return (
    <div className={`container bg-gray-100 ${className}`}>
      <div className='w-full pt-8 md:pt-24'>
        <div className='w-full flex justify-between items-center mb-6'>
          <div>
            {title && (
              <motion.h1
                className='text-primary-900 text-xl md:text-2xl font-semibold text-start'
                initial={{ y: 20, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                viewport={{ once: true }}
                transition={{
                  delay: 0.2,
                  duration: 0.7
                }}
              >
                {title}
              </motion.h1>
            )}
            {description && (
              <motion.p
                className='text-gray-500 text-start'
                initial={{ y: 20, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                viewport={{ once: true }}
                transition={{
                  delay: 0.4,
                  duration: 0.7
                }}
              >
                {description}
              </motion.p>
            )}
          </div>
          {options && options}
        </div>
        {children && children}
      </div>
    </div>
  );
};

export default Showcase;
