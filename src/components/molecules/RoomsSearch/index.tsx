/* eslint-disable no-shadow */
/* eslint-disable no-unused-vars */
/* eslint-disable react/no-array-index-key */
import React, {
  FC,
  MouseEventHandler,
  useEffect,
  useRef,
  useState
} from 'react';
import IncrementButton from '@components/atoms/IncrementButton';
import useOnClickOutside from 'src/hooks/useClickOutside';
import Button from '@components/atoms/Button';
import Select from '@components/atoms/Input/Select';
import { childrenAgeOptions } from '@consts/search';
import Divider from '@components/atoms/Divider';
import { ISearchPayloadRooms } from 'src/types/search';
import {
  mountRoomDistributionStringFromDistribution,
  mountRoomDistributionStringFromObject
} from 'src/utils/search';
import { useTranslation } from 'next-i18next';
import { Users } from '@phosphor-icons/react';
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTrigger
} from '@components/atoms/Sheet';
import useWindowWith from 'src/hooks/useWindowSize';

const MAX_ROOMS_LENGTH = 4;

type IRoomsSearch = {
  initial: ISearchPayloadRooms[];
  onChange: (
    distribution: ISearchPayloadRooms[],
    distributionCode: string,
    distributionLabel: string
  ) => void;
};

const RoomsInput = ({
  label,
  placeholder,
  onClick
}: {
  label: string;
  placeholder: string;
  onClick: MouseEventHandler;
}) => {
  return (
    <div className='w-full flex gap-4' onClick={onClick}>
      <div className='flex flex-none items-center justify-center w-12 bg-gray-100 rounded-button'>
        <Users size={22} />
      </div>
      <div className='flex flex-col items-start w-full'>
        <label htmlFor='destination' className='font-medium text-primary-900'>
          {label}
        </label>
        <p className='text-gray-500 text-ellipsis whitespace-nowrap overflow-hidden'>
          {placeholder}
        </p>
      </div>
    </div>
  );
};

const RoomsOverlayContent = ({
  distribution,
  handleRemoveRoom,
  handleIncrement,
  handleChildrenAge,
  handleAddRoom,
  onApply
}: {
  distribution: ISearchPayloadRooms[];
  handleRemoveRoom: Function;
  handleIncrement: Function;
  handleChildrenAge: Function;
  handleAddRoom: Function;
  onApply?: () => void;
}) => {
  const { t } = useTranslation('common');

  return (
    <div className='flex flex-col p-0 md:p-4 gap-8 md:gap-4'>
      {distribution.map((room, index) => {
        return (
          <div className='flex flex-col gap-6' key={index}>
            <div className='flex flex-col gap-4'>
              <div className='flex items-center justify-between'>
                <h3 className='text-lg text-primary-900 font-semibold'>{`${t(
                  'search.rooms.room'
                )} ${index + 1}`}</h3>
                {index > 0 ? (
                  <Button
                    color='primary'
                    variant='outline'
                    onClick={() => handleRemoveRoom(index)}
                    size='small'
                  >
                    {t('search.rooms.remove')}
                  </Button>
                ) : null}
              </div>
              <IncrementButton
                value={room.adults}
                label={t('search.rooms.adults')}
                increment={value => handleIncrement(value, index, 'adults')}
              />
              <IncrementButton
                value={room.kids.length}
                label={t('search.rooms.kids')}
                increment={value => handleIncrement(value, index, 'kids')}
                maxValue={4}
              />
              {room.kids.length > 0 ? (
                <div className='flex flex-col gap-2'>
                  {room.kids.map((_, childrenIndex) => {
                    return (
                      <Select
                        key={index}
                        id={`children-${index}-${childrenIndex}`}
                        options={Array.from({ length: 18 }, (_, i) => ({
                          value: `${i}`,
                          label: t('search.rooms.kidsAge', { count: i })
                        }))}
                        onChange={ev =>
                          handleChildrenAge(
                            ev.target.value,
                            childrenIndex,
                            index
                          )
                        }
                        defaultValue={room.kids[childrenIndex]}
                      />
                    );
                  })}
                </div>
              ) : null}
            </div>
            {distribution.length > 1 && index < distribution.length - 1 && (
              <Divider key={index} orientation='horizontal' />
            )}
          </div>
        );
      })}
      <div className='flex flex-col gap-4'>
        <Button
          fullWidth
          color='primary'
          variant='outline'
          onClick={() => handleAddRoom()}
          disabled={distribution.length === MAX_ROOMS_LENGTH}
        >
          {t('search.rooms.addRoom')}
        </Button>
        <Button onClick={() => onApply?.()}>{t('search.rooms.apply')}</Button>
      </div>
    </div>
  );
};

const RoomsSearch: FC<IRoomsSearch> = ({ initial, onChange }: IRoomsSearch) => {
  const { t } = useTranslation('common');
  const [overlayOpen, setOverlayOpen] = useState(false);
  const [distribution, setDistribution] = useState<ISearchPayloadRooms[]>([
    { adults: 2, kids: [] }
  ]);
  const [distributionLabel, setDistributionLabel] = useState(
    mountRoomDistributionStringFromDistribution(t, initial)
  );
  const windowWidth = useWindowWith();
  const overlayRef = useRef(null);
  useOnClickOutside(overlayRef, () => setOverlayOpen(false));

  useEffect(() => {
    if (initial) {
      setDistribution(initial);
      setDistributionLabel(
        mountRoomDistributionStringFromDistribution(t, initial)
      );
    }
  }, [t, initial]);

  const handleApply = (updatedDistribution: ISearchPayloadRooms[]) => {
    const newDistributionCode =
      mountRoomDistributionStringFromObject(updatedDistribution);
    const newDistributionLabel = mountRoomDistributionStringFromDistribution(
      t,
      updatedDistribution
    );

    setDistributionLabel(newDistributionLabel);
    onChange?.(updatedDistribution, newDistributionCode, newDistributionLabel);
  };

  const handleIncrement = (
    value: number,
    roomIndex: number,
    guestType: 'adults' | 'kids'
  ) => {
    setDistribution(prev => {
      const newDistribution: ISearchPayloadRooms[] = JSON.parse(
        JSON.stringify(prev)
      );

      if (guestType === 'kids') {
        if (value > 0) newDistribution[roomIndex].kids.push(0);
        else newDistribution[roomIndex].kids.pop();
      } else {
        newDistribution[roomIndex].adults += value;
      }

      handleApply(newDistribution);
      return newDistribution;
    });
  };

  const handleAddRoom = () => {
    setDistribution(prev => {
      const newDistribution = [...prev, { adults: 2, kids: [] }];

      handleApply(newDistribution);
      return newDistribution;
    });
  };

  const handleRemoveRoom = (index: number) => {
    setDistribution(prev => {
      const newDistribution = [...prev];
      newDistribution.splice(index, 1);

      handleApply(newDistribution);
      return newDistribution;
    });
  };

  const handleChildrenAge = (
    value: string,
    ageIndex: number,
    roomIndex: number
  ) => {
    setDistribution(prev => {
      const newDistribution = JSON.parse(JSON.stringify(prev));
      newDistribution[roomIndex].kids[ageIndex] = parseInt(value, 10);

      handleApply(newDistribution);
      return newDistribution;
    });
  };

  return (
    <>
      <div className='relative w-full hidden md:flex z-20'>
        <RoomsInput
          label={t('search.rooms.label')}
          placeholder={distributionLabel}
          onClick={() => setOverlayOpen(true)}
        />
        {overlayOpen && (
          <div
            ref={overlayRef}
            className='absolute flex flex-col w-full top-[100%] mt-6 rounded-default bg-white p-4 shadow-lg'
          >
            <RoomsOverlayContent
              distribution={distribution}
              handleRemoveRoom={handleRemoveRoom}
              handleIncrement={handleIncrement}
              handleChildrenAge={handleChildrenAge}
              handleAddRoom={handleAddRoom}
              onApply={() => setOverlayOpen(false)}
            />
          </div>
        )}
      </div>
      <div className='w-full flex md:hidden'>
        <Sheet
          open={windowWidth < 768 ? overlayOpen : false}
          onOpenChange={setOverlayOpen}
        >
          <SheetTrigger className='w-full'>
            <RoomsInput
              label={t('search.rooms.label')}
              placeholder={distributionLabel}
              onClick={() => setOverlayOpen(true)}
            />
          </SheetTrigger>
          <SheetContent
            ref={overlayRef}
            side='bottom'
            className='max-h-96 rounded-t-default overflow-auto'
          >
            <SheetHeader className='text-start'>
              <div className='w-full flex flex-col gap-2 md:pl-1 md:flex-row mb-4 md:mb-0'>
                <RoomsOverlayContent
                  distribution={distribution}
                  handleRemoveRoom={handleRemoveRoom}
                  handleIncrement={handleIncrement}
                  handleChildrenAge={handleChildrenAge}
                  handleAddRoom={handleAddRoom}
                  onApply={() => setOverlayOpen(false)}
                />
              </div>
            </SheetHeader>
          </SheetContent>
        </Sheet>
      </div>
    </>
  );
};

export default RoomsSearch;
