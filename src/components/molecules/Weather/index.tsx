import { CloudR<PERSON>, CloudSun, Snowflake, Sun } from '@phosphor-icons/react';

interface IWeather {
  day: string;
  type: 'SUNNY' | 'RAINY' | 'CLOUDY' | 'SNOWING';
  maxTemperature: string;
  minTemperature: string;
  city: string;
}

const Weather = ({
  day,
  type,
  maxTemperature,
  minTemperature,
  city
}: IWeather) => {
  const types = {
    SUNNY: {
      icon: <Sun size={18} weight='bold' color='#c3e000' />,
      text: 'Ensolarado'
    },
    RAINY: {
      icon: <CloudRain size={18} weight='bold' />,
      text: 'Chuvoso'
    },
    CLOUDY: {
      icon: <CloudSun size={18} weight='bold' />,
      text: 'Nublado'
    },
    SNOWING: {
      icon: <Snowflake size={18} weight='bold' />,
      text: 'Nevando'
    }
  };

  return (
    <div className='w-full flex justify-between bg-gray-100 rounded-[10px] my-[5px] p-[15px]'>
      <div className='flex flex-col'>
        <p className='text-xs text-gray-500'>{city}</p>
        <h3 className='text-sm text-primary-900 font-medium'>{day}</h3>
        <div className='flex items-center gap-[5px] mt-[5px]'>
          {types[type]?.icon}
          <p className='text-sm text-primary-500 font-medium'>
            {types[type]?.text}
          </p>
        </div>
      </div>
      <div className='flex flex-col items-end font-medium'>
        <h3 className='text-xl text-primary-900'>{maxTemperature}</h3>
        <p className='text-sm text-gray-500'>{minTemperature}</p>
      </div>
    </div>
  );
};

export default Weather;
