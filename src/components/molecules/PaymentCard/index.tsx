import { ReactNode } from 'react';
import Badge from '@components/atoms/Badge';
import { useTranslation } from 'next-i18next';
import { ClockCountdown } from '@phosphor-icons/react';

interface IPaymentCard {
  icon?: ReactNode;
  title: string;
  description: string;
  expired?: boolean;
}

const PaymentCard = ({ icon, title, description, expired }: IPaymentCard) => {
  const { t } = useTranslation('bookings');

  return (
    <div className='flex items-center gap-2'>
      {icon ? (
        <div className='w-[45px] h-[45px] flex items-center justify-center bg-white rounded-full relative text-primary-500'>
          {icon}
        </div>
      ) : null}
      <div className='flex flex-col'>
        <h3 className='flex gap-2 font-semibold text-primary-900'>
          {title}
          {expired && (
            <Badge
              size='small'
              type='danger'
              icon={<ClockCountdown size={14} />}
            >
              {t('orderCard.status.paymentExpired')}
            </Badge>
          )}
        </h3>
        <p className='text-gray-500 text-sm'>{description}</p>
      </div>
    </div>
  );
};

export default PaymentCard;
