import { Radio } from '@components/atoms/Input';
import { FC, useState } from 'react';
import { useSearch } from 'src/context/SearchContext';
import { availability } from 'src/server/search';

interface ISearchRadioSortProps {}

const SearchRadioSort: FC<ISearchRadioSortProps> = () => {
  const {
    loading,
    searchResponse,
    searchPayload,
    setSearchResponse,
    setLoading
  } = useSearch();
  const [optionSelected, setOptionSelected] = useState(searchPayload.sorting);
  const handleSearch = async (sortOption: string) => {
    if (sortOption === optionSelected) return;
    try {
      setLoading(true);
      const payload = { ...searchPayload, sorting: sortOption };
      setOptionSelected(sortOption);
      const { data } = await availability(payload);
      setSearchResponse(data);
    } catch (err) {
      //
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className='flex flex-col gap-3'>
      {searchResponse.sorting.map(option => (
        <Radio
          id={option.label}
          checked={option.label === optionSelected}
          onClick={() => handleSearch(option.label)}
          disabled={loading}
          label={option.display}
          size='large'
        />
      ))}
    </div>
  );
};

export default SearchRadioSort;
