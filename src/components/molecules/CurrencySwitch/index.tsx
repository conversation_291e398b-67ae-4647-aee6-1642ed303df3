import { useCurrency, currencies } from 'src/context/CurrencyContext';
import { useState } from 'react';
import { CaretDown, CurrencyEur, CurrencyDollar } from '@phosphor-icons/react';
import { cn } from '@utils/classes';
import { i18n } from 'next-i18next';
import Dropdown, { DropdownItemType } from '../Dropdown';

type CurrencyItem = {
  id: string;
  text: string;
  onClick: () => void;
};

const CurrencySwitchTrigger = ({
  currencyCode,
  color
}: {
  currencyCode: string;
  color?: 'primary' | 'white';
}) => {
  const textClasses = cn(
    'font-normal',
    color === 'primary' ? 'text-primary-900' : 'text-white'
  );

  return (
    <div className='flex items-center gap-2 cursor-pointer text-primary'>
      <p className={textClasses}>{currencyCode}</p>
      <CaretDown className={textClasses} />
    </div>
  );
};

const CurrencySwitch = ({ color }: { color?: 'primary' | 'white' }) => {
  const { currency, setCurrency } = useCurrency();
  const [open, setOpen] = useState<boolean>(false);

  const changeCurrency = (code: string) => {
    setCurrency(code);
  };

  const currencyItems: CurrencyItem[] = Object.values(currencies)
    .filter(c => c.show)
    .map(c => ({
      id: c.code,
      text: `${c.code} - ${i18n!.t(`currencies.${c.code}`)}`,
      onClick: () => changeCurrency(c.code)
    }));

  const items: DropdownItemType[] = currencyItems.map(item => ({
    id: item.id,
    text: item.text,
    icon: item.id === 'EUR' ? <CurrencyEur /> : <CurrencyDollar />,
    checked: currency.code === item.id,
    onClick: () => item.onClick()
  }));

  return (
    <Dropdown items={items} open={open} onToggle={setOpen}>
      <CurrencySwitchTrigger currencyCode={currency.code} color={color} />
    </Dropdown>
  );
};

export default CurrencySwitch;
