import { IBooking<PERSON><PERSON> } from 'src/types/booking';
import { Check, ForkKnife, Warning } from '@phosphor-icons/react';
import {
  convertStringToDefaultFormatedDate,
  getDateFNSLocale
} from 'src/utils/date';
import { Room } from 'src/types/buy';
import Badge from '@components/atoms/Badge';
import GuestBadge from '@components/molecules/GuestBadge';
import { i18n, useTranslation } from 'next-i18next';

interface IReservationCardRoom {
  room: IBookingRoom | Room;
}

const ReservationCardRoom = ({ room }: IReservationCardRoom) => {
  const { t } = useTranslation('bookings');

  return (
    <div className='flex flex-col md:flex-row md:items-center justify-between bg-gray-100 rounded-button p-4 gap-4'>
      <div className='flex items-center gap-4'>
        <div className='flex flex-col gap-2 justify-between items-start'>
          <div className='flex items-center gap-2 flex-wrap'>
            <p className='text-sm text-gray-500'>
              {t('reservationCard.reservationCardRoom.room')}{' '}
              {room.accommodationIndex + 1}
            </p>
            {room.cancellationPolicies.refundable ? (
              <Badge
                icon={<Check weight='bold' size={14} />}
                type='success'
                size='small'
              >
                {t('reservationCard.reservationCardRoom.refundable')}
              </Badge>
            ) : (
              <Badge
                icon={<Warning weight='bold' size={14} />}
                type='warning'
                size='small'
              >
                {t('reservationCard.reservationCardRoom.nonRefundable')}
              </Badge>
            )}
            <Badge icon={<ForkKnife />} type='info' size='small'>
              {room.mealPlanDisplay}
            </Badge>
          </div>
          <div>
            <div className='flex items-center gap-1'>
              <h3 className='text-primary-900 font-semibold'>
                {room.accommodationName}
              </h3>
            </div>
            {room.cancellationPolicies.refundable ? (
              <p className='text-success-600 text-sm leading-3'>
                {t('reservationCard.reservationCardRoom.freeCancellationUntil')}{' '}
                {convertStringToDefaultFormatedDate(
                  getDateFNSLocale(i18n!.language),
                  room.cancellationPolicies.cancellationLimitDate
                )}
              </p>
            ) : null}
          </div>
        </div>
      </div>
      <div className='flex flex-wrap items-start gap-2'>
        {room.guests.map(guest => (
          <GuestBadge key={guest.id} {...guest} />
        ))}
      </div>
    </div>
  );
};

export default ReservationCardRoom;
