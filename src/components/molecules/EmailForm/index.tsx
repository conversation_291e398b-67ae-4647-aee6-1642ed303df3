/* eslint-disable no-unused-vars */
import Button from '@components/atoms/Button';
import { Input } from '@components/atoms/Input';
import React, { FC, use, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { EmailFormType } from 'src/types/checkout';
import LoginBox from '@components/organisms/LoginBox';
import { verifyAccount } from 'src/server/auth';
import { useTranslation } from 'next-i18next';
import { Sheet, SheetContent } from '@components/atoms/Sheet';
import useWindowWith from 'src/hooks/useWindowSize';
import Modal from '@components/molecules/Modal';

interface IEmailFormProps {
  handleEmailSubmit: (data: EmailFormType) => void;
  onLogin?: () => void;
}

const EmailForm: FC<IEmailFormProps> = ({ handleEmailSubmit, onLogin }) => {
  const { t } = useTranslation('checkout');
  const [loading, setLoading] = useState<boolean>(false);
  const [loginSheetOpen, setLoginSheetOpen] = useState<boolean>(false);
  const { register, handleSubmit, watch } = useForm<EmailFormType>({
    defaultValues: { email: '' },
    mode: 'onBlur',
    reValidateMode: 'onBlur'
  });

  const windowWidth = useWindowWith();

  const email = watch('email');

  const onSubmit = async (data: EmailFormType) => {
    setLoading(true);
    try {
      const response = await verifyAccount(data.email);
      if (response.data === 'login') {
        setLoginSheetOpen(true);
      } else if (response.data === 'signin') {
        handleEmailSubmit(data);
      }
    } catch (err) {
      //
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <form onSubmit={handleSubmit(data => onSubmit(data))}>
        <div className='flex flex-col my-4'>
          <h3 className='text-primary-900 font-semibold my-1'>
            {t('account.title')}
          </h3>
          <p className='text-gray-500'>{t('account.subtitle')}</p>
        </div>
        <div className='flex flex-col md:flex-row items-end gap-4'>
          <Input
            {...register('email', {
              required: t('validation.required')
            })}
            placeholder={t('account.input')}
            type='email'
            border
          />
          <Button
            size='large'
            type='submit'
            loading={loading}
            className='w-full md:w-auto'
          >
            {t('buttons.next')}
          </Button>
        </div>
      </form>
      {windowWidth >= 768 && (
        <div className='hidden md:block'>
          <Modal show={loginSheetOpen} onClose={() => setLoginSheetOpen(false)}>
            <LoginBox
              email={email}
              description={
                <span className='text-gray-500 mt-1'>
                  {t('account.login.description')}
                </span>
              }
              onLogin={() => {
                handleEmailSubmit({ email });
                setLoginSheetOpen(false);
                onLogin?.();
              }}
              expanded
              footer={
                <Button
                  onClick={() => setLoginSheetOpen(false)}
                  color='white'
                  type='button'
                >
                  {t('account.login.changeEmail')}
                </Button>
              }
            />
          </Modal>
        </div>
      )}
      <Sheet
        open={windowWidth < 768 && loginSheetOpen}
        onOpenChange={() => setLoginSheetOpen(false)}
      >
        <SheetContent side='bottom' className='rounded-t-default'>
          <LoginBox
            expanded
            email={email}
            description={
              <span className='text-gray-500 mt-1'>
                {t('account.login.description')}
              </span>
            }
            onLogin={() => {
              handleEmailSubmit({ email });
              setLoginSheetOpen(false);
              onLogin?.();
            }}
            footer={
              <Button
                onClick={() => setLoginSheetOpen(false)}
                color='white'
                type='button'
              >
                {t('account.login.changeEmail')}
              </Button>
            }
          />
        </SheetContent>
      </Sheet>
    </>
  );
};

export default EmailForm;
