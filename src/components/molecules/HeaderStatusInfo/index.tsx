import { FC } from 'react';
import Button from '@components/atoms/Button';
import { cva } from 'class-variance-authority';

interface IHeaderStatusInfo {
  info: any;
}

const HeaderStatusInfo: FC<IHeaderStatusInfo> = ({
  info
}: IHeaderStatusInfo) => {
  const statusClasses = cva('rounded-default p-4', {
    variants: {
      messageType: {
        success: 'bg-green-100/40 text-green-600',
        danger: 'bg-red-100/30 text-red-500',
        info: 'bg-primary-100/50 text-primary-900'
      }
    },
    defaultVariants: {
      messageType: 'info'
    }
  });

  return (
    <div
      className={statusClasses({ messageType: info?.messageType || 'info' })}
    >
      <div className='flex items-center justify-between gap-6 flex-col md:flex-row'>
        <div className='flex items-center gap-4'>
          <div className='w-11 h-11 bg-white flex items-center justify-center rounded-full'>
            {info?.icon}
          </div>
          <div className='flex flex-col gap-1'>
            <h2 className='text-base font-medium'>{info?.title}</h2>
            {info?.subtitle ? (
              <p className='text-sm text-gray-500 leading-none'>
                {info.subtitle}
              </p>
            ) : null}
          </div>
        </div>
        {info?.buttonText ? (
          <Button
            className='w-full md:w-auto'
            onClick={() => info?.buttonAction()}
          >
            {info.buttonText}
          </Button>
        ) : null}
      </div>
    </div>
  );
};

export default HeaderStatusInfo;
