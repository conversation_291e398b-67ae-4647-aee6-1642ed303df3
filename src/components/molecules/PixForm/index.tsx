/* eslint-disable no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
import Button from '@components/atoms/Button';
import { Checkbox, Input } from '@components/atoms/Input';
import { payerDefaultValues } from '@consts/checkout';
import { FC, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { ICheckoutRequestPayer } from 'src/types/checkout';
import {
  isValidCNPJ,
  isValidCpf,
  isValidEmail,
  isValidName
} from 'src/utils/validation';
import { useHookFormMask } from 'use-mask-input';
import { ArrowRight, ClockCountdown } from '@phosphor-icons/react';
import { useTranslation } from 'next-i18next';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@components/atoms/Input/NewSelect';
import PhoneInput from '@components/atoms/Input/Phone';

interface IPixForm {
  handlePixSubmit: (data: { pix: ICheckoutRequestPayer }) => void;
  toPreviousStep: Function;
  initialValues: { pix: ICheckoutRequestPayer };
  userValues?: ICheckoutRequestPayer;
  isAuthenticated: boolean;
}

const PixForm: FC<IPixForm> = ({
  userValues,
  initialValues,
  handlePixSubmit,
  isAuthenticated
}) => {
  const { t, i18n } = useTranslation('checkout');
  const [isCPF, setIsCPF] = useState<boolean>(false);
  const {
    register,
    handleSubmit,
    watch,
    reset,
    setValue,
    setFocus,
    getValues,
    clearErrors,
    formState: { errors }
  } = useForm<{ pix: ICheckoutRequestPayer }>({
    defaultValues: {
      ...initialValues,
      pix: {
        ...initialValues.pix,
        phone: {
          ...initialValues.pix.phone,
          ddi:
            initialValues.pix.phone?.ddi ||
            i18n.language.toUpperCase() === 'PT_BR'
              ? '+55'
              : ''
        },
        identification: {
          ...initialValues.pix.identification,
          documentType: 'CPF'
        }
      }
    },
    mode: 'onBlur',
    reValidateMode: 'onBlur'
  });

  const pixErrors = errors?.pix;
  const DDI = watch('pix.phone.ddi');

  const handlePixAsPayer = (checked: boolean) => {
    reset({
      pix: checked
        ? { ...userValues }
        : {
            ...{
              ...payerDefaultValues,
              phone: {
                ...payerDefaultValues.phone,
                ddi: i18n.language.toUpperCase() === 'PT_BR' ? '+55' : ''
              }
            }
          }
    });
  };

  const registerWithMask = useHookFormMask(register);
  const documentType = watch('pix.identification.documentType');
  const userAsPayer = watch('pix.userAsPayer');

  const handleDocumentTypeChange = (value: string) => {
    setIsCPF(value === 'CPF');
    setValue('pix.identification.documentType', value);
    clearErrors('pix.identification.documentType');
  };

  useEffect(() => {
    setIsCPF(initialValues?.pix?.identification?.documentType === 'CPF');
    setValue('pix', initialValues?.pix);
  }, [initialValues?.pix, setValue]);

  return (
    <form
      onSubmit={handleSubmit(data => handlePixSubmit(data))}
      className='flex flex-col gap-4'
    >
      <div className='flex flex-col gap-1'>
        <div className='flex items-center gap-2 py-4 px-5 rounded-default mb-3 bg-white'>
          <ClockCountdown
            size={18}
            weight='bold'
            className='text-primary-400'
          />
          <p className='text-primary-900'>{t('payer.warning.pix')}</p>
        </div>
        <h3 className='font-semibold text-primary-900'>{t('payer.title')}</h3>
        {isAuthenticated ? (
          <Checkbox
            label={t('payer.useMyInfo')}
            value={userAsPayer}
            onChange={data => handlePixAsPayer(data)}
          />
        ) : null}
      </div>
      <Input
        placeholder={`${t('payer.personalInfo.email')} *`}
        {...register('pix.email', {
          required: t('validation.required'),
          validate: val => isValidEmail(val) || t('validation.invalid.email')
        })}
        error={pixErrors?.email?.message}
      />
      <div className='flex items-center gap-6 flex-wrap'>
        <div className='flex-1'>
          <Input
            placeholder={`${t('payer.personalInfo.firstName')} *`}
            {...register('pix.firstName', {
              required: t('validation.required'),
              validate: val => isValidName(val) || t('validation.invalid.name')
            })}
            border
            error={pixErrors?.firstName?.message}
          />
        </div>
        <div className='flex-1'>
          <Input
            placeholder={`${t('payer.personalInfo.lastName')} *`}
            {...register('pix.lastName', {
              required: t('validation.required'),
              validate: val =>
                isValidName(val) || t('validation.invalid.surname')
            })}
            error={pixErrors?.lastName?.message}
          />
        </div>
      </div>
      <div className='flex items-center gap-6 flex-wrap'>
        <div className='flex-1'>
          <Select
            value={documentType}
            onValueChange={value => {
              if (value) {
                handleDocumentTypeChange(value);
              }
            }}
            {...register('pix.identification.documentType', {
              required: t('validation.required')
            })}
          >
            <SelectTrigger className='w-auto bg-white'>
              <SelectValue
                placeholder={t('payer.personalInfo.document.type')}
              />
            </SelectTrigger>
            <SelectContent position='item-aligned'>
              <SelectItem value='CPF'>CPF</SelectItem>
              <SelectItem value='CNPJ'>CNPJ</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className='flex-3'>
          <Input
            placeholder={`${t('payer.personalInfo.document.number')} *`}
            {...registerWithMask(
              'pix.identification.documentNumber',
              ['999.999.999-99', '99.999.999/9999-99'],
              {
                required: t('validation.required'),
                validate: val =>
                  isCPF
                    ? isValidCpf(val) || t('validation.invalid.cpf')
                    : isValidCNPJ(val) || t('validation.invalid.cnpj')
              }
            )}
            error={pixErrors?.identification?.documentNumber?.message}
          />
        </div>
      </div>
      <div className='flex items-center gap-6 flex-wrap'>
        <PhoneInput
          DDI={DDI}
          phoneDDIKey='pix.phone.ddi'
          phoneAreaCodeKey='pix.phone.areaCode'
          phoneNumberKey='pix.phone.number'
          registerWithMask={registerWithMask}
          setFocus={setFocus}
          errors={pixErrors as any}
          ddiClassName='bg-white'
          areaCodeClassName='bg-white'
          phoneClassName='bg-white'
        />
      </div>
      <div className='flex justify-between'>
        <div />
        <Button size='large' type='submit' className='w-full md:w-auto'>
          {t('buttons.next')}
          <ArrowRight />
        </Button>
      </div>
    </form>
  );
};

export default PixForm;
