/* eslint-disable no-shadow */
import { FC, HTMLAttributes, useContext, useEffect, useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import Button from '@components/atoms/Button';
import { AuthContext } from 'src/context/AuthContext';
import { List } from '@phosphor-icons/react';
import NavbarUser from '@components/molecules/NavbarUser';
import Sidebar from '@components/molecules/Sidebar';
import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import Divider from '@components/atoms/Divider';
import LanguageSwitch from '@components/molecules/LanguageSwitch';
import CurrencySwitch from '@components/molecules/CurrencySwitch';
import { cva } from 'class-variance-authority';
import { cn } from '@utils/classes';

const navbarVariants = cva(
  'w-full h-[70px] z-30 flex justify-between items-center transition-all duration-300',
  {
    variants: {
      fixed: {
        true: 'fixed top-0',
        false: 'relative'
      },
      color: {
        transparent: 'bg-transparent',
        white: 'bg-white',
        gray: 'bg-gray-100',
        primary: 'bg-primary-900'
      }
    }
  }
);

const linkColorVariants = cva('hidden md:block', {
  variants: {
    color: {
      white: 'text-white',
      primary: 'text-primary-900'
    }
  }
});

export interface NavbarStyle {
  fixed?: boolean;
  backgroundColor: 'transparent' | 'white' | 'primary' | 'gray';
  foregroundColor: 'white' | 'primary';
  scrollingBackgroundColor: 'transparent' | 'white' | 'primary' | 'gray';
  scrollingForegroundColor: 'white' | 'primary';
}

interface INavbar extends HTMLAttributes<HTMLDivElement>, NavbarStyle {}

const Navbar: FC<INavbar> = ({
  fixed,
  backgroundColor,
  foregroundColor,
  scrollingBackgroundColor,
  scrollingForegroundColor,
  className
}: INavbar) => {
  const [bgColor, setBgColor] = useState<
    'transparent' | 'white' | 'primary' | 'gray'
  >(backgroundColor);
  const [fgColor, setFgColor] = useState<'white' | 'primary'>(foregroundColor);
  const [scrolling, setScrolling] = useState<boolean>(false);
  const [sidebarOpen, setSidebarOpen] = useState<boolean>(false);

  const { isAuthenticated, user, logout } = useContext(AuthContext);
  const { t, i18n } = useTranslation('common');
  const router = useRouter();

  useEffect(() => {
    const onScroll = () => {
      const scrollCheck = window.scrollY > 10;
      if (scrollCheck !== scrolling) {
        setScrolling(!scrolling);
        setBgColor(!scrolling ? scrollingBackgroundColor : backgroundColor);
        setFgColor(!scrolling ? scrollingForegroundColor : foregroundColor);
      }
    };
    document.addEventListener('scroll', onScroll);
    return () => {
      document.removeEventListener('scroll', onScroll);
    };
  }, [
    scrolling,
    backgroundColor,
    foregroundColor,
    scrollingBackgroundColor,
    scrollingForegroundColor
  ]);

  return (
    <nav
      className={cn(
        navbarVariants({
          fixed,
          color: bgColor
        }),
        className
      )}
    >
      <div className='container'>
        <div className='w-full flex justify-between items-center'>
          <div className='flex items-center gap-12'>
            <Image
              onClick={() => router.push('/')}
              src={`https://ourtrips3.s3.amazonaws.com/new-logos/horizontal_icon_text_${
                bgColor === 'primary' || bgColor === 'transparent'
                  ? 'white'
                  : 'blue'
              }.png`}
              className='hidden md:block object-cover cursor-pointer'
              alt='Logo OurTrip'
              width={100}
              height={0}
            />
            <Image
              onClick={() => router.push('/')}
              src={`https://ourtrips3.s3.amazonaws.com/new-logos/icon_${
                bgColor === 'primary' || foregroundColor === 'white'
                  ? 'white'
                  : 'blue'
              }.png`}
              className='block md:hidden object-cover cursor-pointer'
              alt='Logo OurTrip'
              width={30}
              height={30}
            />
            <Link
              href='/'
              className={cn(linkColorVariants({ color: fgColor }))}
            >
              {t('navbar.home')}
            </Link>
            <Link
              href='/tripcash'
              className={cn(linkColorVariants({ color: fgColor }))}
            >
              {t('navbar.tripcash.title')}
            </Link>
          </div>

          <div className='flex md:hidden gap-6 items-center'>
            <CurrencySwitch color={fgColor} />
            <div className='h-5'>
              <Divider
                fullHeight
                orientation='vertical'
                className='opacity-50'
              />
            </div>
            <LanguageSwitch color={fgColor} />
            <List
              size={28}
              onClick={() => setSidebarOpen(true)}
              className={`${
                fgColor === 'white' ? 'text-white' : 'text-primary-900'
              } flex-none`}
            />
          </div>
          <div className='hidden md:flex items-center gap-6'>
            <CurrencySwitch color={fgColor} />
            <div className='h-5'>
              <Divider
                fullHeight
                orientation='vertical'
                className='opacity-50'
              />
            </div>
            <LanguageSwitch color={fgColor} />
            {isAuthenticated ? (
              <NavbarUser
                name={user!.name}
                tripcash={user!.tripcashBalances}
                lastname={user!.surname}
                logout={logout}
              />
            ) : (
              <div className='flex items-center gap-3'>
                <Link href={t('routes.signIn')} locale={i18n.language}>
                  <Button variant='outline' color={fgColor}>
                    {t('navbar.signIn')}
                  </Button>
                </Link>
                <Link href={t('routes.signUp')} locale={i18n.language}>
                  <Button color={fgColor}>{t('navbar.signUp')}</Button>
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
      <Sidebar open={sidebarOpen} onClose={() => setSidebarOpen(false)} />
    </nav>
  );
};

export default Navbar;
