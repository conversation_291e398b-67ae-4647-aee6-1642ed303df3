import { useState } from 'react';
import Dropdown, { DropdownItemType } from '@components/molecules/Dropdown';
import {
  HandCoins,
  SignOut,
  UserCircle,
  UserList
} from '@phosphor-icons/react';
import { useRouter } from 'next/router';
import TripcashCardBalance from '@components/molecules/TripcashCardBalance';
import { useTranslation } from 'next-i18next';

interface INavbarUser {
  name: string;
  lastname: string;
  tripcash?: string[];
  logout?: Function;
}

const NavbarUser = ({ name, lastname, tripcash, logout }: INavbarUser) => {
  const router = useRouter();

  const [open, setOpen] = useState<boolean>(false);
  const { t, i18n } = useTranslation('common');

  const dropdownItems: DropdownItemType[] = [
    {
      type: 'divider'
    },
    {
      icon: <UserCircle size={22} />,
      text: t('navbar.myProfile'),
      onClick: () =>
        router.push({ pathname: t('routes.myProfile') }, '', {
          locale: i18n.language
        })
    },
    {
      icon: <UserList size={22} />,
      text: t('navbar.myReservations'),
      onClick: () =>
        router.push({ pathname: t('routes.myReservations') }, '', {
          locale: i18n.language
        })
    },
    {
      icon: <HandCoins size={22} />,
      text: t('navbar.myTripcash'),
      onClick: () =>
        router.push({ pathname: t('routes.myTripcash') }, '', {
          locale: i18n.language
        })
    },
    {
      type: 'divider'
    },
    {
      icon: <SignOut size={22} />,
      text: t('navbar.logout'),
      onClick: logout
    }
  ];

  return (
    <Dropdown
      open={open}
      items={dropdownItems}
      onToggle={setOpen}
      header={
        <TripcashCardBalance
          label={t('navbar.tripcash.balance')}
          values={tripcash}
        />
      }
    >
      <div
        className='flex items-center gap-2.5 bg-white py-2 px-5 rounded-md cursor-pointer relative select-none'
        onClick={() => setOpen(prev => !prev)}
      >
        <div className='w-7 h-7 rounded-full bg-gray-200 text-primary-500 text-xs flex justify-center items-center font-bold'>
          {name.charAt(0) + lastname.charAt(0)}
        </div>
        <h3 className='text-primary-900 font-medium'>{`${name.split(' ')[0]} ${
          lastname.split(' ')[0]
        }`}</h3>
      </div>
    </Dropdown>
  );
};

export default NavbarUser;
