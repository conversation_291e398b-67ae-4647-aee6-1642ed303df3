/* eslint-disable react/no-array-index-key */
import { FC } from 'react';
import Divider from '@components/atoms/Divider';

import HotelInfoResume from '@components/atoms/HotelInfoResume';
import {
  Check,
  ForkKnife,
  Warning,
  WarningCircleIcon
} from '@phosphor-icons/react';
import Badge from '@components/atoms/Badge';
import Collapse from '@components/atoms/Collapse';
import { useTranslation } from 'next-i18next';

export type PurchaseResumeConfigType = {
  index: number;
  title?: string;
  text: string;
  value: string;
  description?: string;
  refundable?: boolean;
  rateComments?: string[];
  mealPlan: string;
};

interface IPurchaseResume {
  hotelImage?: string;
  hotelName?: string;
  hotelAddress?: string;
  hotelStars?: number;
  checkinDate: string;
  checkinTime?: string;
  checkoutDate: string;
  checkoutTime?: string;
  configs: PurchaseResumeConfigType[];
}

const PurchaseResume: FC<IPurchaseResume> = ({
  hotelImage,
  hotelName,
  hotelAddress,
  hotelStars,
  checkinDate,
  checkinTime,
  checkoutDate,
  checkoutTime,
  configs
}: IPurchaseResume) => {
  const { t } = useTranslation('hotel');

  return (
    <div className='flex flex-col rounded-default bg-white p-2 md:p-6'>
      <h3 className='text-primary-900 text-lg font-medium'>
        {t('purchaseResume.title')}
      </h3>
      <div className='flex flex-col gap-6 mt-6'>
        {hotelName ? (
          <>
            <HotelInfoResume
              stars={hotelStars}
              address={hotelAddress}
              name={hotelName}
              image={hotelImage}
            />
            <Divider orientation='horizontal' />
          </>
        ) : null}
        <div className='flex gap-4'>
          <div className='flex flex-col flex-1'>
            <p className='text-sm text-primary-900 leading-none'>
              {t('purchaseResume.checkin')}
            </p>
            <h3 className='font-semibold text-sm text-primary-900 leading-tight mt-3'>
              {checkinDate}
            </h3>
            {checkinTime ? (
              <span className='font-normal text-xs text-gray-500 leading-none mt-2'>
                {checkinTime}
              </span>
            ) : null}
          </div>
          <Divider />
          <div className='flex flex-col flex-1 items-end text-right'>
            <p className='text-sm text-primary-900 leading-none'>
              {t('purchaseResume.checkout')}
            </p>
            <h3 className='font-semibold text-sm text-primary-900 leading-tight mt-3'>
              {checkoutDate}
            </h3>
            {checkoutTime ? (
              <span className='font-normal text-xs text-gray-500 leading-none mt-2'>
                {checkoutTime}
              </span>
            ) : null}
          </div>
        </div>
        {configs.length > 0 && <Divider orientation='horizontal' />}
        <div className='flex flex-col gap-3'>
          {configs.map((config, index) => (
            <div className='flex flex-col' key={`${config.text}-${index}`}>
              <p className='font-light text-sm text-gray-500 leading-none'>
                {config.title}
              </p>
              {config.description && (
                <span className='mt-2 text-sm text-success-500'>
                  {config.description}
                </span>
              )}
              <div className='w-full flex justify-between items-start gap-2'>
                <h3 className='font-semibold text-base text-primary-900 leading-snug overflow-hidden line-clamp-2'>
                  {config.text}
                </h3>
                <p className='text-sm text-gray-500 leading-normal whitespace-nowrap'>
                  {config.value}
                </p>
              </div>
              <div className='flex flex-col items-start'>
                <div className='mt-2 flex flex-wrap gap-1'>
                  {config.refundable && (
                    <Badge
                      icon={<Check weight='bold' size={14} />}
                      type='success'
                      size='small'
                    >
                      {t('purchaseResume.refundable')}
                    </Badge>
                  )}
                  {config.refundable === false && (
                    <Badge
                      icon={<Warning weight='bold' size={14} />}
                      type='warning'
                      size='small'
                    >
                      {t('purchaseResume.nonRefundable')}
                    </Badge>
                  )}
                  <Badge
                    icon={<ForkKnife weight='bold' size={14} />}
                    type='info'
                    size='small'
                  >
                    {config.mealPlan}
                  </Badge>
                </div>
              </div>
              {config.rateComments?.map(rateComment => (
                <div className='my-2 text-sm' key={rateComment}>
                  <Collapse
                    icon={
                      <WarningCircleIcon
                        weight='bold'
                        className='text-red-600 animate-ping'
                        size={14}
                      />
                    }
                    title={t('purchaseResume.aboutRoom')}
                    content={<span>{rateComment}</span>}
                    showPreview
                  />
                </div>
              ))}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PurchaseResume;
