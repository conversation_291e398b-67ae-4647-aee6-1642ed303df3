import Button from '@components/atoms/Button';
import { Checkbox, Input } from '@components/atoms/Input';
import { payerDefaultValues } from '@consts/checkout';
import { FC, useCallback, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { ICheckoutRequestPayer } from 'src/types/checkout';
import { isValidEmail, isValidName } from 'src/utils/validation';
import { useHookFormMask } from 'use-mask-input';
import { i18n, useTranslation } from 'next-i18next';
import { ArrowRight } from '@phosphor-icons/react';
import PhoneInput from '@components/atoms/Input/Phone';

interface IExternalPaymentForm {
  // eslint-disable-next-line no-unused-vars
  onSubmit: (data: { external: ICheckoutRequestPayer }) => void;
  toPreviousStep: Function;
  initialValues: { external: ICheckoutRequestPayer };
  userValues?: ICheckoutRequestPayer;
  isAuthenticated: boolean;
}

const ExternalPaymetForm: FC<IExternalPaymentForm> = ({
  onSubmit,
  initialValues,
  userValues,
  isAuthenticated
}) => {
  const { t } = useTranslation('checkout');
  const {
    register,
    handleSubmit,
    watch,
    reset,
    setValue,
    setFocus,
    formState: { errors }
  } = useForm<{ external: ICheckoutRequestPayer }>({
    defaultValues: {
      ...initialValues,
      external: {
        ...initialValues.external,
        phone: {
          ...initialValues.external.phone,
          ddi:
            initialValues.external.phone!.ddi ||
            i18n!.language.toUpperCase() === 'PT_BR'
              ? '+55'
              : ''
        },
        identification: {
          ...initialValues.external.identification,
          documentType: 'DN'
        }
      }
    },
    mode: 'onBlur',
    reValidateMode: 'onBlur'
  });

  const formErros = errors?.external;
  const DDI = watch('external.phone.ddi');

  const handleUserAsPayer = useCallback(
    (checked: boolean) => {
      reset({
        external: checked
          ? { ...userValues }
          : {
              ...payerDefaultValues,
              identification: { documentType: 'DN', documentNumber: '' }
            }
      });
    },
    [reset, userValues]
  );

  useEffect(() => {
    const email = initialValues?.external.email;
    if (email) setValue('external.email', email);
  }, [initialValues?.external.email, setValue]);

  const registerWithMask = useHookFormMask(register);
  const userAsPayer = watch('external.userAsPayer');

  return (
    <form
      onSubmit={handleSubmit(data => onSubmit({ ...data }))}
      className='flex flex-col gap-4'
    >
      <div className='flex flex-col gap-2'>
        <h3 className='font-semibold text-primary-900'>{t('payer.title')}</h3>
        {isAuthenticated ? (
          <Checkbox
            label={t('payer.useMyInfo')}
            value={userAsPayer}
            onChange={data => handleUserAsPayer(data)}
          />
        ) : null}
      </div>
      <Input
        placeholder={`${t('payer.personalInfo.email')} *`}
        disabled={isAuthenticated && userAsPayer}
        {...register('external.email', {
          required: t('validation.required'),
          validate: val => isValidEmail(val) || t('validation.invalid.email')
        })}
        border
        error={formErros?.email?.message}
      />
      <div className='flex items-center gap-3 flex-wrap'>
        <div className='flex-1'>
          <Input
            placeholder={`${t('payer.personalInfo.firstName')} *`}
            {...register('external.firstName', {
              required: t('validation.required'),
              validate: val => isValidName(val) || t('validation.invalid.name')
            })}
            border
            error={formErros?.firstName?.message}
          />
        </div>
        <div className='flex-1'>
          <Input
            placeholder={`${t('payer.personalInfo.lastName')} *`}
            {...register('external.lastName', {
              required: t('validation.required'),
              validate: val =>
                isValidName(val) || t('validation.invalid.surname')
            })}
            border
            error={formErros?.lastName?.message}
          />
        </div>
      </div>
      <div className='flex items-center gap-3 flex-wrap'>
        <div className='flex-1'>
          <Input
            placeholder={`${t('payer.personalInfo.document.type')} *`}
            {...register('external.identification.documentNumber', {
              required: t('validation.required')
            })}
            border
            error={formErros?.identification?.documentNumber?.message}
          />
        </div>
      </div>
      <div className='flex items-center gap-3 flex-wrap'>
        <PhoneInput
          DDI={DDI}
          phoneDDIKey='external.phone.ddi'
          phoneAreaCodeKey='external.phone.areaCode'
          phoneNumberKey='external.phone.number'
          registerWithMask={registerWithMask}
          setFocus={setFocus}
          errors={formErros as any}
          ddiClassName='bg-white'
          areaCodeClassName='bg-white'
          phoneClassName='bg-white'
        />
      </div>
      <div className='flex justify-between'>
        <div />
        <Button size='large' type='submit' className='w-full md:w-auto'>
          {t('buttons.next')}
          <ArrowRight />
        </Button>
      </div>
    </form>
  );
};

export default ExternalPaymetForm;
