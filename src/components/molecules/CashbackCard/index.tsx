import { FC } from 'react';
import { HandCoins } from '@phosphor-icons/react';
import { useTranslation } from 'next-i18next';

interface ICashbackCard {
  percentage?: string | null;
  currencySymbol?: string;
  value: string | null;
}

const CashbackCard: FC<ICashbackCard> = ({
  percentage,
  currencySymbol,
  value
}: ICashbackCard) => {
  const { t } = useTranslation('common');

  return (
    <div className='w-full flex gap-4 relative bg-white rounded-default p-6'>
      {percentage && (
        <div className='w-12 h-12 rounded-full bg-yellow text-white font-bold absolute -top-2.5 -right-2.5 flex items-center justify-center'>
          {percentage}
        </div>
      )}
      <HandCoins weight='duotone' size={50} />
      <div className='flex flex-col gap-1 flex-1'>
        <h3 className='font-semibold text-lg text-primary-900'>
          {t('cashbackCard.title')}
        </h3>
        <div className='font-normal leading-relaxed text-base'>
          {value ? (
            <p
              className='[&>span]:font-bold [&>span]:text-primary-500'
              dangerouslySetInnerHTML={{
                __html: t('cashbackCard.description.withValue', {
                  value: `${currencySymbol ?? ''} ${value}`
                })
              }}
            />
          ) : (
            <>{t('cashbackCard.description.withoutValue')}</>
          )}
        </div>
      </div>
    </div>
  );
};

export default CashbackCard;
