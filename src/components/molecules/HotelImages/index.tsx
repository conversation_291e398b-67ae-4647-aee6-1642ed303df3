/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/img-redundant-alt */
/* eslint-disable @next/next/no-img-element */
import { FC, useState } from 'react';
import { IHotelPhoto } from 'src/types/hotel';
import Gallery from '@components/organisms/Gallery';

interface IHotelImages {
  cover?: IHotelPhoto;
  images?: IHotelPhoto[];
}

const HotelImages: FC<IHotelImages> = ({ cover, images }: IHotelImages) => {
  const [isGalleryOpen, setIsGalleryOpen] = useState<boolean>(false);
  const startGridImage = !cover?.url ? 0 : 1;

  const handleCloseGallery = () => {
    setIsGalleryOpen(false);
  };

  return (
    <div className='flex gap-2.5'>
      <div className='flex relative flex-1'>
        <img
          key={cover?.url || images![0]?.url || ''}
          src={cover?.url || images![0]?.url || ''}
          onClick={() => setIsGalleryOpen(true)}
          alt='Image capa do hotel'
          className='w-full h-full object-cover rounded-default aspect-square cursor-pointer'
        />
      </div>
      <div className='h-full grid grid-cols-2 auto-rows-fr gap-2.5 flex-1'>
        {images
          ?.slice(startGridImage, startGridImage + 4)
          .map((image, index) =>
            image.url ? (
              <img
                key={image.url}
                src={image.url}
                alt={`Image ${index + 1} capa do hotel`}
                width={500}
                height={500}
                onClick={() => setIsGalleryOpen(true)}
                className='w-full h-full object-cover rounded-default aspect-square cursor-pointer'
              />
            ) : null
          )}
      </div>
      <Gallery
        open={isGalleryOpen}
        images={images}
        onClose={handleCloseGallery}
      />
    </div>
  );
};

export default HotelImages;
