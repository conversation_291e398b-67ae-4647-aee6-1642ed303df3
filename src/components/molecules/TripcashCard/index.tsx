import Button from '@components/atoms/Button';
import { useRouter } from 'next/router';
import TripcashCardBalance from '@components/molecules/TripcashCardBalance';
import { useTranslation } from 'next-i18next';

interface ITripcashCard {
  tripcashBalance?: string[];
}

const TripcashCard = ({ tripcashBalance }: ITripcashCard) => {
  const { t, i18n } = useTranslation(['account', 'common'], {
    nsMode: 'fallback'
  });

  const router = useRouter();

  return (
    <div className='flex flex-col gap-4 bg-white p-6 rounded-default'>
      <p className='text-gray-500'>{t('account.tripcash.title')}</p>
      <TripcashCardBalance values={tripcashBalance} />
      <Button
        fullWidth
        variant='outline'
        onClick={() =>
          router.push({ pathname: t('routes.myTripcash') }, '', {
            locale: i18n.language
          })
        }
      >
        {t('account.tripcash.details')}
      </Button>
    </div>
  );
};

export default TripcashCard;
