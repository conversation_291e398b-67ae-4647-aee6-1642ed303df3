import { FC } from 'react';
import { MapPin } from '@phosphor-icons/react';
import Stars from '@components/atoms/Stars';
import Badge from '@components/atoms/Badge';

interface IHotelHeader {
  name?: string;
  location?: string;
  stars: number | null;
  type?: string;
}

const HotelHeader: FC<IHotelHeader> = ({
  name,
  location,
  stars,
  type
}: IHotelHeader) => {
  return (
    <div className='w-full flex justify-between items-end md:mt-12 mb-6'>
      <div className='w-full'>
        <div className='flex items-baseline justify-between md:justify-start gap-2'>
          <div className='font-medium md:font-semibold text-primary-900 text-xl md:text-2xl'>
            {name || ''}
          </div>
          {type && (
            <Badge type='success' size='small'>
              {type}
            </Badge>
          )}
          {stars ? <Stars rate={stars} /> : null}
        </div>
        <div className='flex items-center gap-1'>
          <MapPin size={16} weight='bold' className='text-primary-900' />
          <p className='text-sm text-gray-500'>{location || ''}</p>
        </div>
      </div>
      <div className='flex flex-col items-end' />
    </div>
  );
};

export default HotelHeader;
