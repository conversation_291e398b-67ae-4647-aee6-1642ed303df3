/* eslint-disable react/no-array-index-key */
import { useRef, useState, TouchEvent } from 'react';
import { CaretLeft, CaretRight } from '@phosphor-icons/react';
import Gallery from '@components/organisms/Gallery';
import { cn } from 'src/utils/classes';

interface IImageCarousel {
  cover?: string;
  images: string[];
  indicator?: boolean;
  className?: string;
}

const ImageCarousel = ({
  cover,
  images,
  indicator,
  className
}: IImageCarousel) => {
  const getImagesArray = () => {
    let urlList: string[] = [];
    if (cover && images.length) urlList = [cover, ...images];
    if (cover && !images.length) urlList = [cover];
    if (!cover && images.length) urlList = [...images];
    if (!cover && !images.length) urlList = ['/images/hotel-placeholder.webp'];

    urlList = Array.from(new Set(urlList.map((url: string) => url)));
    return urlList;
  };

  const [imageList] = useState(getImagesArray());
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const [galleryOpen, setGalleryOpen] = useState<boolean>(false);
  const carousel = useRef<HTMLDivElement>(null);

  const isDragging = useRef<boolean>(false);
  const startX = useRef<number>(0);
  const scrollLeft = useRef<number>(0);

  const handleTouchStart = (e: TouchEvent<HTMLDivElement>) => {
    isDragging.current = true;
    startX.current = e.touches[0].pageX;
    scrollLeft.current = carousel.current?.scrollLeft || 0;
  };

  const handleTouchMove = (e: TouchEvent<HTMLDivElement>) => {
    if (!isDragging.current) return;
    const x = e.touches[0].pageX;
    const walk = x - startX.current;
    if (carousel.current) {
      carousel.current.scrollLeft = scrollLeft.current - walk;
    }
  };

  const handleTouchEnd = () => {
    isDragging.current = false;
    if (carousel.current) {
      const newIndex = Math.round(
        carousel.current.scrollLeft / carousel.current.clientWidth
      );
      setCurrentIndex(newIndex);
      carousel.current.scrollTo({
        left: newIndex * carousel.current.clientWidth,
        behavior: 'smooth'
      });
    }
  };

  const next = (e: any) => {
    e.stopPropagation();
    if (currentIndex < imageList.length - 1) {
      carousel.current?.scrollTo({
        left: (currentIndex + 1) * (carousel.current?.clientWidth || 0),
        behavior: 'smooth'
      });
      setCurrentIndex(prev => prev + 1);
    }
  };

  const previous = (e: any) => {
    e.stopPropagation();
    if (currentIndex > 0) {
      carousel.current?.scrollTo({
        left: (currentIndex - 1) * (carousel.current?.clientWidth || 0),
        behavior: 'smooth'
      });
      setCurrentIndex(prev => prev - 1);
    }
  };

  const classes = cn(className, 'relative cursor-pointer flex-none');

  return (
    <div
      className={classes}
      onClick={() => {
        if (imageList.length > 1) setGalleryOpen(true);
      }}
    >
      <div
        ref={carousel}
        className='w-full h-full flex snap-mandatory overflow-hidden'
      >
        {imageList.map((image, index) => (
          <img
            key={`${image}-${index}`}
            src={image}
            alt='Imagem'
            className='relative w-full h-full flex-none object-cover snap-center'
          />
        ))}
      </div>
      {imageList.length > 1 && (
        <div
          className='w-full h-full absolute top-0 p-4 flex justify-between items-center'
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        >
          <div
            className='w-6 h-6 rounded-full bg-primary-900/60 hover:bg-primary-500 flex items-center justify-center'
            onClick={previous}
            aria-hidden='true'
          >
            <CaretLeft className='text-white' />
          </div>
          <div
            className='w-6 h-6 rounded-full bg-primary-900/60 hover:bg-primary-500 flex items-center justify-center'
            onClick={next}
            aria-hidden='true'
          >
            <CaretRight className='text-white' />
          </div>
        </div>
      )}
      {indicator && (
        <div className='w-full absolute bottom-0 left-0 flex justify-center'>
          <div className='py-1 px-2 mb-8 bg-black/60 text-white text-xs whitespace-nowrap rounded-button'>
            {currentIndex + 1} de {imageList.length}
          </div>
        </div>
      )}
      <Gallery
        open={galleryOpen}
        onClose={() => setGalleryOpen(false)}
        images={images.map(imageUrl => ({ url: imageUrl }))}
      />
    </div>
  );
};

export default ImageCarousel;
