/* eslint-disable react/no-array-index-key */
import { FC, useContext, useEffect } from 'react';
import { IHotelOfferPlan } from 'src/types/hotel';
import { OffersContext } from 'src/context/OffersContext';
import { useTranslation } from 'next-i18next';
import HotelAvailableRoom from '../HotelAvailableRoom';
import HotelAvailableRoomSkeleton from '../Skeletons/HotelAvailableRoom';

interface IHotelAvailableRooms {
  rooms: IHotelOfferPlan[];
  onChange?: Function;
  loading?: boolean;
}

const HotelAvailableRooms: FC<IHotelAvailableRooms> = ({
  rooms,
  onChange,
  loading
}: IHotelAvailableRooms) => {
  const { t } = useTranslation('hotel');
  const { selectedOffers } = useContext(OffersContext);

  useEffect(() => {
    onChange?.(selectedOffers);
  }, [onChange, selectedOffers]);

  return (
    <div className='w-full flex flex-col items-start'>
      <h3 className='text-lg text-primary-900 font-medium mb-4'>
        {t('availableRooms.title')}
      </h3>
      <div className='w-full flex flex-col gap-6'>
        {loading ? <HotelAvailableRoomSkeleton /> : null}
        {!loading &&
          rooms?.length > 0 &&
          rooms.map(room => (
            <HotelAvailableRoom
              key={room.accommodationId}
              amenitiesGroups={room.amenitiesGroups}
              accommodationName={room.accommodationName}
              photoCover={room.photoCover?.url}
              photos={room.photos?.map(photo => photo?.url!)}
              refundable={room.cancellationPolicies.refundable}
              cancellationDate={room.cancellationPolicies.cancellationLimitDate}
              mealPlan={room.mealPlan.description}
              offers={room.offers}
            />
          ))}
      </div>
    </div>
  );
};

export default HotelAvailableRooms;
