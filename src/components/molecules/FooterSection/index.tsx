import { ReactNode } from 'react';

interface IFooterSection {
  title: string;
  children?: ReactNode;
}

const FooterSection = ({ title, children }: IFooterSection) => {
  return (
    <div className='flex flex-col'>
      {title && (
        <h4 className='text-primary-900 font-semibold mb-6'>{title}</h4>
      )}
      {children && (
        <div className='w-full flex flex-col gap-4 text-sm'>{children}</div>
      )}
    </div>
  );
};

export default FooterSection;
