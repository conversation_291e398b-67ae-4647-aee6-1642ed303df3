import { FC, ReactNode } from 'react';
import useBlockScroll from 'src/hooks/useBlockScroll';
import { useRive } from '@rive-app/react-canvas';

interface ILoadingProps {
  loading: boolean;
  children: ReactNode;
}

const Loading: FC<ILoadingProps> = ({ loading, children }) => {
  useBlockScroll(loading);

  const { RiveComponent } = useRive({
    src: '/animations/loading.riv',
    stateMachines: 'Loading',
    autoplay: true
  });

  return loading ? (
    <div className='fixed flex flex-col justify-center items-center z-100000 bg-white/80 top-0 left-0 w-full h-full backdrop-blur-xl gap-2'>
      <div className='w-12 h-12'>
        <RiveComponent />
      </div>
      {children}
    </div>
  ) : null;
};

export default Loading;
