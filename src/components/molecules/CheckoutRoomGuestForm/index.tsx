/* eslint-disable react/no-array-index-key */
import Input from '@components/atoms/Input/Input';
import {
  isFullName,
  isValidAdult,
  isValidChildDate,
  isValidCpf
} from '@utils/validation';
import { TFunction } from 'i18next';
import { FieldErrors } from 'react-hook-form';
import { ICartRoomDetails } from 'src/types/cart';
import {
  GuestFormType,
  PaymentMethodEnum,
  PaymentMethodType
} from 'src/types/checkout';
import { childrenAgeOptions } from '@consts/search';

interface ICheckoutRoomGuestFormProps {
  t: TFunction;
  room: ICartRoomDetails;
  index: number;
  paymentMethod: PaymentMethodType;
  registerWithMask: any;
  register: any;
  errors: FieldErrors<GuestFormType & { userAsFirstGuest: boolean }>;
}

const CheckoutRoomGuestForm = ({
  t,
  room,
  index,
  paymentMethod,
  register,
  registerWithMask,
  errors
}: ICheckoutRoomGuestFormProps) => {
  return (
    <div key={`room-${index}`} className='my-6'>
      <h3 className='font-medium text-primary-900'>
        {t('room.index', { index: index + 1 })}
      </h3>
      <div className='flex flex-col gap-2 mt-2'>
        {Array.from(Array(room.adults)).map((_, adultIndex) => {
          return (
            <div key={`adult-${adultIndex}`} className='flex flex-col'>
              {adultIndex === 0 ? (
                <div className='w-full pb-3'>
                  <Input
                    id='guest-1'
                    label={t('payer.personalInfo.document.type')}
                    error={errors?.room?.[index]?.document?.message}
                    {...(paymentMethod === PaymentMethodEnum.EXTERNAL
                      ? register(`room.${index}.document`, {
                          required: t('validation.required')
                        })
                      : registerWithMask(`room.${index}.document`, 'cpf', {
                          required: t('validation.required'),
                          validate: (val: string) =>
                            isValidCpf(val) || t('validation.invalid.cpf')
                        }))}
                  />
                </div>
              ) : null}
              <div className='flex flex-col gap-4 md:flex-row'>
                <Input
                  label={`${t('payer.personalInfo.fullName')} (${t(
                    'guests.adultIndex',
                    { index: adultIndex + 1 }
                  )})`}
                  {...register(`room.${index}.adult.${adultIndex}.name`, {
                    required: t('validation.required'),
                    validate: (val: string) =>
                      isFullName(val) || t('validation.invalid.name')
                  })}
                  error={
                    errors?.room?.[index]?.adult?.[adultIndex]?.name?.message
                  }
                />
                {room?.requiredGuestInformation?.birthday && (
                  <Input
                    type='date'
                    label={`${t('guests.birth')} (${t('guests.adultIndex', {
                      index: adultIndex + 1
                    })})`}
                    {...register(`room.${index}.adult.${adultIndex}.birthday`, {
                      required: t('validation.required'),
                      validate: {
                        validDate: (val: string) => isValidAdult(val)
                      }
                    })}
                    error={
                      errors?.room?.[index]?.adult?.[adultIndex]?.birthday
                        ?.message
                    }
                  />
                )}
              </div>
            </div>
          );
        })}
        {room?.kids?.map((age, kidsIndex) => {
          return (
            <div
              key={`kids-${kidsIndex}`}
              className='flex flex-col gap-4 md:flex-row'
            >
              <Input
                {...register(`room.${index}.kid.${kidsIndex}.name`, {
                  required: t('validation.required'),
                  validate: (val: string) =>
                    isFullName(val) || t('validation.invalid.name')
                })}
                error={errors?.room?.[index]?.kid?.[kidsIndex]?.name?.message}
                label={`${t(
                  'payer.personalInfo.fullName'
                )} (${childrenAgeOptions[age].label.toLowerCase()})`}
              />
              {room?.requiredGuestInformation?.birthday && (
                <Input
                  type='date'
                  {...register(`room.${index}.kid.${kidsIndex}.birthday`, {
                    required: t('validation.required'),
                    validate: {
                      validDate: (val: string) => isValidChildDate(val)
                    }
                  })}
                  label={`${t('guests.birth')} (${childrenAgeOptions[
                    age
                  ].label.toLowerCase()})`}
                  error={
                    errors?.room?.[index]?.kid?.[kidsIndex]?.birthday?.message
                  }
                />
              )}
              <input
                style={{
                  visibility: 'hidden',
                  position: 'absolute',
                  height: 0
                }}
                {...register(`room.${index}.kid.${kidsIndex}.age`)}
                value={age}
              />
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default CheckoutRoomGuestForm;
