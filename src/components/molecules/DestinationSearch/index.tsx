/* eslint-disable consistent-return */
/* eslint-disable no-shadow */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-unused-vars */
import { FC, useEffect, useRef, useState } from 'react';
import {
  CircleNotch,
  House,
  MapPinLine,
  PencilSimple,
  SmileyXEyes
} from '@phosphor-icons/react';
import { IDestination, IDestinationSearchGroup } from 'src/types/search';
import { getDestinationAutoComplete } from 'src/server/search';
import { useTranslation } from 'next-i18next';
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTrigger
} from '@components/atoms/Sheet';
import useWindowWith from 'src/hooks/useWindowSize';
import useOnClickOutside from 'src/hooks/useClickOutside';

type DestinationProps = {
  label: string;
  placeholder: string;
  value: IDestination;
  onChange?: (destination: IDestination) => void;
};

const destinationGroupIcons = {
  CITY: <MapPinLine size={18} />,
  HOTEL: <House size={18} />,
  '': ''
};

const DestinationInput = ({
  label,
  value,
  placeholder,
  onChange,
  setPopoverOpen
}: any) => {
  return (
    <div className='w-full flex gap-4' onClick={() => setPopoverOpen(true)}>
      <div className='flex flex-none items-center justify-center w-12 text-primary-900 bg-gray-100 rounded-button'>
        <MapPinLine size={22} />
      </div>
      <div className='w-full flex flex-col'>
        <p className='font-medium text-primary-900 text-start'>{label}</p>
        <input
          value={value}
          placeholder={placeholder}
          onChange={onChange}
          className='w-full text-ellipsis text-gray-500'
        />
      </div>
    </div>
  );
};

const DestinationOverlayContent = ({
  value,
  loading,
  optionsGroup,
  handleOptionSelect
}: {
  value: string;
  loading: boolean;
  optionsGroup: IDestinationSearchGroup[];
  handleOptionSelect: Function;
}) => {
  const { t } = useTranslation('common', { keyPrefix: 'search' });

  return (
    <div>
      {!loading && !optionsGroup.length && value?.length! < 3 && (
        <div className='flex flex-col items-center gap-2'>
          <PencilSimple size={22} />
          <p className='text-center text-gray-500'>{t('destination.min')}</p>
        </div>
      )}
      {loading && (
        <div className='flex items-center gap-2'>
          <CircleNotch className='animate-spin' />
          <p>{t('destination.loading')}</p>
        </div>
      )}
      {!loading &&
        optionsGroup.length > 0 &&
        optionsGroup.map(group =>
          group.itens.map(item => (
            <div
              key={group.display + item.id}
              onClick={() => handleOptionSelect(item)}
              className='flex items-center p-3 gap-2 radius-default hover:bg-gray-100 cursor-pointer'
            >
              <div className='flex-none text-primary-900'>
                {destinationGroupIcons[item.group]}
              </div>
              <p className='text-sm'>{item.display}</p>
            </div>
          ))
        )}
      {!loading && !optionsGroup.length && value?.length! >= 3 && (
        <div className='flex flex-col items-center justify-center gap-2 p-3'>
          <SmileyXEyes size={22} />
          <p className='text-center'>{t('destination.notfound')}</p>
        </div>
      )}
    </div>
  );
};

const DestinationSearch: FC<DestinationProps> = ({
  label,
  placeholder,
  value,
  onChange
}: DestinationProps) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const windowWidth = useWindowWith();
  const [optionsGroup, setOptionsGroup] = useState<IDestinationSearchGroup[]>(
    []
  );
  const [loading, setLoading] = useState(false);
  const [fetching, setFetching] = useState(false);
  const [overlayOpen, setOverlayOpen] = useState(false);
  const [displayText, setDisplayText] = useState<string | null>('');
  const [selectedOption, setSelectedOption] = useState<IDestination | null>(
    null
  );

  const overlayRef = useRef<HTMLDivElement>(null);
  useOnClickOutside(overlayRef, () => setOverlayOpen(false));

  useEffect(() => {
    if (value) {
      setDisplayText(value.display);
      setSelectedOption(value);
    }
  }, [value]);

  const fetchDestination = async (value: string) => {
    if (fetching) return;

    try {
      setLoading(true);
      setFetching(true);

      const {
        data: {
          suggestion: { groups }
        }
      } = await getDestinationAutoComplete(value as string);
      setOptionsGroup(groups);
    } catch (err) {
      //
    } finally {
      setLoading(false);
      setFetching(false);
    }
  };

  const handleSearch = (value: string) => {
    setDisplayText(value);
    setOverlayOpen(!!value);
  };

  useEffect(() => {
    const typing = displayText !== null && displayText.length > 2;
    if (!typing) return;

    const timer = setTimeout(() => {
      if (typing && !fetching) {
        fetchDestination(displayText!);
      }
    }, 1000);

    return () => clearTimeout(timer);
  }, [displayText]);

  const handleOptionSelect = (option: IDestination) => {
    setOverlayOpen(false);
    setSelectedOption(option);
    setDisplayText(option.display);

    onChange?.(option);
  };

  return (
    <>
      <div className='relative w-full hidden md:flex z-20'>
        <DestinationInput
          label={label}
          value={displayText}
          placeholder={placeholder}
          onChange={(e: any) => handleSearch(e.target.value)}
          selectedOption={selectedOption}
          setPopoverOpen={setOverlayOpen}
        />
        {overlayOpen && (
          <div
            ref={overlayRef}
            className='absolute flex flex-col w-full top-[100%] mt-6 rounded-default bg-white p-4 max-h-[40vh] overflow-auto shadow-lg'
          >
            <DestinationOverlayContent
              value={displayText ?? ''}
              loading={loading}
              optionsGroup={optionsGroup}
              handleOptionSelect={handleOptionSelect}
            />
          </div>
        )}
      </div>
      <div className='w-full flex md:hidden'>
        <Sheet
          open={windowWidth < 768 ? overlayOpen : false}
          onOpenChange={setOverlayOpen}
        >
          <SheetTrigger className='w-full'>
            <DestinationInput
              label={label}
              value={displayText}
              placeholder={placeholder}
              onChange={(e: any) => handleSearch(e.target.value)}
              selectedOption={selectedOption}
              setPopoverOpen={setOverlayOpen}
            />
          </SheetTrigger>
          <SheetContent
            ref={overlayRef}
            side='bottom'
            className='h-[50vh] rounded-t-default'
          >
            <SheetHeader className='text-start'>
              <div className='w-full flex flex-col gap-2 md:pl-1 md:flex-row mb-4 md:mb-0'>
                <input
                  id='destination'
                  placeholder={placeholder}
                  ref={inputRef}
                  onChange={(e: any) => handleSearch(e.target.value)}
                  className='w-full bg-gray-100 rounded-default p-4 mt-2'
                />
                <DestinationOverlayContent
                  value={displayText ?? ''}
                  loading={loading}
                  optionsGroup={optionsGroup}
                  handleOptionSelect={handleOptionSelect}
                />
              </div>
            </SheetHeader>
          </SheetContent>
        </Sheet>
      </div>
    </>
  );
};

export default DestinationSearch;
