/* eslint-disable react/no-array-index-key */
import Divider from '@components/atoms/Divider';
import Skeleton from 'react-loading-skeleton';

const HotelAvailableRoomSkeleton = () => {
  return (
    <div className='w-full flex flex-col md:flex-row gap-6 md:gap-6 p-6 md:p-6 rounded-default bg-white leading-none'>
      <div className='flex flex-col justify-start items-start gap-3 md:gap-6'>
        <Skeleton width={190} height={190} borderRadius={8} />
      </div>
      <div className='w-full flex p-1 flex-col gap-4'>
        <div className='flex flex-col gap-2'>
          <Skeleton width={150} height={20} />
          <div className='flex gap-1'>
            <Skeleton width={100} height={25} />
            <Skeleton width={200} height={25} />
          </div>
        </div>
        <div className='flex flex-col gap-1'>
          <div className='flex flex-col gap-1 p-4 pt-0'>
            <Skeleton width='100%' height={20} />
            <Skeleton width='100%' height={20} />
          </div>
          <div className='flex flex-col gap-4'>
            <div className='flex flex-col gap-1'>
              <div className='flex flex-col items-start p-1 gap-6 md:gap-6 bg-gray-100 rounded-sm'>
                <div className='flex flex-col md:flex-row md:justify-between md:items-center w-full gap-6'>
                  <div className='flex flex-col gap-1'>
                    <Skeleton width={100} height={44} />
                  </div>
                  <div className='flex flex-col md:flex-row items-center justify-between gap-4'>
                    <div className='flex flex-col gap-1 justify-center items-start md:items-end'>
                      <Skeleton width={75} height={20} />
                      <Skeleton width={75} height={20} />
                    </div>
                    <div className='hidden md:block'>
                      <Divider className='divider' />
                    </div>
                    <div className='flex flex-col gap-1 justify-center items-start md:items-end'>
                      <Skeleton width={75} height={20} />
                      <Skeleton width={75} height={20} />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HotelAvailableRoomSkeleton;
