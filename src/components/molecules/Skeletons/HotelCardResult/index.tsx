import { PropsWithChildren } from 'react';
import Skeleton from 'react-loading-skeleton';

const ImageWrapper = ({ children }: PropsWithChildren) => {
  return (
    <div className='w-[100px] h-[175px] md:w-[250px] md:h-[250px]'>
      {children}
    </div>
  );
};

const HotelCardResultSkeleton = () => {
  return (
    <div className='flex flex-col gap-6 mb-6 leading-none'>
      <div className='flex flex-row gap-2 w-full'>
        <Skeleton width='100%' height='100%' wrapper={ImageWrapper} />
        <div className='w-full flex gap-6'>
          <div className='w-full flex flex-col justify-between'>
            <div className='w-full flex flex-col gap-2'>
              <Skeleton width={75} height={15} />
              <Skeleton width='100%' height={30} />
              <div className='flex flex-col gap-1'>
                <Skeleton width={200} height={15} />
                <Skeleton width={200} height={15} />
              </div>
            </div>
            <div className='flex flex-col gap-1'>
              <Skeleton width={150} height={15} />
              <Skeleton width='100%' height={30} />
            </div>
          </div>
          <div className='w-full hidden md:flex flex-col justify-between text-right'>
            <Skeleton width={150} height={40} />
            <div className='flex flex-col text-right gap-1'>
              <Skeleton width='35%' height={15} />
              <Skeleton width='50%' height={20} />
              <Skeleton width='35%' height={15} />
            </div>
            <Skeleton width='45%' height={40} />
          </div>
        </div>
      </div>
      <Skeleton width='100%' height={40} />
    </div>
  );
};

export default HotelCardResultSkeleton;
