import Skeleton from 'react-loading-skeleton';

const OrderCardSkeleton = () => {
  return (
    <div className='flex flex-col bg-white rounded-default p-6 gap-6 mb-6'>
      <div className='flex justify-between'>
        <div className='flex flex-col gap-2'>
          <div className='flex items-end gap-1'>
            <Skeleton width='70px' height='25px' />
            <Skeleton containerClassName='mb-[-4px]' width={80} />
          </div>
          <Skeleton width={90} />
        </div>
        <div className='flex flex-col items-end gap-2'>
          <Skeleton width={40} height={15} />
          <Skeleton width='90px' height='30px' />
        </div>
      </div>
      <div className='w-full flex gap-4'>
        <Skeleton
          containerClassName='w-full'
          width='100%'
          height={30}
          count={2}
        />
      </div>
    </div>
  );
};

export default OrderCardSkeleton;
