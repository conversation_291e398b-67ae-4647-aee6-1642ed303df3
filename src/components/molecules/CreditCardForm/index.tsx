/* eslint-disable no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
import { Checkbox, Input } from '@components/atoms/Input';
import { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { useHookFormMask } from 'use-mask-input';
import Card from 'react-credit-cards-2';
import {
  ICreditCardFormFields,
  ICheckoutRequestCreditCardPayer
} from 'src/types/checkout';
import { useForm } from 'react-hook-form';
import {
  addressDefaultValues,
  creditCardPayerDefaulValues
} from '@consts/checkout';
import {
  isValidCNPJ,
  isValidCpf,
  isValidEmail,
  isValidName
} from 'src/utils/validation';
import Button from '@components/atoms/Button';
import { getAddressByCep } from 'src/server/cep';
import { mountAdressValues } from 'src/utils/checkout';
import { toast } from 'react-toastify';
import { i18n, useTranslation } from 'next-i18next';
import { ArrowRight, House, User } from '@phosphor-icons/react';
import PhoneInput from '@components/atoms/Input/Phone';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@components/atoms/Input/NewSelect';

type CreditCardFields = 'name' | 'number' | 'expiry' | 'cvc' | '';
type CreditCardFormType = { creditCard: ICreditCardFormFields };

interface ICreditCardForm {
  handleCreditCardSubmit: (data: CreditCardFormType) => void;
  toPreviousStep: Function;
  initialValues?: CreditCardFormType;
  userValues?: ICheckoutRequestCreditCardPayer;
  tripcashApplied: boolean;
  isAuthenticated: boolean;
  installments: any[];
  setCreditCardNumber: (value: string) => void;
  setSelectedInstallment: (value: number) => void;
  selectedInstallment: number;
  setInstallmentsValues: (value?: number) => void;
  selectedCreditCardApi?: string;
}

const CreditCardForm: FC<ICreditCardForm> = ({
  initialValues,
  userValues,
  handleCreditCardSubmit,
  isAuthenticated,
  installments,
  setCreditCardNumber,
  setSelectedInstallment,
  selectedInstallment,
  setInstallmentsValues
}) => {
  const { t } = useTranslation('checkout');
  const {
    register,
    watch,
    handleSubmit,
    setValue,
    getValues,
    reset,
    setFocus: setFieldFocus,
    setError,
    clearErrors,
    formState: { errors }
  } = useForm<CreditCardFormType>({
    defaultValues: { ...initialValues },
    mode: 'onBlur',
    reValidateMode: 'onBlur'
  });
  const registerWithMask = useHookFormMask(register);

  const DDI = watch('creditCard.payer.phone.ddi');
  const { number, cvv, name, expireDate } = watch('creditCard');
  const documentType = watch('creditCard.payer.identification.documentType');
  const userAsPayer = watch('creditCard.payer.userAsPayer');
  const installment = watch('creditCard.installments');

  const [focus, setFocus] = useState<CreditCardFields>('');
  const [addressValues, setAddressValues] = useState({
    ...initialValues?.creditCard?.payer?.address
  });

  const onSumit = async (data: CreditCardFormType) => {
    handleCreditCardSubmit(data);
  };

  const disableEmailField = useMemo(() => {
    return (
      (isAuthenticated && userAsPayer) ||
      (!isAuthenticated && initialValues?.creditCard.payer.email !== '')
    );
  }, [initialValues?.creditCard.payer.email, isAuthenticated, userAsPayer]);

  useEffect(() => {
    setSelectedInstallment(installment);
  }, [installment]);

  const fetchAddress = useCallback(
    async (val?: string) => {
      if (!val) {
        setValue('creditCard.payer.address', { ...addressDefaultValues });
        return;
      }

      if (addressValues.postalCode === val) {
        return;
      }

      const { data } = await getAddressByCep(val);

      if (data.erro) {
        toast(t('errors.cep'), {
          type: 'error'
        });
      } else {
        const address = mountAdressValues(data);
        setAddressValues(address);

        const payer = 'creditCard.payer';

        setValue('creditCard.payer.address', address, {
          shouldValidate: true
        });

        setFieldFocus(`${payer}.address.number`);
      }
    },
    [addressValues.postalCode, setFieldFocus, setValue, t]
  );

  useEffect(() => {
    fetchAddress(getValues('creditCard.payer.address.postalCode'));
  }, [setValue, setFieldFocus, addressValues, t, fetchAddress, getValues]);

  useEffect(() => {
    setCreditCardNumber(number);
  }, [number]);

  useEffect(() => {
    const email = initialValues?.creditCard.payer.email;
    if (email) setValue('creditCard.payer.email', email);
  }, [initialValues?.creditCard.payer.email, setValue]);

  const handleUserAsPayer = useCallback(
    (checked: boolean) => {
      reset({
        creditCard: {
          ...getValues('creditCard'),
          payer: checked
            ? { ...userValues }
            : { ...creditCardPayerDefaulValues }
        }
      });
      setAddressValues(
        checked ? { ...userValues?.address } : { ...addressDefaultValues }
      );
    },
    [getValues, reset, userValues]
  );

  const handleDocumentTypeChange = (value: string) => {
    setValue('creditCard.payer.identification.documentType', value);
    clearErrors('creditCard.payer.identification.documentType');
  };

  const cardError = errors?.creditCard;
  const payerError = errors?.creditCard?.payer;
  const isCPF = documentType === 'CPF';
  return (
    <form onSubmit={handleSubmit((data: CreditCardFormType) => onSumit(data))}>
      <div className='flex flex-col gap-2 mb-2'>
        <h3 className='font-semibold text-primary-900'>
          {t('payment.method.creditCard')}
        </h3>
      </div>
      <div className='flex gap-6 mb-4 flex-col md:flex-row'>
        <div className='flex flex-col gap-4 flex-1'>
          <Input
            border
            {...registerWithMask(
              'creditCard.number',
              ['9999 999999 99999', '9999 9999 9999 9999'],
              {
                required: t('validation.required'),
                onincomplete() {
                  setError('creditCard.number', {
                    type: 'custom',
                    message: t('validation.required')
                  });
                },
                oncomplete() {
                  clearErrors('creditCard.number');
                }
              }
            )}
            onFocus={() => setFocus('number')}
            placeholder={`${t('payment.card.number')} *`}
            error={cardError?.number?.message}
            autoComplete='cc-number'
          />
          <Input
            placeholder={`${t('payment.card.name')} *`}
            {...register('creditCard.name', {
              required: t('validation.required')
            })}
            onFocus={() => setFocus('name')}
            border
            autoComplete='cc-name'
            error={cardError?.name?.message}
          />
          <div className='flex items-center gap-4'>
            <Input
              placeholder={`${t('payment.card.cvv')} *`}
              {...registerWithMask('creditCard.cvv', ['999', '9999'], {
                required: t('validation.required'),
                pattern: {
                  value: /^\d{3,4}$/,
                  message: t('validation.invalid.cvv')
                }
              })}
              onFocus={() => setFocus('cvc')}
              border
              error={cardError?.cvv?.message}
            />
            <Input
              placeholder={`${t('payment.card.expiry')} *`}
              {...registerWithMask('creditCard.expireDate', '99/9999', {
                required: t('validation.required'),
                pattern: {
                  value: /^\d{2}\/\d{4}$/,
                  message: t('validation.invalid.date')
                }
              })}
              autoComplete='cc-exp'
              onFocus={() => setFocus('expiry')}
              border
              error={cardError?.expireDate?.message}
            />
          </div>
        </div>
        <Card
          locale={{ valid: t('payment.card.expiry') }}
          placeholders={{ name: t('payment.card.name').toUpperCase() }}
          number={number}
          cvc={cvv}
          expiry={expireDate}
          name={name}
          focused={focus}
        />
      </div>
      <Select
        onValueChange={(value: string) => setInstallmentsValues(Number(value))}
        {...register('creditCard.installments', {
          required: t('validation.required'),
          valueAsNumber: true
        })}
        value={selectedInstallment.toString()}
      >
        <SelectTrigger className='bg-white'>
          <SelectValue placeholder={`${t('payment.card.installments')} *`} />
        </SelectTrigger>
        <SelectContent position='item-aligned'>
          {installments.length > 0 ? (
            installments.map((option: any) => (
              <SelectItem key={option.value} value={option.value.toString()}>
                {option.label}
              </SelectItem>
            ))
          ) : (
            <SelectItem value='1' disabled>
              {t('payment.card.installments')}
            </SelectItem>
          )}
        </SelectContent>
      </Select>
      <div className='mb-6'>
        <div className='flex flex-col mt-6 gap-1'>
          <h3 className='font-semibold text-primary-900'>{t('payer.title')}</h3>
          <div className='mb-2'>
            {isAuthenticated ? (
              <Checkbox
                label={t('payer.useMyInfo')}
                value={userAsPayer}
                onChange={data => handleUserAsPayer(data)}
              />
            ) : null}
          </div>
        </div>
        <div className='flex flex-col gap-2'>
          <div className='flex mt-4'>
            <h4 className='font-semibold text-primary-900 flex items-center gap-2'>
              <User weight='bold' />
              {t('payer.personalInfo.title')}
            </h4>
          </div>
          <div className='flex flex-col gap-4'>
            <Input
              placeholder={`${t('payer.personalInfo.email')} *`}
              {...register('creditCard.payer.email', {
                required: t('validation.required'),
                validate: val =>
                  isValidEmail(val) || t('validation.invalid.email')
              })}
              error={payerError?.email?.message}
            />
            <div className='flex items-center gap-4'>
              <Input
                placeholder={`${t('payer.personalInfo.firstName')} *`}
                {...register('creditCard.payer.firstName', {
                  required: t('validation.required'),
                  validate: val =>
                    isValidName(val) || t('validation.invalid.name')
                })}
                error={payerError?.firstName?.message}
              />
              <Input
                placeholder={`${t('payer.personalInfo.lastName')} *`}
                {...register('creditCard.payer.lastName', {
                  required: t('validation.required'),
                  validate: val =>
                    isValidName(val) || t('validation.invalid.surname')
                })}
                error={payerError?.lastName?.message}
              />
            </div>
            <div className='flex items-center gap-4 flex-wrap'>
              <div className='flex-1'>
                <Select
                  onValueChange={handleDocumentTypeChange}
                  {...register('creditCard.payer.identification.documentType', {
                    required: t('validation.required')
                  })}
                  value={documentType}
                >
                  <SelectTrigger className='w-auto bg-white'>
                    <SelectValue
                      placeholder={t('payer.personalInfo.document.type')}
                    />
                  </SelectTrigger>
                  <SelectContent position='item-aligned'>
                    <SelectItem value='CPF'>CPF</SelectItem>
                    <SelectItem value='CNPJ'>CNPJ</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className='flex-2'>
                <Input
                  placeholder={`${t('payer.personalInfo.document.number')} *`}
                  {...registerWithMask(
                    'creditCard.payer.identification.documentNumber',
                    isCPF ? 'cpf' : '99.999.999/9999-99',
                    {
                      required: t('validation.required'),
                      validate: val =>
                        isCPF
                          ? isValidCpf(val) || t('validation.invalid.cpf')
                          : isValidCNPJ(val) || t('validation.invalid.cnpj')
                    }
                  )}
                  error={payerError?.identification?.documentNumber?.message}
                />
              </div>
            </div>
            <PhoneInput
              DDI={DDI}
              phoneDDIKey='creditCard.payer.phone.ddi'
              phoneAreaCodeKey='creditCard.payer.phone.areaCode'
              phoneNumberKey='creditCard.payer.phone.number'
              registerWithMask={registerWithMask}
              setFocus={setFieldFocus}
              errors={payerError as any}
              ddiClassName='bg-white'
              areaCodeClassName='bg-white'
              phoneClassName='bg-white'
            />
          </div>
        </div>
        <div className='flex flex-col gap-2 mt-3'>
          <div className='flex mt-4'>
            <h4 className='font-medium text-primary-900 flex items-center gap-2'>
              <House weight='bold' />
              {t('payer.address.title')}
            </h4>
          </div>
          <div className='flex flex-col gap-4'>
            <div className='flex items-center gap-4 flex-wrap'>
              <div className='flex-3 md:flex-1'>
                <Input
                  placeholder={`${t('payer.address.postalCode')} *`}
                  {...registerWithMask(
                    'creditCard.payer.address.postalCode',
                    '',
                    {
                      required: t('validation.required'),
                      oncomplete() {
                        if (i18n!.language.toUpperCase() === 'PT_BR') {
                          fetchAddress(
                            getValues('creditCard.payer.address.postalCode')
                          );
                        }
                      },
                      mask:
                        i18n!.language.toUpperCase() === 'PT_BR'
                          ? '99999-999'
                          : '9{0,12}'
                    }
                  )}
                  error={payerError?.address?.postalCode?.message}
                />
              </div>
              <div className='flex-4' />
            </div>
            <div className='flex items-center gap-4 flex-wrap'>
              <div className='flex-1'>
                <Input
                  placeholder={`${t('payer.address.state')} *`}
                  {...register('creditCard.payer.address.uf', {
                    required: t('validation.required')
                  })}
                  error={payerError?.address?.uf?.message}
                />
              </div>
              <div className='flex-2'>
                <Input
                  placeholder={`${t('payer.address.city')} *`}
                  {...register('creditCard.payer.address.city', {
                    required: t('validation.required')
                  })}
                  error={payerError?.address?.city?.message}
                />
              </div>
              <div className='flex-2'>
                <Input
                  placeholder={`${t('payer.address.country')} *`}
                  {...register('creditCard.payer.address.country', {
                    required: t('validation.required')
                  })}
                  border
                  error={payerError?.address?.country?.message}
                />
              </div>
            </div>
            <div className='flex items-center gap-4 flex-wrap'>
              <div className='flex-3'>
                <Input
                  placeholder={`${t('payer.address.street')} *`}
                  {...register('creditCard.payer.address.street', {
                    required: t('validation.required')
                  })}
                  error={payerError?.address?.street?.message}
                />
              </div>
              <div className='flex-1'>
                <Input
                  placeholder={`${t('payer.address.number')} *`}
                  type='number'
                  error={payerError?.address?.number?.message}
                  {...register('creditCard.payer.address.number', {
                    required: t('validation.required')
                  })}
                />
              </div>
            </div>
            <Input
              placeholder={`${t('payer.address.neighborhood')} *`}
              {...register('creditCard.payer.address.neighborhood', {
                required: t('validation.required')
              })}
              error={payerError?.address?.neighborhood?.message}
            />
            <Input
              placeholder={t('payer.address.complement')}
              {...register('creditCard.payer.address.complement')}
              error={payerError?.address?.complement?.message}
            />
          </div>
        </div>
      </div>
      <div className='flex justify-between'>
        <div />
        <Button size='large' type='submit' className='w-full md:w-auto'>
          {t('buttons.next')}
          <ArrowRight />
        </Button>
      </div>
    </form>
  );
};

export default CreditCardForm;
