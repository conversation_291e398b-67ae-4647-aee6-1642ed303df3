import Button from '@components/atoms/Button';
import MenuButton from '@components/atoms/MenuButton';
import TripcashCardBalance from '@components/molecules/TripcashCardBalance';
import {
  Gear,
  HandCoins,
  House,
  SignOut,
  UserCircle,
  UserList
} from '@phosphor-icons/react';
import { useContext } from 'react';
import { AuthContext } from 'src/context/AuthContext';
import { useRouter } from 'next/router';
import { Sheet, SheetContent } from '@components/atoms/Sheet';
import { useTranslation } from 'next-i18next';

interface ISidebar {
  open: boolean;
  onClose: () => void;
}

const Sidebar = ({ open, onClose }: ISidebar) => {
  const { t } = useTranslation('common');
  const { user, isAuthenticated, logout } = useContext(AuthContext);
  const router = useRouter();

  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetContent className='flex flex-col justify-between z-60'>
        <div className='flex flex-col gap-4'>
          {isAuthenticated && (
            <div className='flex items-center gap-4'>
              <div className='w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center'>
                <UserCircle size={24} className='text-primary-900' />
              </div>
              <div>
                <h3 className='text-primary-900 font-medium'>{`${user.name} ${user.surname}`}</h3>
                <p className='text-sm text-gray-500'>{user!.email}</p>
              </div>
            </div>
          )}
          {isAuthenticated && (
            <TripcashCardBalance
              label={t('navbar.tripcash.balance')}
              values={user!.tripcashBalances}
            />
          )}
          <MenuButton
            icon={<House />}
            text={t('navbar.home')}
            onClick={() => router.push('/')}
          />
          <MenuButton
            icon={<HandCoins />}
            text={t('navbar.tripcash.title')}
            onClick={() => router.push(t('routes.tripcash'))}
          />
          {isAuthenticated && (
            <>
              <MenuButton
                icon={<UserCircle />}
                text={t('navbar.myProfile')}
                onClick={() => router.push(t('routes.myProfile'))}
              />
              <MenuButton
                icon={<UserList />}
                text={t('navbar.myReservations')}
                onClick={() => router.push(t('routes.myReservations'))}
              />
              <MenuButton
                icon={<Gear />}
                text={t('navbar.myTripcash')}
                onClick={() => router.push(t('routes.myTripcash'))}
              />
            </>
          )}
        </div>
        <div className='flex flex-col gap-4'>
          {isAuthenticated ? (
            <MenuButton
              icon={<SignOut />}
              text={t('navbar.logout')}
              onClick={() => logout?.()}
            />
          ) : (
            <>
              <Button
                color='primary'
                variant='outline'
                onClick={() => router.push(t('routes.signIn'))}
              >
                {t('navbar.signIn')}
              </Button>
              <Button
                color='primary'
                onClick={() => router.push(t('routes.signUp'))}
              >
                {t('navbar.signUp')}
              </Button>
            </>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default Sidebar;
