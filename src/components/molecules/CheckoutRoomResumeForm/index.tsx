/* eslint-disable no-unused-vars */
/* eslint-disable react/no-array-index-key */
import Button from '@components/atoms/Button';
import { Baby, User } from '@phosphor-icons/react';
import { TFunction } from 'i18next';
import { CheckoutStepEnum, GuestRoomType } from 'src/types/checkout';

interface ICheckoutRoomResumeFormProps {
  t: TFunction;
  room: GuestRoomType;
  roomName: string;
  handleEditStep: (step: number) => void;
  loading: boolean;
}

const CheckoutRoomResumeForm = ({
  t,
  room,
  roomName,
  handleEditStep,
  loading
}: ICheckoutRoomResumeFormProps) => {
  return (
    <div
      key={room.document}
      className='flex flex-col md:flex-row justify-between md:items-center p-6 bg-white rounded-default gap-3'
    >
      <div className='flex flex-col'>
        <h3 className='font-medium text-primary-900'>{roomName}</h3>
        <div className='flex items-center gap-3 mt-1 flex-wrap'>
          {room.adult.map((adult, index) => {
            return (
              <div
                key={`${adult}-${index}`}
                className='flex items-center gap-1 bg-primary-100 py-1 px-2 rounded-button'
              >
                <User weight='bold' className='text-gray-500' />
                <p className='text-gray-500 text-sm'>{adult.name}</p>
              </div>
            );
          })}
          {room?.kid?.map((kid, index) => {
            return (
              <div
                key={`${kid}-${index}`}
                className='flex items-center gap-1 bg-primary-100 py-1 px-2 rounded-button'
              >
                <Baby weight='bold' className='text-gray-500' />
                <p className='text-gray-500 text-sm'>{kid.name}</p>
              </div>
            );
          })}
        </div>
      </div>
      <Button
        onClick={() => handleEditStep(CheckoutStepEnum.GUESTS)}
        disabled={loading}
        color='primary'
        variant='outline'
        size='small'
      >
        {t('room.change')}
      </Button>
    </div>
  );
};

export default CheckoutRoomResumeForm;
