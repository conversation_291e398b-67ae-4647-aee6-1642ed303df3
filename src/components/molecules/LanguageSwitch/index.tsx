import { i18n, useTranslation } from 'next-i18next';
import { ReactElement, useState } from 'react';
import { useRouter } from 'next/router';
import { CaretDown } from '@phosphor-icons/react';
import { cn } from '@utils/classes';
import Dropdown, { DropdownItemType } from '../Dropdown';

type Language = {
  [key: string]: string;
};

type LanguageItem = {
  id: string;
  text: string;
  icon: ReactElement;
  onClick?: () => void;
};

const LanguageSwitchTrigger = ({
  languages,
  color
}: {
  languages: Language;
  color?: 'primary' | 'white';
}) => {
  const textClasses = cn(
    'font-normal',
    color === 'primary' ? 'text-primary-900' : 'text-white'
  );

  return (
    <div className='flex items-center gap-2 cursor-pointer'>
      <img
        className='w-[22px]'
        src={`/images/flags/${i18n!.language}.png`}
        alt='Flag'
      />
      <p className={textClasses}>{languages[i18n!.language]}</p>
      <CaretDown className={textClasses} />
    </div>
  );
};

const LanguageSwitch = ({ color }: { color?: 'primary' | 'white' }) => {
  const router = useRouter();
  const { t } = useTranslation('common');
  // const [host, setHost] = useState<string | null>(null);
  const [languageOpen, setLanguageOpen] = useState<boolean>(false);

  const languages: Language = {
    pt_PT: t('navbar.language.portuguese'),
    pt_BR: t('navbar.language.portuguese_brazil'),
    en_US: t('navbar.language.english'),
    es_ES: t('navbar.language.spanish')
  };

  const changeLanguage = (lng: string) => {
    const { pathname, query } = router;
    // const finalLng = !host?.includes('.br') && lng === 'pt_BR' ? 'pt_PT' : lng;

    router.push({ pathname, query }, '', {
      locale: lng
    });
  };

  const languageItems: LanguageItem[] = [
    {
      id: 'pt_PT',
      text: t('navbar.language.portuguese'),
      icon: (
        <img
          src='/images/flags/pt_PT.png'
          alt='Portuguese flag'
          className='w-[22px]'
        />
      ),
      onClick: () => changeLanguage('pt_PT')
    },
    {
      id: 'pt_BR',
      text: t('navbar.language.portuguese_brazil'),
      icon: (
        <img
          src='/images/flags/pt_BR.png'
          alt='Brazilian Portuguese flag'
          className='w-[22px]'
        />
      ),
      onClick: () => changeLanguage('pt_BR')
    },
    {
      id: 'en_US',
      text: t('navbar.language.english'),
      icon: (
        <img
          src='/images/flags/en_US.png'
          alt='English flag'
          className='w-[22px]'
        />
      ),
      onClick: () => changeLanguage('en_US')
    },
    {
      id: 'es_ES',
      text: t('navbar.language.spanish'),
      icon: (
        <img
          src='/images/flags/es_ES.png'
          alt='Spanish flag'
          className='w-[22px]'
        />
      ),
      onClick: () => changeLanguage('es_ES')
    }
  ];

  const items: DropdownItemType[] = languageItems.map(item => ({
    id: item.id,
    text: item.text,
    icon: item.icon,
    checked: i18n!.language === item.id,
    onClick: () => item.onClick?.()
  }));

  return (
    <Dropdown open={languageOpen} onToggle={setLanguageOpen} items={items}>
      <LanguageSwitchTrigger languages={languages} color={color} />
    </Dropdown>
  );
};

export default LanguageSwitch;
