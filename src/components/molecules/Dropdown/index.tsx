/* eslint-disable no-unused-vars */
/* eslint-disable react/no-array-index-key */
import { FC, ReactNode } from 'react';
import MenuButton from '@components/atoms/MenuButton';
import { Sheet, SheetContent, SheetTrigger } from '@components/atoms/Sheet';
import useWindowWith from 'src/hooks/useWindowSize';
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@components/atoms/Popover';
import Divider from '@components/atoms/Divider';

export type DropdownItemType = {
  icon?: ReactNode;
  type?: 'divider' | 'button';
  text?: string;
  onClick?: Function;
  checked?: boolean;
};

interface IDropdown {
  open: boolean;
  items: DropdownItemType[];
  children: ReactNode;
  onToggle?: (open: boolean) => void;
  header?: ReactNode;
  className?: string;
}

interface IDropdownContent {
  items: DropdownItemType[];
  header?: ReactNode;
  onClose: () => void;
}

const DropdownContent: FC<IDropdownContent> = ({
  items,
  header,
  onClose
}: IDropdownContent) => {
  return (
    <>
      {header}
      {items?.map((item, index) => {
        if (item.type === 'divider')
          return (
            <Divider key={index} orientation='horizontal' className='my-4' />
          );

        return (
          <MenuButton
            key={item.text}
            icon={item.icon}
            text={item.text || ''}
            checked={item.checked}
            onClick={() => {
              item.onClick?.();
              onClose?.();
            }}
          />
        );
      })}
    </>
  );
};

const Dropdown = ({
  open,
  items,
  children,
  onToggle,
  header,
  className
}: IDropdown) => {
  const windowWidth = useWindowWith();

  return windowWidth < 768 ? (
    <Sheet open={open} onOpenChange={onToggle}>
      <SheetTrigger className={className}>{children}</SheetTrigger>
      <SheetContent side='bottom' className='rounded-t-default bg-white'>
        <DropdownContent
          items={items}
          header={header}
          onClose={() => onToggle?.(false)}
        />
      </SheetContent>
    </Sheet>
  ) : (
    <Popover open={open} onOpenChange={onToggle}>
      <PopoverTrigger>{children}</PopoverTrigger>
      <PopoverContent align='end' className='min-w-[340px]'>
        <DropdownContent
          items={items}
          header={header}
          onClose={() => onToggle?.(false)}
        />
      </PopoverContent>
    </Popover>
  );
};

export default Dropdown;
