/* eslint-disable @next/next/no-img-element */
import Button from '@components/atoms/Button';
import {
  ArrowBendUpRight,
  Check,
  ForkKnife,
  HandCoins,
  MagnifyingGlass,
  MapPinLine,
  MapTrifold,
  Prohibit,
  Tag,
  Warning
} from '@phosphor-icons/react';
import { ISearchResponseResult } from 'src/types/search';
import { useRouter } from 'next/router';
import Stars from '@components/atoms/Stars';
import Badge from '@components/atoms/Badge';
import { useTranslation } from 'next-i18next';
import useWindowWith from 'src/hooks/useWindowSize';
import { useReferral } from 'src/context/ReferralContext';
import ImageCarousel from '../ImageCarousel';

const HotelCardResult = ({
  accomodation,
  price,
  pricePromotional,
  distance,
  refundable,
  unavailable,
  choose,
  mealPlan
}: ISearchResponseResult) => {
  const { params } = useReferral();
  const { t, i18n } = useTranslation(['search', 'common'], {
    nsMode: 'fallback'
  });
  const router = useRouter();
  const windowWidth = useWindowWith();

  const hotelUrl = `${t('routes.hotel')}/${
    accomodation?.id
  }?${new URLSearchParams({ ...(router.query as any), ...params }).toString()}`;

  const handleRedirect = () => {
    if (windowWidth < 768) {
      router.push(hotelUrl, '', { locale: i18n.language });
      return;
    }

    window.open(`/${i18n.language}${hotelUrl}`, '_blank');
  };

  return (
    <div
      className='flex flex-col cursor-pointer'
      onClick={() => handleRedirect()}
    >
      {choose && (
        <div className='flex bg-primary-500 py-3 px-2 rounded-t-default items-center gap-2 text-white'>
          <MagnifyingGlass size={16} weight='bold' />
          {t('hotelCard.searchingAccommodation')}
        </div>
      )}
      <div className='flex flex-col bg-white p-3 rounded-default gap-4'>
        <div className='flex flex-wrap gap-2 md:hidden'>
          {refundable ? (
            <Badge
              icon={<Check weight='bold' size={16} />}
              type='success'
              size='small'
            >
              {t('hotelCard.refundable')}
            </Badge>
          ) : (
            <Badge
              icon={<Warning weight='bold' size={16} />}
              type='warning'
              size='small'
            >
              {t('hotelCard.nonRefundable')}
            </Badge>
          )}
          {mealPlan && (
            <Badge
              icon={
                mealPlan.code === 'RO' ? (
                  <Prohibit weight='bold' size={16} />
                ) : (
                  <ForkKnife weight='bold' size={16} />
                )
              }
              type={mealPlan.code === 'RO' ? 'danger' : 'secondary'}
              size='small'
            >
              {mealPlan.description}
            </Badge>
          )}
          {price?.tripcashInfo ? (
            <Badge
              type='info'
              icon={<HandCoins weight='duotone' size={16} />}
              size='small'
            >
              {t('hotelCard.tripcash', {
                value: price.tripcashInfo?.formatedValue
              })}
            </Badge>
          ) : null}
        </div>
        <div className='flex gap-4'>
          <ImageCarousel
            cover={accomodation.photosCover?.url}
            images={accomodation.photos?.map(photo => photo?.url!) || []}
            className='w-[100px] h-full md:w-[250px] md:min-h-[250px] rounded-button overflow-hidden'
          />
          <div className='w-full flex flex-col md:flex-row justify-between gap-4 md:p-3'>
            <div className='flex flex-col justify-between gap-4 w-full'>
              <div className='w-full'>
                <div className='w-full flex gap-2 items-center'>
                  <div className='w-full flex items-start gap-2 flex-row-reverse justify-between md:flex-col'>
                    {accomodation?.stars ? (
                      <Stars rate={accomodation?.stars} />
                    ) : null}
                    <h2 className='text-md md:text-xl text-primary-900 font-medium line-clamp-2'>
                      {accomodation?.name}
                    </h2>
                  </div>
                  {accomodation.type && (
                    <Badge type='info' size='small'>
                      {accomodation.type}
                    </Badge>
                  )}
                </div>
                <div className='flex flex-col gap-0 md:gap-1 mt-1'>
                  <div className='flex gap-1 md:gap-2 items-center'>
                    <MapPinLine className='text-primary-900 flex-none' />
                    <p className='text-xs md:text-sm text-gray-500 line-clamp-1'>
                      {accomodation?.address?.fullAddress}
                    </p>
                  </div>
                  {!choose && (
                    <div className='gap-2 items-center hidden md:flex'>
                      <MapTrifold className='text-primary-900 flex-none' />
                      <p className='text-xs md:text-sm text-gray-500 line-clamp-1'>
                        {distance < 1
                          ? t('hotelCard.distanceInMeters', {
                              distance: (distance * 1000).toFixed(0)
                            })
                          : t('hotelCard.distanceInKilometers', {
                              distance: distance?.toFixed(2)
                            })}
                      </p>
                    </div>
                  )}
                </div>
              </div>
              <div className='flex-col items-start gap-2 hidden md:flex'>
                {refundable && (
                  <div className='flex flex-col mb-2'>
                    <h4 className='text-sm text-green-600 font-semibold'>
                      {t('hotelCard.freeCancellation')}
                    </h4>
                    <p className='text-sm text-green-600'>
                      {t('hotelCard.freeCancellationDescription')}
                    </p>
                  </div>
                )}
                <div className='flex flex-wrap gap-2'>
                  {refundable ? (
                    <Badge
                      icon={<Check weight='bold' size={18} />}
                      type='success'
                    >
                      {t('hotelCard.refundable')}
                    </Badge>
                  ) : (
                    <Badge
                      icon={<Warning weight='bold' size={18} />}
                      type='warning'
                    >
                      {t('hotelCard.nonRefundable')}
                    </Badge>
                  )}
                  {mealPlan && (
                    <Badge
                      icon={
                        mealPlan.code === 'RO' ? (
                          <Prohibit weight='bold' size={18} />
                        ) : (
                          <ForkKnife weight='bold' size={18} />
                        )
                      }
                      type={mealPlan.code === 'RO' ? 'danger' : 'info'}
                    >
                      {mealPlan.description}
                    </Badge>
                  )}
                </div>
              </div>
            </div>
            <div className='flex flex-col items-start md:items-end justify-between md:ml-2'>
              <div className='hidden md:flex'>
                {price?.tripcashInfo ? (
                  <Badge
                    type='info'
                    icon={<HandCoins weight='duotone' size={22} />}
                  >
                    {t('hotelCard.tripcash', {
                      value: price.tripcashInfo?.formatedValue
                    })}
                  </Badge>
                ) : null}
              </div>
              <div className='flex flex-col items-start md:items-end justify-center'>
                <p className='text-xs text-gray-500'>{price?.mainText}</p>
                {!unavailable ? (
                  <div className='flex flex-col items-start md:items-end'>
                    {pricePromotional && (
                      <Badge
                        className='mt-2'
                        icon={<Tag weight='bold' size={14} />}
                        type='success'
                        size='small'
                      >
                        {t('hotelCard.discountOnPix')}
                      </Badge>
                    )}
                    {pricePromotional ? (
                      <div className='flex gap-2 items-baseline whitespace-nowrap'>
                        <p className='text-sm line-through text-gray-500'>{`${price?.currencySymbol} ${price?.main}`}</p>
                        <h3 className='text-primary-900 text-md md:text-xl font-semibold'>{`${pricePromotional?.currencySymbol} ${pricePromotional?.main}`}</h3>
                      </div>
                    ) : (
                      <div className='flex gap-2 items-baseline whitespace-nowrap'>
                        <h3 className='text-primary-900 text-lg md:text-xl font-semibold'>{`${price?.currencySymbol} ${price?.main}`}</h3>
                      </div>
                    )}
                  </div>
                ) : (
                  <h3>{t('hotelCard.unavailableRooms')}</h3>
                )}
                <p className='text-xs text-gray-500'>{price?.taxMessage}</p>
              </div>
              <Button
                color='primary'
                fontWeight='bold'
                onClick={() => {}}
                disabled={unavailable}
                className='hidden md:flex'
              >
                {unavailable ? null : (
                  <ArrowBendUpRight size={18} weight='bold' />
                )}
                {unavailable ? t('hotelCard.unavailable') : t('hotelCard.book')}
              </Button>
            </div>
          </div>
        </div>
        <div className='flex flex-col items-start gap-2 md:hidden'>
          <Button
            color='primary'
            onClick={() => {}}
            disabled={unavailable}
            size='small'
            fullWidth
          >
            {unavailable ? null : <ArrowBendUpRight size={18} weight='bold' />}
            {unavailable ? t('hotelCard.unavailable') : t('hotelCard.book')}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default HotelCardResult;
