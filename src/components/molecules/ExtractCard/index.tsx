import { Check, ClockClockwise, X } from '@phosphor-icons/react';
import classNames from 'classnames';

export interface IExtractCard {
  status: 'CONFIRMED' | 'PENDING' | 'CANCELED';
  statusDisplay: string;
  description: string;
  outdated?: boolean;
  value: string;
  time: string;
}

const ExtractCard = ({
  status,
  statusDisplay,
  description,
  value,
  outdated,
  time
}: IExtractCard) => {
  const bgColors = classNames({
    'bg-green-100/40': status === 'CONFIRMED',
    'bg-orange-100/40': status === 'PENDING',
    'bg-red-100/40': status === 'CANCELED'
  });

  return (
    <div
      className={`flex gap-4 rounded-button py-3 px-4 ${bgColors} ${
        outdated ? 'opacity-50' : ''
      }`}
    >
      <div className='w-[45px] h-[45px] flex-none rounded-full flex justify-center items-center bg-white text-gray-500'>
        {status === 'CONFIRMED' ? (
          <Check size={22} />
        ) : status === 'PENDING' ? (
          <ClockClockwise size={22} />
        ) : (
          <X size={22} />
        )}
      </div>
      <div className='w-full flex flex-col justify-center'>
        <div className='flex justify-between'>
          <h3 className='font-medium text-primary-900'>{statusDisplay}</h3>
          <p className='text-gray-500 text-xs'>{time}</p>
        </div>
        <p className='text-sm text-gray-500'>{description}</p>
        <p className='text-sm text-gray-500'>{value}</p>
      </div>
    </div>
  );
};

export default ExtractCard;
