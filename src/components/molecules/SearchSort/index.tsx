import Tag from '@components/atoms/Tag';
import { SortAscending } from '@phosphor-icons/react';
import { FC, useCallback, useMemo, useState } from 'react';
import { ISearchPayload, ISearchSorting } from 'src/types/search';
import { useSearch } from 'src/context/SearchContext';
import Dropdown, { DropdownItemType } from '@components/molecules/Dropdown';

interface ISearchSortProps {
  sorting: ISearchSorting[];
  // eslint-disable-next-line no-unused-vars
  onOptionSelect: (payload: ISearchPayload) => void;
}

const SearchSort: FC<ISearchSortProps> = ({ sorting, onOptionSelect }) => {
  const [open, setOpen] = useState(false);
  const { searchPayload } = useSearch();
  const handleSelect = useCallback(
    (value: string) => {
      onOptionSelect({ ...searchPayload, sorting: value });
      setOpen(false);
    },
    [onOptionSelect, searchPayload]
  );

  const { selectedOption, options } = useMemo<{
    selectedOption: string;
    options: DropdownItemType[];
  }>(() => {
    return sorting.reduce(
      (acc, sort) => {
        if (sort.selected) {
          acc.selectedOption = sort.display;
        } else {
          acc.options.push({
            text: sort.display,
            onClick: () => handleSelect(sort.label)
          });
        }
        return acc;
      },
      { selectedOption: '', options: [] as DropdownItemType[] }
    );
  }, [sorting, handleSelect]);

  return (
    <div className='relative hidden md:block'>
      <Dropdown open={open} items={options} onToggle={setOpen}>
        <Tag
          color='primary'
          onClick={() => setOpen(true)}
          iconLeft={<SortAscending size={20} />}
        >
          {selectedOption}
        </Tag>
      </Dropdown>
    </div>
  );
};

export default SearchSort;
