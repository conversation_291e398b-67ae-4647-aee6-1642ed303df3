import { FC, ReactNode } from 'react';
import Breadcrumbs, {
  BreadcrumbsItemType
} from '@components/atoms/Breadcrumbs';

interface IHeaderBreadcrumbs {
  items: BreadcrumbsItemType[];
  action?: ReactNode;
}

const HeaderBreadcrumbs: FC<IHeaderBreadcrumbs> = ({ items, action }) => {
  return (
    <div className='bg-white py-4'>
      <div className='container'>
        <Breadcrumbs items={items} />
        {action}
      </div>
    </div>
  );
};

export default HeaderBreadcrumbs;
