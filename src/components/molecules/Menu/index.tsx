import { ReactNode } from 'react';
import MenuButton from '@components/atoms/MenuButton';

export type MenuItem = {
  text: string;
  icon?: ReactNode;
  route?: string;
  onClick?: Function;
};

interface IMenu {
  title?: string;
  items: MenuItem[];
}

const Menu = ({ title, items }: IMenu) => {
  return (
    <div className='flex flex-col gap-2 bg-white p-6 rounded-default'>
      {title && <p className='text-gray-500'>{title}</p>}
      {items.map(item => (
        <MenuButton
          key={item.text}
          text={item.text}
          icon={item.icon}
          onClick={() => item.onClick?.()}
        />
      ))}
    </div>
  );
};

export default Menu;
