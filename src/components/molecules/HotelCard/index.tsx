import { motion } from 'framer-motion';

interface IHotelCard {
  image: string;
  name?: string;
  address?: string;
  onClick: Function;
  index?: number;
}

const HotelCard = ({
  image,
  name,
  address,
  onClick,
  index = 0
}: IHotelCard) => {
  return (
    <motion.div
      className='w-full cursor-pointer bg-white p-3 rounded-default group'
      initial={{ marginLeft: 20, opacity: 0 }}
      whileInView={{ marginLeft: 0, opacity: 1 }}
      viewport={{ once: true }}
      exit={{ marginLeft: 20, opacity: 0 }}
      transition={{
        delay: 0.2 * index,
        duration: 0.7
      }}
      onClick={() => onClick?.()}
    >
      <div className='relative overflow-hidden w-full aspect-square rounded-default mb-4'>
        <img
          className='absolute top-0 left-0 w-[101%] h-[101%] object-cover transition group-hover:scale-110'
          src={image}
          alt={`Imagem do hotel ${name}`}
        />
      </div>
      <h4 className='mt-1 text-primary-900 text-lg font-medium'>{name}</h4>
      <p className='mt-1 text-gray-500 text-sm'>{address}</p>
    </motion.div>
  );
};

export default HotelCard;
