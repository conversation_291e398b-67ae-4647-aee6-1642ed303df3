.react-datepicker {
  @apply border-none;
}

.react-datepicker__input-container input {
  @apply border-none;
}

.react-datepicker__header {
  @apply bg-white border-none p-0;
}

.react-datepicker__week, .react-datepicker__day-names {
  @apply flex text-sm;
}

.react-datepicker__day, .react-datepicker__day-name {
  @apply m-0 w-full min-w-[40px] h-[40px] flex items-center justify-center;
}

.react-datepicker__day--in-range, .react-datepicker__day--in-selecting-range {
  @apply bg-primary-100 rounded-none text-black;
}

.react-datepicker__day--outside-month {
  @apply bg-transparent;
}

.react-datepicker__day:hover, 
.react-datepicker__day--range-start, 
.react-datepicker__day--range-end, 
.react-datepicker__day--selecting-range-end, 
.react-datepicker__day--selecting-range-start, 
.react-datepicker__day--keyboard-selected {
  @apply bg-primary-500 text-white rounded-lg duration-150;
}

.react-datepicker__month-container {
  @apply w-full my-[10px] mx-0;
  @apply md:mx-[10px] md:w-auto;
}

.react-datepicker__day--today {
  @apply bg-transparent;
}

.react-datepicker__month {
  @apply m-0;
}

.react-datepicker__current-month {
  @apply text-lg;
}

.react-datepicker__navigation--next {
  @apply right-[35px] top-[35px];
}

.react-datepicker__navigation--previous {
  @apply left-[35px] top-[35px];
}

.react-datepicker__triangle {
  @apply hidden;
}

#sb-shield {
  position: initial;
}

#ra-widget-verified .ra-widget-verified-wrapper .ra-widget-verified-content {
  border: none;
}