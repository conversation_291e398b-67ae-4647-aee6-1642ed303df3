/* eslint-disable no-unused-vars */
import { RefObject, useEffect } from 'react';

export default function useOnClickOutside(
  ref: RefObject<any>,
  handler: (ev?: Event) => void
) {
  useEffect(() => {
    const listener = (event: Event) => {
      if (!ref?.current || ref?.current?.contains(event.target)) {
        return;
      }
      handler(event);
    };
    document.addEventListener('mousedown', listener);
    document.addEventListener('touchstart', listener);
    return () => {
      document.removeEventListener('mousedown', listener);
      document.removeEventListener('touchstart', listener);
    };
  }, [ref, handler]);
}
