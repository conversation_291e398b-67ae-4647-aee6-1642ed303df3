/* eslint-disable no-param-reassign */
import Image from 'next/image';
import { useState } from 'react';
import Template from '@components/templates/Template';
import { Input } from '@components/atoms/Input';
import Button from '@components/atoms/Button';
import { useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import { IContactPayload } from 'src/types/contact';
import { contact } from 'src/server/contact';
import { isFullName, isValidEmail } from 'src/utils/validation';
import { GetServerSideProps, GetServerSidePropsContext } from 'next';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

interface IContact {
  orderId?: string;
  host: string;
}

const Contact = ({ orderId, host }: IContact) => {
  const { t, i18n } = useTranslation('contact');
  const [formUnavailable] = useState(false);
  const [loading, setLoading] = useState<boolean>(false);
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<IContactPayload>({
    defaultValues: {},
    mode: 'onBlur',
    reValidateMode: 'onBlur'
  });

  const onSubmit = async (data: IContactPayload) => {
    try {
      setLoading(true);

      if (orderId) {
        data = {
          ...data,
          message: `${data.message} | [Sistema] Pedido: ${orderId}`
        };
      }

      await contact({ ...data, lang: i18n.language.toUpperCase() });
      reset();
      toast(t('successToast'), {
        type: 'success'
      });
    } catch (err) {
      toast(t('errorToast'), {
        type: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Template
      title={t('title')}
      description={t('description')}
      host={host}
      navbar={{
        fixed: false,
        backgroundColor: 'white',
        foregroundColor: 'primary',
        scrollingBackgroundColor: 'white',
        scrollingForegroundColor: 'primary'
      }}
    >
      <div className='container flex flex-col-reverse md:flex-row items-center justify-center py-8 pt-4 md:py-24 gap-24 w-full'>
        <div className='hidden md:block w-full relative flex-2 aspect-square'>
          <Image
            src='/images/contact.png'
            alt={t('headerTitle')}
            className='object-cover rounded-default'
            fill
          />
        </div>
        <form
          onSubmit={handleSubmit(data => onSubmit(data))}
          className='flex flex-col flex-2'
        >
          <h2 className='text-xl text-primary-900 font-semibold'>
            {t('headerTitle')}
          </h2>
          <p className='mt-1 text-gray-500'>
            {formUnavailable ? t('introTextUnavailable') : t('introText')}
          </p>
          {!formUnavailable ? (
            <div className='flex flex-col gap-2 mt-6'>
              <Input
                border
                type='text'
                label={t('fullNameLabel')}
                {...register('name', {
                  required: t('fullNameErrorRequired'),
                  validate: name =>
                    isFullName(name) || t('fullNameErrorInvalid')
                })}
                error={errors.name?.message}
              />
              <Input
                border
                type='email'
                label={t('emailLabel')}
                {...register('mail', {
                  required: t('emailErrorRequired'),
                  validate: email =>
                    isValidEmail(email) || t('emailErrorInvalid')
                })}
                error={errors.mail?.message}
              />
              <Input
                border
                type='text'
                label={t('messageLabel')}
                {...register('message', {
                  required: t('messageErrorRequired')
                })}
                error={errors.message?.message}
              />
              <Button color='primary' type='submit' loading={loading}>
                {t('submitButton')}
              </Button>
            </div>
          ) : null}
        </form>
      </div>
    </Template>
  );
};

export const getServerSideProps: GetServerSideProps = async (
  ctx: GetServerSidePropsContext
) => {
  const orderId = ctx?.query?.order ?? '';

  return {
    props: { orderId, ...(await serverSideTranslations(ctx.locale!)) }
  };
};

export default Contact;
