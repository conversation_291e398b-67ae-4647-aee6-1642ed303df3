import Template from '@components/templates/Template';
import { GetServerSideProps, GetServerSidePropsContext } from 'next';
import EmailVerificationBox from '@components/organisms/EmailVerificationBox';
import { verifyEmail } from 'src/server/auth';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useTranslation } from 'next-i18next';

interface IRecoveryPassword {
  status: 'VERIFIED' | 'ERROR' | 'TOKEN_NOT_FOUND';
  host: string;
}

const EmailVerify = ({ status, host }: IRecoveryPassword) => {
  const { t } = useTranslation('verify-account');

  return (
    <Template
      title={t('title')}
      description={t('description')}
      host={host}
      navbar={{
        fixed: false,
        backgroundColor: 'white',
        foregroundColor: 'primary',
        scrollingBackgroundColor: 'white',
        scrollingForegroundColor: 'primary'
      }}
    >
      <div className='container my-6 md:my-16'>
        <EmailVerificationBox status={status} />
      </div>
    </Template>
  );
};

export const getServerSideProps: GetServerSideProps = async (
  ctx: GetServerSidePropsContext
) => {
  const { token } = ctx.query;

  if (!token) {
    return {
      props: {
        status: 'TOKEN_NOT_FOUND',
        ...(await serverSideTranslations(ctx.locale!))
      }
    };
  }

  try {
    await verifyEmail(token as string);

    return {
      props: {
        status: 'VERIFIED',
        ...(await serverSideTranslations(ctx.locale!))
      }
    };
  } catch (err) {
    return {
      props: {
        status: 'ERROR',
        ...(await serverSideTranslations(ctx.locale!))
      }
    };
  }
};

export default EmailVerify;
