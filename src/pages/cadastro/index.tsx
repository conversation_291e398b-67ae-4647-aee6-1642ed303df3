import Template from '@components/templates/Template';
import RegisterBox from '@components/organisms/RegisterBox';
import { registerMetaTags } from '@consts/meta/register';
import { useTranslation } from 'next-i18next';

import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { GetServerSideProps, GetServerSidePropsContext } from 'next';

interface IRegisterProps {
  host: string;
}

const Register = ({ host }: IRegisterProps) => {
  const { t } = useTranslation('register');

  return (
    <Template
      title={t('title')}
      description={t('description')}
      extraTags={registerMetaTags}
      host={host}
      navbar={{
        fixed: false,
        backgroundColor: 'white',
        foregroundColor: 'primary',
        scrollingBackgroundColor: 'white',
        scrollingForegroundColor: 'primary'
      }}
    >
      <div className='container my-6 md:my-16'>
        <RegisterBox />
      </div>
    </Template>
  );
};

export const getServerSideProps: GetServerSideProps = async (
  ctx: GetServerSidePropsContext
) => {
  return {
    props: {
      ...(await serverSideTranslations(ctx.locale!))
    }
  };
};

export default Register;
