import AccountTemplate from '@components/templates/AccountTemplate';
import TripcashDashboardCard from '@components/molecules/TripcashDashboardCard';
import {
  ArrowSquareOut,
  ClockCounterClockwise,
  ListMagnifyingGlass,
  PiggyBank
} from '@phosphor-icons/react';
import ExtractCard from '@components/molecules/ExtractCard';
import { GetServerSideProps, GetServerSidePropsContext } from 'next';
import { getTripcash } from 'src/server/tripcash';
import { ITripcash } from 'src/types/tripcash';
import { getDateFNSLocale } from 'src/utils/date';
import EmptyState from '@components/molecules/EmptyState';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { i18n, useTranslation } from 'next-i18next';
import { formatDistance } from 'date-fns';

interface IMyTripcash {
  tripcashData: ITripcash;
  host: string;
}

const MyTripcash = ({ tripcashData, host }: IMyTripcash) => {
  const { t } = useTranslation(['common', 'my-tripcash'], {
    nsMode: 'fallback'
  });

  return (
    <AccountTemplate
      title={t('title')}
      description={t('description')}
      host={host}
      navbar={{
        fixed: false,
        backgroundColor: 'white',
        foregroundColor: 'primary',
        scrollingBackgroundColor: 'white',
        scrollingForegroundColor: 'primary'
      }}
    >
      <div className='flex flex-col gap-6'>
        <div className='flex flex-col md:flex-row gap-4'>
          <TripcashDashboardCard
            icon={<PiggyBank size={22} />}
            label={t('cards.balance.label')}
            description={tripcashData.availableAmount.description}
            values={tripcashData.availableAmount.amounts}
            color='success'
          />
          <TripcashDashboardCard
            icon={<ClockCounterClockwise size={22} />}
            label={t('cards.pending.label')}
            description={tripcashData.pendingAmount.description}
            values={tripcashData.pendingAmount.amounts}
            color='warning'
          />
          <TripcashDashboardCard
            icon={<ArrowSquareOut size={22} />}
            label={t('cards.used.label')}
            description={tripcashData.usedAmount.description}
            values={tripcashData.usedAmount.amounts}
            color='info'
          />
        </div>
        <div className='flex gap-4'>
          <div className='w-full flex flex-col bg-white p-6 rounded-default gap-4'>
            <p className='text-gray-500'>{t('extract.title')}</p>
            <div className='flex flex-col gap-3'>
              {tripcashData.transactions.length ? (
                tripcashData.transactions.map(transaction => (
                  <ExtractCard
                    key={transaction.id}
                    description={transaction.description}
                    status={transaction.status}
                    statusDisplay={transaction.statusDisplay}
                    value={transaction.amount.formattedValue}
                    time={formatDistance(
                      new Date(transaction.createAt),
                      new Date(),
                      {
                        locale: getDateFNSLocale(i18n?.language!),
                        addSuffix: true
                      }
                    )}
                  />
                ))
              ) : (
                <EmptyState
                  icon={<ListMagnifyingGlass size={50} />}
                  title={t('extract.empty.title')}
                  description={t('extract.empty.description')}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </AccountTemplate>
  );
};

export const getServerSideProps: GetServerSideProps = async (
  ctx: GetServerSidePropsContext
) => {
  const { token } = ctx.req.cookies;

  if (!token) {
    const destination =
      ctx.locale === ctx.defaultLocale
        ? `${i18n!.t('routes.signIn')}?referrer=${i18n!.t('routes.myTripcash')}`
        : `/${ctx.locale}${i18n!.t('routes.signIn')}?referrer=${i18n!.t(
            'routes.myTripcash'
          )}`;

    return {
      redirect: {
        destination,
        permanent: false
      }
    };
  }

  try {
    const { data: tripcashData } = await getTripcash(
      ctx.req.cookies?.token as string,
      ctx.locale?.toUpperCase() || 'EN_US'
    );

    return {
      props: {
        tripcashData,
        ...(await serverSideTranslations(ctx.locale!))
      }
    };
  } catch (err) {
    return {
      redirect: {
        permanent: false,
        destination: '/'
      }
    };
  }
};

export default MyTripcash;
