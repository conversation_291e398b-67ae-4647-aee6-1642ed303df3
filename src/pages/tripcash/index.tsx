import Template from '@components/templates/Template';
import {
  ArrowsClockwise,
  Bed,
  ClockClockwise,
  CreditCard,
  Gear,
  HandArrowDown,
  HandCoins,
  PiggyBank
} from '@phosphor-icons/react';
import TripcashHeader from '@components/organisms/TripcashHeader';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { GetServerSideProps, GetServerSidePropsContext } from 'next';

interface ITripcashProps {
  host: string;
}

const Tripcash = ({ host }: ITripcashProps) => {
  const { t } = useTranslation('tripcash');

  return (
    <Template
      title={t('title')}
      description={t('description')}
      mobileBottomNavigationBar
      host={host}
      navbar={{
        fixed: true,
        backgroundColor: 'transparent',
        foregroundColor: 'white',
        scrollingBackgroundColor: 'white',
        scrollingForegroundColor: 'primary'
      }}
    >
      <TripcashHeader />
      <div className='flex pt-[50px] pb-[50px] bg-white'>
        <div className='container flex flex-col md:flex-row justify-center gap-[25px] md:gap-[100px]'>
          <div className='w-full flex gap-[15px]'>
            <Bed size={24} className='flex-none mb-[15px] text-primary-900' />
            <div>
              <h3 className='font-semibold mb-[5px] text-primary-900'>
                {t('howWorksReserveTitle')}
              </h3>
              <p className='font-light text-gray-500 leading-6'>
                {t('howWorksReserveDescription')}
              </p>
            </div>
          </div>
          <div className='w-full flex gap-[15px]'>
            <PiggyBank
              size={24}
              className='flex-none mb-[15px] text-primary-900'
            />
            <div>
              <h3 className='font-semibold mb-[5px] text-primary-900'>
                {t('howWorksSaveTitle')}
              </h3>
              <p className='font-light text-gray-500 leading-6'>
                {t('howWorksSaveDescription')}
              </p>
            </div>
          </div>
          <div className='w-full flex gap-[15px]'>
            <Gear size={24} className='flex-none mb-[15px] text-primary-900' />
            <div>
              <h3 className='font-semibold mb-[5px] text-primary-900'>
                {t('howWorksManageTitle')}
              </h3>
              <p className='font-light text-gray-500 leading-6'>
                {t('howWorksManageDescription')}
              </p>
            </div>
          </div>
          <div className='w-full flex gap-[15px]'>
            <CreditCard
              size={24}
              className='flex-none mb-[15px] text-primary-900'
            />
            <div>
              <h3 className='font-semibold mb-[5px] text-primary-900'>
                {t('howWorksUseTitle')}
              </h3>
              <p className='font-light text-gray-500 leading-6'>
                {t('howWorksUseDescription')}
              </p>
            </div>
          </div>
        </div>
      </div>
      <div className='container flex flex-col md:flex-row items-center justify-center pt-[50px] pb-[50px] gap-[25px] md:gap-[100px]'>
        <div className='flex flex-col items-start gap-[10px] flex-1'>
          <HandCoins size={42} className='mb-[15px] text-primary-500' />
          <h1 className='font-semibold text-primary-900'>{t('aboutTitle')}</h1>
          <p className='font-light text-primary-900 leading-relaxed mb-[15px]'>
            {t('aboutDescription')}
          </p>
          <h3 className='font-semibold text-primary-900'>{t('walletTitle')}</h3>
          <p className='font-light text-primary-900 leading-relaxed mb-[15px]'>
            {t('walletDescription')}
          </p>
          <h3 className='font-semibold text-primary-900'>
            {t('howToUseTitle')}
          </h3>
          <p className='font-light text-primary-900 leading-relaxed'>
            {t('howToUseDescription')}
          </p>
        </div>
        <img
          src='/images/tripcash_hero.webp'
          alt='Foto de uma família de braços pra cima'
          className='w-full md:w-1/2 rounded-xs'
        />
      </div>
      <div className='container flex flex-col px-4'>
        <h3 className='font-semibold text-primary-900 mb-[5px]'>
          {t('rulesTitle')}
        </h3>
        <p className='font-light text-gray-500 leading-6'>
          {t('rulesDescription')}
        </p>
        <div className='flex flex-col md:flex-row justify-center gap-[25px] md:gap-[100px] pt-[50px] pb-[100px]'>
          <div className='w-full flex flex-col'>
            <HandArrowDown size={32} className='mb-[15px] text-primary-500' />
            <h3 className='font-semibold text-primary-900 mb-[5px]'>
              {t('ruleWhenReceiveTitle')}
            </h3>
            <p className='font-light text-gray-500 leading-6'>
              {t('ruleWhenReceiveDescription')}
            </p>
          </div>
          <div className='w-full flex flex-col'>
            <ClockClockwise size={32} className='mb-[15px] text-primary-500' />
            <h3 className='font-semibold text-primary-900 mb-[5px]'>
              {t('ruleDurationTitle')}
            </h3>
            <p className='font-light text-gray-500 leading-6'>
              {t('ruleDurationDescription')}
            </p>
          </div>
          <div className='w-full flex flex-col'>
            <ArrowsClockwise size={32} className='mb-[15px] text-primary-500' />
            <h3 className='font-semibold text-primary-900 mb-[5px]'>
              {t('ruleTransferTitle')}
            </h3>
            <p className='font-light text-gray-500 leading-6'>
              {t('ruleTransferDescription')}
            </p>
          </div>
        </div>
      </div>
    </Template>
  );
};

export const getServerSideProps: GetServerSideProps = async (
  ctx: GetServerSidePropsContext
) => {
  return {
    props: {
      ...(await serverSideTranslations(ctx.locale!))
    }
  };
};

export default Tripcash;
