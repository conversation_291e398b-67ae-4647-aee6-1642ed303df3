import Template from '@components/templates/Template';
import ChangePasswordBox from '@components/organisms/ChangePasswordBox';
import { GetServerSideProps, GetServerSidePropsContext } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { i18n, useTranslation } from 'next-i18next';

interface IChangePasswordProps {
  host: string;
}

const ChangePassword = ({ host }: IChangePasswordProps) => {
  const { t } = useTranslation('password-change');
  return (
    <Template
      title={t('title')}
      description={t('description')}
      host={host}
      navbar={{
        fixed: false,
        backgroundColor: 'white',
        foregroundColor: 'primary',
        scrollingBackgroundColor: 'white',
        scrollingForegroundColor: 'primary'
      }}
    >
      <div className='container my-6 md:my-16'>
        <ChangePasswordBox />
      </div>
    </Template>
  );
};

export const getServerSideProps: GetServerSideProps = async (
  ctx: GetServerSidePropsContext
) => {
  const { token } = ctx.req.cookies;

  if (!token) {
    return {
      redirect: {
        destination: `${i18n!.t('routes.signIn')}?referrer=${i18n!.t(
          'routes.passwordChange'
        )}`,
        permanent: false
      }
    };
  }

  return {
    props: {
      ...(await serverSideTranslations(ctx.locale!))
    }
  };
};

export default ChangePassword;
