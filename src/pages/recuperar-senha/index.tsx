import Template from '@components/templates/Template';
import RecoveryPasswordRequestBox from '@components/organisms/RecoveryPasswordRequestBox';
import { GetServerSideProps, GetServerSidePropsContext } from 'next';
import RecoveryPasswordBox from '@components/organisms/RecoveryPasswordBox';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useTranslation } from 'next-i18next';

interface IRecoveryPassword {
  token: string;
  host: string;
}

const RecoveryPassword = ({ token, host }: IRecoveryPassword) => {
  const { t } = useTranslation('password-recovery');

  return (
    <Template
      title={t('title')}
      description={t('description')}
      host={host}
      navbar={{
        fixed: false,
        backgroundColor: 'white',
        foregroundColor: 'primary',
        scrollingBackgroundColor: 'white',
        scrollingForegroundColor: 'primary'
      }}
    >
      <div className='container my-6 md:my-16'>
        {token ? (
          <RecoveryPasswordBox token={token} />
        ) : (
          <RecoveryPasswordRequestBox />
        )}
      </div>
    </Template>
  );
};

export const getServerSideProps: GetServerSideProps = async (
  ctx: GetServerSidePropsContext
) => {
  const { token } = ctx.query;

  if (token) {
    return {
      props: {
        token,
        ...(await serverSideTranslations(ctx.locale!))
      }
    };
  }

  return {
    props: {
      ...(await serverSideTranslations(ctx.locale!))
    }
  };
};

export default RecoveryPassword;
