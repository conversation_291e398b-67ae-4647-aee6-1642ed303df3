/* eslint-disable no-shadow */
/* eslint-disable no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-console */
import Template from '@components/templates/Template';
import { CheckoutContext, CheckoutProvider } from 'src/context/CheckoutContext';
import { GetServerSideProps, GetServerSidePropsContext } from 'next';
import { useContext, useEffect, useMemo, useState } from 'react';
import StepFormHead from '@components/molecules/StepFormHead';
import GuestForm from '@components/molecules/GuestForm';

import { postOrder } from 'src/server/order';
import {
  CheckoutFields,
  CheckoutStepEnum,
  CheckoutStepType,
  EmailFormType,
  GuestFormType,
  ICheckoutRequestCreditCardPayer,
  ICheckoutRequestPayload,
  PaymentData,
  PaymentMethodEnum,
  PaymentMethodType
} from 'src/types/checkout';
import {
  getDistributionConfig,
  getPurchaseDescriptionItems,
  getTotalPrice,
  getTripcashInfo,
  mountGuestsPayload,
  mountInstallmentOptions,
  mountRegisterPayloadByFormValues,
  redirectToPurchase,
  sendGTMEvent
} from 'src/utils/checkout';
import {
  CreditCardDefaultValues,
  FormDefaultValues,
  creditCardPayerDefaulValues,
  payerDefaultValues
} from '@consts/checkout';
import CheckoutResumeForm from '@components/molecules/CheckoutResumeForm';
import { convertStringToFormatedDate, getDateFNSLocale } from 'src/utils/date';
import Button from '@components/atoms/Button';
import {
  CreditCard,
  ClockCountdown,
  ArrowLeft,
  ListBullets,
  PixLogo
} from '@phosphor-icons/react';
import PixForm from '@components/molecules/PixForm';
import CreditCardForm from '@components/molecules/CreditCardForm';
import { useRouter } from 'next/router';
import { getCart } from 'src/server/cart';
import { AdditionalTaxes, ICartResponse } from 'src/types/cart';
import { AuthContext } from 'src/context/AuthContext';
import { getCustomer, register } from 'src/server/customer';
import { toast } from 'react-toastify';
import axios, { AxiosError } from 'axios';
import FullLoading from '@components/molecules/Loading';
import CountdownCard from '@components/molecules/CountdownCard';
import EmptyState from '@components/molecules/EmptyState';
import {
  MercadoPageErrorTypes,
  mercadoPagoErrors
} from '@consts/mercadopagoErrors';
import EmailForm from '@components/molecules/EmailForm';
import { setCookie } from 'cookies-next';
import PurchaseDetails from '@components/organisms/PurchaseDetails';
import { v4 as uuidv1 } from 'uuid';
import { checkoutMetaTags } from '@consts/meta/checkout';
import Badge from '@components/atoms/Badge';
import { convertNumberToCurrency } from 'src/utils/currency';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useTranslation } from 'next-i18next';
import ExternalPaymetForm from '@components/molecules/ExternalPaymentForm';
import { useCurrency } from 'src/context/CurrencyContext';
import { Sheet, SheetContent } from '@components/atoms/Sheet';
import { loadMercadoPago } from '@mercadopago/sdk-js';
import * as Sentry from '@sentry/nextjs';

export interface ICheckoutPageProps {
  cart: ICartResponse;
  host: string;
}

const CheckoutPage = ({ setCart }: { setCart: Function }) => {
  const { currency } = useCurrency();
  const {
    cart: checkoutData,
    isTripcashApplied,
    setIsTripcashApplied,
    isExpired,
    setIsExpired
  } = useContext(CheckoutContext);

  const { t, i18n } = useTranslation(
    ['checkout', 'login', 'common', 'purchase'],
    {
      nsMode: 'fallback'
    }
  );
  const [attemptReference] = useState(uuidv1());
  const [overlayOpen, setOverlayOpen] = useState<boolean>(false);
  const [exitConfirmationOverlayOpen, setExitConfirmationOverlayOpen] =
    useState<boolean>(false);
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethodType>(
    checkoutData!.paymentMethodsAccepted[0]
  );
  const [isPaymentMethodSelected, setIsPaymentMethodSelected] =
    useState<boolean>(false);
  const [selectedInstallment, setSelectedInstallment] = useState(1);
  const [formValues, setFormValues] = useState<CheckoutFields>({
    ...FormDefaultValues
  });

  const [mp, setMp] = useState<any>(null);
  const selectedCreditCardApi = checkoutData!.paymentsApi.find(
    paymentApi =>
      (paymentApi.api === 'MERCADO_PAGO' ||
        paymentApi.api === 'MERCADO_PAGO_FLUXO') &&
      paymentApi.method === 'CREDIT_CARD'
  )
    ? 'MERCADO_PAGO'
    : 'DEFAULT';
  const [mercadoPagoInstallmentOptions, setMercadoPagoInstallmentOptions] =
    useState<any>([]);

  const installmentOptions = useMemo(() => {
    const installments = isTripcashApplied
      ? checkoutData!.paymentInstallmentsWithTripcash
      : checkoutData!.paymentInstallments;

    return mountInstallmentOptions(installments);
  }, [isTripcashApplied]);

  const [selectedInstallmentKey, setSelectedInstallmentKey] = useState<string>(
    installmentOptions[selectedInstallment]?.key
  );

  const { user, isAuthenticated, updateUser } = useContext(AuthContext);
  const [step, setStep] = useState<CheckoutStepType>({
    current: CheckoutStepEnum.ACCOUNT,
    next: CheckoutStepEnum.PAYMENT
  });
  const [userAsFirstGuest, setUserAsFirstGuest] = useState<boolean>(true);
  const [loading, setLoading] = useState<boolean>(false);

  const router = useRouter();

  const paymentDisplay = {
    PIX: { icon: <PixLogo size={22} />, label: t('payment.method.pix') },
    CREDIT_CARD: {
      icon: <CreditCard size={24} weight='duotone' />,
      label: t('payment.method.creditCard')
    },
    EXTERNAL: { label: t('payment.method.external') }
  };

  useEffect(() => {
    if (attemptReference) {
      const nethoneScript = document.createElement('script');
      nethoneScript.type = 'text/javascript';
      nethoneScript.id = 'nethone-script';
      nethoneScript.crossOrigin = 'use-credentials';
      nethoneScript.src =
        'https://d354c9v5bptm0r.cloudfront.net/s/68741/dQItJr.js';
      nethoneScript.async = true;
      document.body.appendChild(nethoneScript);
      nethoneScript.onload = () => {
        localStorage.setItem('attempt_reference', attemptReference);
        const nethoneOptions = {
          attemptReference,
          sensitiveFields: [
            'cardHolderName',
            'cardCvv',
            'cardExpirationDate',
            'cardNumber'
          ]
        };
        if (window.dftp) {
          window.dftp?.init?.(nethoneOptions);
        } else {
          nethoneScript.addEventListener('load', () => {
            if (window.dftp) {
              window.dftp?.init?.(nethoneOptions);
            }
          });
        }
      };
    }
  }, [attemptReference]);

  const userAsPayerValues = useMemo(() => {
    if (isAuthenticated) {
      const { name, surname, documentNumber, documentType, ...rest } = user;

      const payerValues = {
        userAsPayer: true,
        firstName: name,
        lastName: surname,
        identification: {
          documentNumber,
          documentType
        },
        ...rest
      } as ICheckoutRequestCreditCardPayer;

      return payerValues;
    }

    return {
      userAsPayer: true,
      email: formValues.email,
      identification: {
        documentType: 'CPF'
      },
      phone: {
        ddi: i18n.language.toUpperCase() === 'PT_BR' ? '+55' : ''
      }
    } as ICheckoutRequestCreditCardPayer;
  }, [user, isAuthenticated, formValues.email]);

  useEffect(() => {
    setFormValues(prev => {
      return {
        ...prev,
        room: [
          {
            adult: [
              {
                name: `${user.name} ${user.surname}`,
                birthday: user.birthday
              }
            ],
            document: user.documentNumber
          },
          ...prev.room.slice(1)
        ]
      };
    });
  }, [user, isAuthenticated]);

  useEffect(() => {
    if (userAsPayerValues) {
      setFormValues(prev => {
        return {
          ...prev,
          pix: {
            ...userAsPayerValues,
            phone: {
              ...(userAsPayerValues?.phone ?? {}),
              ddi:
                userAsPayerValues?.phone?.ddi ??
                (i18n.language.toUpperCase() === 'PT_BR' ? '+55' : ''),
              areaCode: userAsPayerValues?.phone?.areaCode ?? '',
              number: userAsPayerValues?.phone?.number ?? ''
            }
          },
          creditCard: {
            ...prev.creditCard,
            payer: {
              ...userAsPayerValues,
              address: {
                ...prev?.creditCard?.payer?.address,
                ...userAsPayerValues?.address
              }
            }
          },
          external: {
            ...prev.external,
            ...userAsPayerValues
          }
        };
      });
    }
  }, [userAsPayerValues, i18n.language]);

  useEffect(() => {
    if (isAuthenticated && step.current === CheckoutStepEnum.ACCOUNT) {
      setStep(prev => {
        return { current: prev.next, next: prev.next + 1 };
      });
    } else if (!isAuthenticated && step.current !== CheckoutStepEnum.ACCOUNT) {
      setStep({
        current: CheckoutStepEnum.ACCOUNT,
        next: CheckoutStepEnum.PAYMENT
      });
    }
  }, [isAuthenticated]);

  const toPreviousStep = () => {
    if (step.current <= 1) {
      setExitConfirmationOverlayOpen(true);
      return;
    }

    setStep(prev => {
      return { current: prev.current - 1, next: prev.next - 1 };
    });
  };

  const handleStepSubmit = (
    data: EmailFormType | PaymentData | GuestFormType,
    current?: number,
    next?: number
  ) => {
    setFormValues(prev => {
      return { ...prev, ...data };
    });
    window.scrollTo({ top: 0 });
    setStep(prev => {
      return { current: current ?? prev.next, next: next ?? prev.next + 1 };
    });
  };

  const handleUpdateCart = async () => {
    try {
      const { data } = await getCart(checkoutData!.id);
      setCart(data);
    } catch (err) {
      toast(t('errors.tripcash'), { type: 'error' });
    }
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);

      const { pix, creditCard, external } = formValues;
      let payer;
      let card;
      let total;
      let cardTokenResponse;

      if (paymentMethod === PaymentMethodEnum.CREDIT_CARD) {
        payer = creditCard.payer;
        const { name: holderName, cvv, expireDate, number } = creditCard;
        const [expirationMonth, expirationYear] = expireDate.split('/');

        const installments = isTripcashApplied
          ? checkoutData!.paymentInstallmentsWithTripcash
          : checkoutData!.paymentInstallments;

        card = {
          holderName,
          cvv,
          number,
          expirationMonth,
          expirationYear: expirationYear.substring(2)
        };

        const optionSelected = installments.find(
          installment => installment.installments === selectedInstallment
        );
        total = optionSelected?.totalAmountWithDecimal;

        if (selectedCreditCardApi === 'MERCADO_PAGO') {
          cardTokenResponse = await mp.createCardToken({
            cardNumber: number.replace(/\s/g, ''),
            cardholderName: holderName,
            cardExpirationMonth: expirationMonth,
            cardExpirationYear: expirationYear,
            securityCode: cvv,
            identificationType: payer.identification.documentType,
            identificationNumber: payer.identification.documentNumber.replace(
              /\D/g,
              ''
            )
          });
        }
      } else {
        payer = PaymentMethodEnum.PIX === paymentMethod ? pix : external;
        total = isTripcashApplied
          ? checkoutData?.priceWithTripcash?.priceWithDecimal
          : checkoutData!.price.priceWithDecimal;
      }

      const guests = mountGuestsPayload(formValues.room);

      const payload: ICheckoutRequestPayload = {
        paymentInfo: {
          card,
          payer,
          method: paymentMethod,
          paymentMethodId: creditCard.paymentMethodId,
          paymentMethodOptionId: creditCard.paymentMethodOptionId,
          installmentOptionId: creditCard.installmentOptionId,
          token: cardTokenResponse?.id || undefined,
          installments: selectedInstallment,
          tripcashApplied: isTripcashApplied,
          attemptReference
        },
        cartId: checkoutData!.id,
        guests
      };

      if (!isAuthenticated) {
        const customer = mountRegisterPayloadByFormValues(
          payer,
          i18n.language.toUpperCase()
        );
        const {
          data: { token }
        } = await register(customer);
        setCookie('token', token);
        const { data: updatedUser } = await getCustomer();
        updateUser?.(updatedUser);
      }

      const {
        data: { orderId }
      } = await postOrder(payload);

      sendGTMEvent(payload, total!, checkoutData!);
      redirectToPurchase(t, i18n.language, orderId!, router);
    } catch (error: AxiosError | any) {
      const message = t('errors.default');

      const sentryError: AxiosError | any = error as AxiosError;
      delete sentryError.config?.data;
      delete sentryError.config?.headers?.Authorization;

      Sentry.captureException(sentryError, {
        attachments: [
          {
            filename: 'handle-submit.json',
            data: JSON.stringify(sentryError)
          }
        ]
      });

      if (axios.isAxiosError(error)) {
        if (error?.response?.status === 400) {
          toast(error?.response?.data?.message || '', { type: 'error' });
        } else {
          toast(message, { type: 'error' });
        }
      } else if (Boolean(error?.cause) || error?.[0].code) {
        const code = (error?.cause?.[0].code ||
          error?.[0].code ||
          'default') as MercadoPageErrorTypes;

        toast(mercadoPagoErrors?.[code] || message, {
          type: 'error'
        });
      } else {
        toast(message, { type: 'error' });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleChangePaymentMethod = (data: PaymentMethodType) => {
    const isPix = data === PaymentMethodEnum.PIX;
    setPaymentMethod(data);
    setFormValues(prev =>
      isPix
        ? {
            ...prev,
            creditCard: {
              ...CreditCardDefaultValues,
              payer: { ...creditCardPayerDefaulValues, ...userAsPayerValues }
            }
          }
        : { ...prev, pix: { ...payerDefaultValues, ...userAsPayerValues } }
    );
    if (isPix) setSelectedInstallment(1);
  };

  const purchaseDescriptionItems = useMemo(() => {
    return getPurchaseDescriptionItems(
      t,
      checkoutData!,
      isTripcashApplied,
      paymentMethod,
      selectedInstallment
    );
  }, [t, checkoutData, isTripcashApplied, paymentMethod, selectedInstallment]);

  const tripcashInfo = useMemo(() => {
    return getTripcashInfo(isTripcashApplied, paymentMethod, checkoutData!);
  }, [isTripcashApplied, paymentMethod]);

  const totalPrice = useMemo(() => {
    return getTotalPrice(
      paymentMethod,
      selectedInstallment,
      isTripcashApplied,
      checkoutData!
    );
  }, [paymentMethod, selectedInstallment, isTripcashApplied, checkoutData]);

  const additionalTaxes: AdditionalTaxes[] = useMemo(() => {
    let additionalTaxes = null;

    if (isTripcashApplied) {
      additionalTaxes =
        paymentMethod === 'PIX'
          ? checkoutData!.pricePromotionalWithTripcash.additionalTaxes
          : checkoutData!.priceWithTripcash.additionalTaxes;
    }

    additionalTaxes =
      paymentMethod === 'PIX'
        ? checkoutData!.pricePromotional.additionalTaxes
        : checkoutData!.price.additionalTaxes;

    return additionalTaxes ? [additionalTaxes] : [];
  }, [checkoutData]);

  const setInstallmentsValues = (value: number = selectedInstallment) => {
    let selectedMercadoPagoOption: any = null;

    const selectedOption = installmentOptions[value - 1];
    if (mercadoPagoInstallmentOptions.length === 0) {
      selectedMercadoPagoOption = mercadoPagoInstallmentOptions[value - 1];
    }

    setSelectedInstallment(value);
    setSelectedInstallmentKey(selectedOption.key);

    setFormValues(prev => {
      return {
        ...prev,
        creditCard: {
          ...prev.creditCard,
          installments: value,
          paymentMethodOptionId:
            selectedMercadoPagoOption?.paymentMethodOptionId,
          installmentOptionId: selectedOption.installmentOptionId
        }
      };
    });
  };

  const getMercadoPagoInstallments = async (number: string) => {
    let mercadoPagoInstallments: any = null;

    const installmentsPayload = {
      amount: (totalPrice! / 100)?.toString() ?? '0',
      bin: number.replaceAll(' ', '').substring(0, 6),
      paymentTypeId: 'credit_card'
    };

    try {
      const response = await mp.getInstallments(installmentsPayload);
      mercadoPagoInstallments = response?.[0];
    } catch (error) {
      Sentry.captureException(error, {
        attachments: [
          {
            filename: 'mp-get-installment.json',
            data: JSON.stringify(error)
          }
        ]
      });
    }

    setFormValues(prev => {
      return {
        ...prev,
        creditCard: {
          ...prev.creditCard,
          paymentMethodId: mercadoPagoInstallments?.payment_method_id
        }
      };
    });

    const mercadoPagoInstallmentsResponse =
      mercadoPagoInstallments?.payer_costs.map((item: any) => ({
        value: item.installments,
        paymentMethodOptionId: item.payment_method_option_id,
        label: item.recommended_message
      })) || [];

    setMercadoPagoInstallmentOptions(mercadoPagoInstallmentsResponse);
    setInstallmentsValues(selectedInstallment);
  };

  useEffect(() => {
    const init = async () => {
      await loadMercadoPago();
      const mp = await new window.MercadoPago(selectedInstallmentKey, {
        locale: i18n?.language.replace('_', '-')
      });

      setMp(mp);
    };

    if (selectedInstallmentKey && selectedCreditCardApi === 'MERCADO_PAGO') {
      init();
    }
  }, [selectedInstallmentKey, i18n?.language]);

  const setCreditCardNumber = (number: string) => {
    setFormValues(prev => {
      return {
        ...prev,
        creditCard: {
          ...prev.creditCard,
          number: number.replace(/\s/g, '')
        }
      };
    });
  };

  useEffect(() => {
    if (
      mp &&
      formValues.creditCard.number.replaceAll(' ', '').replaceAll('_', '')
        .length >= 6
    ) {
      getMercadoPagoInstallments(formValues.creditCard.number);
    } else {
      setInstallmentsValues(selectedInstallment);
    }
  }, [mp, selectedInstallmentKey, formValues.creditCard.number, totalPrice]);

  const paymentForms = {
    [PaymentMethodEnum.CREDIT_CARD]: (
      <CreditCardForm
        tripcashApplied={isTripcashApplied}
        initialValues={{
          ...formValues,
          creditCard: {
            ...formValues.creditCard,
            installments: selectedInstallment
          }
        }}
        userValues={userAsPayerValues}
        handleCreditCardSubmit={data => {
          handleStepSubmit(data);
          setIsPaymentMethodSelected(true);
        }}
        installments={installmentOptions}
        selectedCreditCardApi={selectedCreditCardApi}
        setCreditCardNumber={setCreditCardNumber}
        setSelectedInstallment={installment =>
          setSelectedInstallment(installment)
        }
        selectedInstallment={selectedInstallment}
        setInstallmentsValues={setInstallmentsValues}
        toPreviousStep={() => toPreviousStep()}
        isAuthenticated={isAuthenticated}
      />
    ),
    [PaymentMethodEnum.PIX]: (
      <PixForm
        initialValues={formValues}
        userValues={userAsPayerValues}
        toPreviousStep={() => toPreviousStep()}
        isAuthenticated={isAuthenticated}
        handlePixSubmit={data => {
          handleStepSubmit(data);
          setIsPaymentMethodSelected(true);
        }}
      />
    ),
    [PaymentMethodEnum.EXTERNAL]: (
      <ExternalPaymetForm
        userValues={userAsPayerValues}
        onSubmit={data => {
          handleStepSubmit(data);
          setIsPaymentMethodSelected(true);
        }}
        toPreviousStep={() => toPreviousStep()}
        initialValues={{ ...formValues }}
        isAuthenticated={isAuthenticated}
      />
    )
  };

  const forms = [
    <EmailForm
      handleEmailSubmit={data => handleStepSubmit(data)}
      onLogin={() => handleUpdateCart()}
    />,
    <div className='grid gap-6'>
      <div className='flex flex-col md:flex-row items-center gap-3 md:gap-6 mt-3 md:mt-0 md:mb-6'>
        {checkoutData!.paymentMethodsAccepted.map(payment => {
          const display = paymentDisplay[payment];
          return (
            <Button
              key={payment}
              color='primary'
              fullWidth
              onClick={() => handleChangePaymentMethod(payment)}
              variant={paymentMethod === payment ? 'fill' : 'outline'}
              size='large'
            >
              {display?.icon} {display?.label}{' '}
              {payment === PaymentMethodEnum.PIX &&
                checkoutData!.pricePromotional?.promotionalPercentual > 0 && (
                  <Badge size='small' type='success'>
                    {checkoutData!.price.currencySymbol}{' '}
                    {convertNumberToCurrency(
                      i18n.language,
                      currency.code,
                      checkoutData!.pricePromotional?.promotionalAmount
                    )}{' '}
                    OFF
                  </Badge>
                )}
              {payment === PaymentMethodEnum.CREDIT_CARD && (
                <Badge size='small' type='success'>
                  {t('payment.method.installments')}
                </Badge>
              )}
            </Button>
          );
        })}
      </div>
      {paymentForms[paymentMethod]}
    </div>,
    <GuestForm
      user={user}
      rooms={checkoutData!.items[0].offers}
      userAsFirstGuest={userAsFirstGuest}
      setUserAsFirstGuest={(value: boolean) => setUserAsFirstGuest(value)}
      handleGuestSubmit={data => handleStepSubmit(data)}
      paymentMethod={paymentMethod}
      toPreviousStep={() => toPreviousStep()}
      isAuthenticated={isAuthenticated}
      initialValues={formValues.room}
    />,
    <CheckoutResumeForm
      totalPrice={`${
        checkoutData!.price.currencySymbol
      } ${convertNumberToCurrency(i18n.language, currency.code, totalPrice!)}`}
      checkoutFields={formValues}
      roomDetails={checkoutData!.items[0].offers}
      paymentMethod={paymentMethod}
      loading={loading}
      onChangeTripcash={(value: boolean) => setIsTripcashApplied(value)}
      appliedTripcash={isTripcashApplied}
      handleSubmit={() => handleSubmit()}
      handleEditStep={stepToEdit =>
        handleStepSubmit(formValues, stepToEdit, CheckoutStepEnum.RESUME)
      }
      tripcashDiscount={
        checkoutData?.priceWithTripcash
          ? `${checkoutData?.priceWithTripcash?.currencySymbol} ${checkoutData?.priceWithTripcash?.summary?.adjustments?.[0]?.value}`
          : undefined
      }
      setInstallmentsValues={setInstallmentsValues}
      installments={
        isTripcashApplied
          ? checkoutData!.paymentInstallmentsWithTripcash
          : checkoutData!.paymentInstallments
      }
      selectedInstallment={selectedInstallment}
    />
  ];

  return (
    <>
      <div className='container'>
        <div className='flex md:hidden justify-between w-full py-6'>
          <Button color='white' size='icon'>
            <ArrowLeft size={18} onClick={() => toPreviousStep()} />
          </Button>
        </div>
        <StepFormHead
          step={step.current}
          username={`${user?.name.split(' ')[0]} ${
            user?.surname.split(' ')[0]
          }`}
          paymentMethod={isPaymentMethodSelected ? paymentMethod : undefined}
        />
        <div className='flex gap-9 mb-12'>
          <div className='min-h-[350px] flex-10'>{forms[step.current]}</div>
          <div className='hidden md:block flex-5'>
            <PurchaseDetails
              header={
                <CountdownCard
                  start={checkoutData!.secondsRemaining}
                  onCountdownEnd={() => setIsExpired(true)}
                />
              }
              resume={{
                hotelName: checkoutData?.hotel?.name,
                hotelAddress: checkoutData?.hotel?.address,
                hotelImage: checkoutData?.hotel?.photoCover?.url,
                hotelStars: parseFloat(checkoutData?.hotel?.stars || '0'),
                checkinDate: convertStringToFormatedDate(
                  getDateFNSLocale(i18n.language),
                  checkoutData!.checkin
                ),
                checkoutDate: convertStringToFormatedDate(
                  getDateFNSLocale(i18n.language),
                  checkoutData!.checkout
                ),
                distribution: getDistributionConfig(t, i18n, checkoutData!)
              }}
              tripcash={{
                ...tripcashInfo,
                currencySymbol: checkoutData!.price.currencySymbol
              }}
              pricing={{
                totalPrice: totalPrice!,
                userTotalPrice: checkoutData!.price.userFinalPrice,
                details: purchaseDescriptionItems,
                additionalTaxes,
                withDiscount:
                  paymentMethod === PaymentMethodEnum.PIX &&
                  !!checkoutData!.pricePromotional,
                position: 'top',
                currencySymbol: checkoutData!.price.currencySymbol,
                userCurrency: checkoutData!.price.userCurrency,
                userCurrencySymbol: checkoutData!.price.userCurrencySymbol,
                applyUserPrice: checkoutData!.price.applyUserPrice
              }}
            />
          </div>
        </div>
      </div>
      {isExpired && (
        <EmptyState
          fullscreen
          icon={<ClockCountdown size={42} weight='duotone' />}
          title={t('expired.title')}
          description={t('expired.description')}
          action={
            <Button onClick={() => router.push('/')}>
              {t('expired.button')}
            </Button>
          }
        />
      )}
      <div className='md:hidden fixed flex flex-col gap-2 bottom-0 left-0 w-[100vw] bg-white px-4 py-6 rounded-t-2xl z-10 shadow-2xl shadow-black/90'>
        <div className='flex justify-between items-end'>
          <div className='flex flex-col mb-1'>
            <p className='text-xl text-primary-900 font-bold leading-5'>
              {`${checkoutData!.price.currencySymbol} ${convertNumberToCurrency(
                i18n.language,
                currency.code,
                totalPrice!
              )}`}
            </p>
            {additionalTaxes.length === 0 && (
              <p className='text-sm text-gray-500 mb-2'>
                {t('purchasePricing.taxesAndFeesIncluded')}
              </p>
            )}
            <p className='text-sm text-primary-900'>
              {t('purchaseDetails.price.tripcash', {
                value: `${
                  checkoutData!.price.currencySymbol
                } ${convertNumberToCurrency(
                  i18n.language,
                  currency.code,
                  checkoutData!.price.tripcashInfo.percent * totalPrice!
                )}`
              })}
            </p>
          </div>
          <CountdownCard
            start={checkoutData!.secondsRemaining}
            onCountdownEnd={() => setIsExpired(true)}
            render={time => <p className='text-gray-500'>{time}</p>}
          />
        </div>
        <div className='flex gap-2'>
          <Button
            color='primary'
            variant='outline'
            className='w-full h-[50px]'
            onClick={() => setOverlayOpen(true)}
          >
            {t('details')}
            <ListBullets weight='bold' size={18} />
          </Button>
        </div>
        <Sheet
          open={overlayOpen}
          onOpenChange={(open: boolean) => setOverlayOpen(open)}
        >
          <SheetContent
            side='bottom'
            className='overflow-auto rounded-t-default max-h-[90vh]'
          >
            <PurchaseDetails
              resume={{
                hotelName: checkoutData?.hotel?.name,
                hotelAddress: checkoutData?.hotel?.address,
                hotelImage: checkoutData?.hotel?.photoCover?.url,
                hotelStars: parseFloat(checkoutData?.hotel?.stars || '0'),
                checkinDate: convertStringToFormatedDate(
                  getDateFNSLocale(i18n.language),
                  checkoutData?.checkin
                ),
                checkoutDate: convertStringToFormatedDate(
                  getDateFNSLocale(i18n.language),
                  checkoutData!.checkout
                ),
                distribution: getDistributionConfig(t, i18n, checkoutData!)
              }}
              tripcash={{
                ...tripcashInfo,
                currencySymbol: checkoutData!.price.currencySymbol
              }}
              pricing={{
                totalPrice: totalPrice!,
                details: purchaseDescriptionItems,
                additionalTaxes,
                withDiscount:
                  paymentMethod === PaymentMethodEnum.PIX &&
                  !!checkoutData!.pricePromotional,
                position: 'sticky',
                currencySymbol: checkoutData!.price.currencySymbol
              }}
            />
          </SheetContent>
        </Sheet>
      </div>
      <Sheet
        open={exitConfirmationOverlayOpen}
        onOpenChange={setExitConfirmationOverlayOpen}
      >
        <SheetContent side='bottom' className='rounded-t-default'>
          <div className='flex flex-col'>
            <h3 className='text-lg text-primary-900 font-semibold'>
              {t('exitConfirmation.title')}
            </h3>
            <p className='text-sm text-gray-500'>
              {t('exitConfirmation.description')}
            </p>
            <div className='flex gap-4 mt-4'>
              <Button
                color='primary'
                fullWidth
                onClick={() => setExitConfirmationOverlayOpen(false)}
              >
                {t('exitConfirmation.cancel')}
              </Button>
              <Button
                color='danger'
                onClick={() => router.back()}
                fullWidth
                variant='fill'
              >
                {t('exitConfirmation.exit')}
              </Button>
            </div>
          </div>
        </SheetContent>
      </Sheet>
      <FullLoading loading={loading}>
        <p>{t('loading')}</p>
      </FullLoading>
    </>
  );
};

const Checkout = ({ cart: cartData, host }: ICheckoutPageProps) => {
  const [cart, setCart] = useState<ICartResponse>(cartData);
  const { t } = useTranslation(['checkout', 'login', 'common', 'purchase'], {
    nsMode: 'fallback'
  });

  return (
    <Template
      title={t('title')}
      description={t('description')}
      navbarHiddenOnMobile
      extraTags={checkoutMetaTags}
      host={host}
      navbar={{
        fixed: false,
        backgroundColor: 'primary',
        foregroundColor: 'white',
        scrollingBackgroundColor: 'primary',
        scrollingForegroundColor: 'white'
      }}
    >
      <CheckoutProvider cart={cart}>
        <CheckoutPage setCart={setCart} />
      </CheckoutProvider>
    </Template>
  );
};

export const getServerSideProps: GetServerSideProps = async (
  ctx: GetServerSidePropsContext
) => {
  try {
    const id = ctx?.params?.id;
    const { data } = await getCart(id as string, ctx);

    return {
      props: {
        cart: data,
        ...(await serverSideTranslations(ctx.locale!))
      }
    };
  } catch (err) {
    console.log(err);
    Sentry.captureException(err);

    return {
      redirect: {
        permanent: false,
        destination: '/'
      },
      props: {}
    };
  }
};

export default Checkout;
