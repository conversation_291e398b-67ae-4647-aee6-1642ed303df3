/* eslint-disable react-hooks/exhaustive-deps */
import Template from '@components/templates/Template';
import HotelCardResult from '@components/molecules/HotelCardResult';
import Pagination from '@components/molecules/Pagination';
import FilterBox from '@components/organisms/FilterBox';
import { GetServerSideProps, GetServerSidePropsContext } from 'next';
import { useCallback, useEffect, useState } from 'react';
import { useSearch } from 'src/context/SearchContext';
import { ISearchPayload, SearchUrlParams } from 'src/types/search';
import { mountSearchUrlByPayload } from 'src/utils/search';
import { availability } from 'src/server/search';
import SearchSort from '@components/molecules/SearchSort';
import Skeleton from 'react-loading-skeleton';
import { HotelCardResultSkeleton } from '@components/molecules/Skeletons';
import AppliedFilterBox from '@components/organisms/AppliedFilterBox';
import EmptyState from '@components/molecules/EmptyState';
import { ArrowLeft, Funnel, ListMagnifyingGlass } from '@phosphor-icons/react';
import { useRouter } from 'next/router';
import Search from '@components/organisms/SearchBox';
import { searchMetaTags } from '@consts/meta/search';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useTranslation } from 'next-i18next';
import { useCurrency } from 'src/context/CurrencyContext';
import { Sheet, SheetContent, SheetTrigger } from '@components/atoms/Sheet';
import Button from '@components/atoms/Button';
import LanguageSwitch from '@components/molecules/LanguageSwitch';
import CurrencySwitch from '@components/molecules/CurrencySwitch';

interface IBuscaProps {
  host: string;
}

const Busca = ({ host }: IBuscaProps) => {
  const { t, i18n } = useTranslation(['search', 'common'], {
    nsMode: 'fallback'
  });
  const {
    loading,
    searchPayload,
    setSearchPayload,
    searchResponse,
    setSearchResponse
  } = useSearch();
  const router = useRouter();
  const { currency } = useCurrency();
  const [newSearchLoading, setNewSearchLoading] = useState(
    searchResponse.result.length === 0
  );

  const getChannel = () => {
    if (router.query.UTM_SOURCE) {
      return router.query.UTM_SOURCE.toString();
    }

    if (currency.code === 'EUR') return 'SITE_EUROPA';
    if (currency.code === 'USD') return 'SITE_AMERICA';
    if (currency.code === 'ARS') return 'SITE_ARGENTINA';
    if (currency.code === 'CLP') return 'SITE_CHILE';

    return 'SITE';
  };

  const fetchSearchResult = useCallback(
    async (payload: ISearchPayload, reload: boolean = false) => {
      try {
        if (searchResponse.result.length === 0 || reload) {
          setNewSearchLoading(true);

          const { data } = await availability(payload);
          setSearchPayload({
            ...payload,
            searchId: data.searchId,
            page: data.paging.page
          });
          setSearchResponse(data);
        }
      } catch (err) {
        //
      } finally {
        setNewSearchLoading(false);
      }
    },
    [setSearchPayload, setSearchResponse]
  );

  const fetchResult = async (reload: boolean = false) => {
    await fetchSearchResult(
      {
        ...searchPayload,
        channel: router.query.utm_source?.toString() || getChannel(),
        currency: currency.code,
        lang: i18n.language.toUpperCase(),
        searchId: ''
      },
      reload
    );
  };

  const {
    paging: { total, limit }
  } = searchResponse;

  useEffect(() => {
    fetchResult(
      i18n.language.toUpperCase() !== searchPayload.lang?.toUpperCase() ||
        currency.code !== searchPayload.currency
    );
  }, [currency, i18n.language]);

  const handlePageChange = async (page: number) => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
    await fetchSearchResult({ ...searchPayload, page });
  };

  const handleNewSearch = async (payload: ISearchPayload) => {
    const [url] = mountSearchUrlByPayload(payload);
    router.push(
      {
        pathname: t('routes.search'),
        query:
          url +
          (router.query.utm_source
            ? `&${new URLSearchParams(router.query as any).toString()}`
            : '')
      },
      '',
      {
        locale: i18n.language
      }
    );

    await fetchSearchResult({ ...payload, searchId: '' }, true);
  };

  return (
    <Template
      title={t('title', {
        destination: searchPayload.destination.display
      })}
      description={t('description')}
      navbarHiddenOnMobile
      mobileBottomNavigationBar
      extraTags={searchMetaTags}
      host={host}
      navbar={{
        fixed: false,
        backgroundColor: 'white',
        foregroundColor: 'primary',
        scrollingBackgroundColor: 'white',
        scrollingForegroundColor: 'primary'
      }}
      preContent={
        <div className='relative w-full h-[100px] flex justify-center items-center bg-gray-100 mb-2 md:mb-20'>
          <div className="w-full h-[150px] bg-[url('/images/bg_2.png')] bg-[center_75%] bg-no-repeat bg-cover absolute contrast-[1.2] brightness-[0.8] top-0 md:block md:opacity-100 hidden opacity-0" />
          <div className='container flex flex-col mt-6 md:mt-52 gap-4'>
            <div className='flex md:hidden justify-between w-full'>
              <Button color='white' size='icon'>
                <ArrowLeft size={18} onClick={() => router.push('/')} />
              </Button>
              <div className='flex gap-2'>
                <div className='flex items-center bg-white pl-3 pr-3 opacity-90 rounded-button'>
                  <LanguageSwitch color='primary' />
                </div>
                <div className='flex items-center bg-white pl-3 pr-3 opacity-90 rounded-div'>
                  <CurrencySwitch color='primary' />
                </div>
              </div>
            </div>
            <div className='flex gap-3'>
              <Search searchFunction={payload => handleNewSearch(payload)} />
              <Sheet>
                <SheetTrigger className='flex md:hidden'>
                  <div className='flex items-center gap-2 p-3 bg-white rounded-default'>
                    <Funnel size={24} />
                  </div>
                </SheetTrigger>
                <SheetContent
                  side='bottom'
                  className='max-h-[75vh] overflow-auto rounded-t-2xl'
                  onOpenAutoFocus={e => e.preventDefault()}
                >
                  <FilterBox pageLoading={newSearchLoading} />
                </SheetContent>
              </Sheet>
            </div>
          </div>
        </div>
      }
    >
      <div className='container'>
        <div className='flex gap-8 pt-6 md:pt-12 pb-6'>
          <div className='hidden flex-none w-80 md:flex'>
            <FilterBox pageLoading={newSearchLoading} />
          </div>
          <div className='w-full flex flex-2 flex-col gap-3 md:gap-6'>
            {!newSearchLoading && searchResponse?.result?.length > 0 && (
              <div className='w-full'>
                <div className='flex justify-between items-end'>
                  <div className='flex flex-col items-start'>
                    <p className='text-xs md:text-sm text-gray-500'>
                      {t('destination')}
                    </p>
                    <h3 className='text-sm md:text-base font-medium text-primary-900'>
                      {searchPayload.destination.display}
                    </h3>
                  </div>
                  <SearchSort
                    onOptionSelect={paylaod => fetchSearchResult(paylaod)}
                    sorting={searchResponse.sorting}
                  />
                </div>
                <AppliedFilterBox />
              </div>
            )}

            {newSearchLoading ? (
              <Skeleton count={20} wrapper={HotelCardResultSkeleton} />
            ) : (
              <div className='w-full flex flex-col gap-3'>
                {searchResponse?.result?.length > 0 ? (
                  searchResponse?.result?.map(hotel => (
                    <div
                      key={hotel.accomodation.name}
                      className='w-full flex flex-col gap-6'
                    >
                      <HotelCardResult {...hotel} />
                    </div>
                  ))
                ) : (
                  <EmptyState
                    icon={<ListMagnifyingGlass size={50} />}
                    title={t('noHotelsFoundTitle')}
                    description={t('noHotelsFoundDescription')}
                  />
                )}
              </div>
            )}

            {total / limit > 1 ? (
              <Pagination
                loading={loading}
                total={Math.ceil(total / limit)}
                page={searchPayload.page + 1}
                onChange={newPage => handlePageChange(newPage - 1)}
              />
            ) : null}
          </div>
        </div>
      </div>
    </Template>
  );
};

export const getServerSideProps: GetServerSideProps = async (
  ctx: GetServerSidePropsContext
) => {
  const params = Object(ctx?.query) as SearchUrlParams;

  if (JSON.stringify(params) === '{}' || !params.latlng) {
    return {
      redirect: {
        destination: '/'
      },
      props: {}
    };
  }

  return {
    props: {
      ...(await serverSideTranslations(ctx.locale!))
    }
  };
};

export default Busca;
