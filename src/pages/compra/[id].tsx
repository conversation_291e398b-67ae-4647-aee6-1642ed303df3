import Template from '@components/templates/Template';
import { GetServerSideProps, GetServerSidePropsContext } from 'next/types';
import { getOrder } from 'src/server/order';
import { IGuestOrderResponse, PriceValues } from 'src/types/buy';
import PurchasePricing from '@components/molecules/PurchasePricing';
import { convertNumberToCurrency } from 'src/utils/currency';
import Divider from '@components/atoms/Divider';
import { createPurchaseItemsFromPriceValues } from 'src/utils/buy';
import PixPaymentCard from '@components/molecules/PixPaymentCard';
import ReservationCard from '@components/organisms/ReservationCard';
import { convertStringToFormatedDate, getDateFNSLocale } from 'src/utils/date';
import HeaderStatusInfo from '@components/molecules/HeaderStatusInfo';
import CashbackCard from '@components/molecules/CashbackCard';
import Button from '@components/atoms/Button';
import { useRouter } from 'next/router';
import {
  ArrowSquareOut,
  ArrowUpRight,
  CheckCircle,
  ClockCountdown,
  CreditCard,
  Download,
  House,
  ListBullets,
  Question,
  X
} from '@phosphor-icons/react';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useTranslation } from 'next-i18next';
import { PaymentMethodEnum } from 'src/types/checkout';
import { useCurrency } from 'src/context/CurrencyContext';
import ExternalPaymentCard from '@components/molecules/ExternalPaymentCard';
import { useState } from 'react';
import { Sheet, SheetContent } from '@components/atoms/Sheet';

interface IPageProps {
  orderData: IGuestOrderResponse;
  host: string;
}

export default function Compra({ orderData, host }: IPageProps) {
  const router = useRouter();
  const { currency } = useCurrency();
  const { t, i18n } = useTranslation(['purchase', 'common', 'bookings'], {
    nsMode: 'fallback'
  });

  const [isExternalPaymentOpen, setIsExternalPaymentOpen] =
    useState<boolean>(false);
  const [overlayOpen, setOverlayOpen] = useState<boolean>(false);
  const [isTimedOut, setIsTimedOut] = useState<boolean>(false);

  const adjustmentType = {
    TRIPCASH: t('tripcash'),
    DISCOUNT_PIX: t('discountPix')
  };

  const bookingInfo = {
    [PaymentMethodEnum.PIX]: {
      CONFIRMED: {
        title: t('pix.confirmed.title'),
        subtitle: t('pix.confirmed.subtitle'),
        icon: <CheckCircle size={25} weight='duotone' />,
        messageType: 'success'
      },
      PENDING_PAYMENT: {
        title: t('pix.pendingPayment.title'),
        subtitle: t('pix.pendingPayment.subtitle'),
        icon: <ClockCountdown size={25} weight='duotone' />,
        messageType: 'info'
      },
      PENDING_ANTIFRAUD: {
        title: t('pix.pendingAntifraud.title'),
        subtitle: t('pix.pendingAntifraud.subtitle'),
        icon: <ClockCountdown size={25} weight='duotone' />,
        messageType: 'info'
      },
      PENDING_CONFIRMATION: {
        title: t('pix.pendingConfirmation.title'),
        subtitle: t('pix.pendingConfirmation.subtitle'),
        icon: <ClockCountdown size={25} weight='duotone' />,
        messageType: 'info'
      },
      PAYMENT_REJECTED: {
        title: t('pix.paymentRejected.title'),
        subtitle: t('pix.paymentRejected.subtitle'),
        icon: <X size={25} weight='bold' />,
        messageType: 'danger'
      },
      CANCELED: {
        title: t('pix.canceled.title'),
        subtitle: t('pix.canceled.subtitle'),
        icon: <X size={25} weight='bold' />,
        messageType: 'danger'
      }
    },
    [PaymentMethodEnum.CREDIT_CARD]: {
      CONFIRMED: {
        title: t('creditCard.confirmed.title'),
        subtitle: t('creditCard.confirmed.subtitle'),
        icon: <CheckCircle size={25} weight='duotone' />,
        messageType: 'success'
      },
      PENDING_PAYMENT: {
        title: t('creditCard.pendingPayment.title'),
        subtitle: t('creditCard.pendingPayment.subtitle'),
        icon: <ClockCountdown size={25} weight='duotone' />,
        messageType: 'info'
      },
      PENDING_ANTIFRAUD: {
        title: t('creditCard.pendingAntifraud.title'),
        subtitle: t('creditCard.pendingAntifraud.subtitle'),
        icon: <ClockCountdown size={25} weight='duotone' />,
        messageType: 'info'
      },
      PENDING_CONFIRMATION: {
        title: t('creditCard.pendingConfirmation.title'),
        subtitle: t('creditCard.pendingConfirmation.subtitle'),
        icon: <ClockCountdown size={25} weight='duotone' />,
        messageType: 'info'
      },
      PAYMENT_REJECTED: {
        title: t('creditCard.paymentRejected.title'),
        subtitle: t('creditCard.paymentRejected.subtitle'),
        icon: <X size={25} weight='bold' />,
        messageType: 'danger'
      },
      CANCELED: {
        title: t('creditCard.canceled.title'),
        subtitle: t('creditCard.canceled.subtitle'),
        icon: <X size={25} weight='bold' />,
        messageType: 'danger'
      }
    },
    TRIPCASH: {
      CONFIRMED: {
        title: t('tripcashPayment.confirmed.title'),
        subtitle: t('tripcashPayment.confirmed.subtitle'),
        icon: <CheckCircle size={25} weight='duotone' />,
        messageType: 'success'
      },
      PENDING_PAYMENT: {
        title: t('tripcashPayment.pendingPayment.title'),
        subtitle: t('tripcashPayment.pendingPayment.subtitle'),
        icon: <ClockCountdown size={25} weight='duotone' />,
        messageType: 'info'
      },
      PENDING_CONFIRMATION: {
        title: t('tripcashPayment.pendingConfirmation.title'),
        subtitle: t('tripcashPayment.pendingConfirmation.subtitle'),
        icon: <ClockCountdown size={25} weight='duotone' />,
        messageType: 'info'
      },
      PENDING_ANTIFRAUD: {
        title: t('tripcashPayment.pendingAntifraud.title'),
        subtitle: t('tripcashPayment.pendingAntifraud.subtitle'),
        icon: <ClockCountdown size={25} weight='duotone' />,
        messageType: 'info'
      },
      PAYMENT_REJECTED: {
        title: t('tripcashPayment.paymentRejected.title'),
        subtitle: t('tripcashPayment.paymentRejected.subtitle'),
        icon: <X size={25} weight='bold' />,
        messageType: 'danger'
      },
      CANCELED: {
        title: t('tripcashPayment.canceled.title'),
        subtitle: t('tripcashPayment.canceled.subtitle'),
        icon: <X size={25} weight='bold' />,
        messageType: 'danger'
      }
    },
    [PaymentMethodEnum.EXTERNAL]: {
      CONFIRMED: {
        title: t('external.confirmed.title'),
        subtitle: t('external.confirmed.subtitle'),
        icon: <CheckCircle size={25} weight='duotone' />,
        messageType: 'success'
      },
      PENDING_PAYMENT: {
        title: t('external.pendingPayment.title'),
        subtitle: t('external.pendingPayment.subtitle'),
        icon: <ClockCountdown size={25} weight='duotone' />,
        messageType: 'info'
      },
      PENDING_ANTIFRAUD: {
        title: t('external.pendingAntifraud.title'),
        subtitle: t('external.pendingAntifraud.subtitle'),
        icon: <ClockCountdown size={25} weight='duotone' />,
        messageType: 'info'
      },
      PENDING_CONFIRMATION: {
        title: t('external.pendingConfirmation.title'),
        subtitle: t('external.pendingConfirmation.subtitle'),
        icon: <ClockCountdown size={25} weight='duotone' />,
        messageType: 'info'
      },
      PAYMENT_REJECTED: {
        title: t('external.paymentRejected.title'),
        subtitle: t('external.paymentRejected.subtitle'),
        icon: <X size={25} weight='bold' />,
        messageType: 'danger'
      },
      PAYMENT_EXPIRED: {
        title: t('external.paymentExpired.title'),
        subtitle: t('external.paymentExpired.subtitle'),
        icon: <ClockCountdown size={25} weight='bold' />,
        messageType: 'danger'
      },
      CANCELED: {
        title: t('external.canceled.title'),
        subtitle: t('external.canceled.subtitle'),
        icon: <X size={25} weight='bold' />,
        messageType: 'danger'
      }
    }
  };

  const info = bookingInfo?.[orderData?.paymentMethod]?.[orderData?.status];

  const getPriceItems = () => {
    const { installments, installmentAmount } = orderData.price;
    const { currencySymbol } = orderData.itens[0].booking.price;

    const priceValues: PriceValues[] = [
      { ...orderData.price.price, currency: currencySymbol },
      { ...orderData.price.taxValue, currency: currencySymbol }
    ];

    if (installments > 0 && orderData.paymentMethod === 'CREDIT_CARD') {
      priceValues.push({
        formattedValue: t('orderCardPayment.creditCard.description', {
          installments,
          amount: `${currencySymbol} ${installmentAmount.formattedValue}`
        }),
        description: t('installments')
      });
    }

    if (orderData?.adjustments) {
      orderData.adjustments.forEach(adjustment =>
        priceValues.push({
          formattedValue: `${convertNumberToCurrency(
            i18n.language,
            currency.code,
            adjustment.value
          )}`,
          description: t(adjustmentType![adjustment.type]) || t('discount'),
          value: adjustment.value,
          currency: currencySymbol,
          discount: true
        })
      );
    }

    return createPurchaseItemsFromPriceValues(
      i18n.language,
      currency.code,
      priceValues
    );
  };

  const formattedValue = `${
    orderData!.price.currencySymbol
  } ${convertNumberToCurrency(
    i18n.language,
    currency.code,
    orderData?.price?.finalPrice?.value ?? 0
  )}`;
  const tripcashFormattedValue = `${
    orderData!.price.currencySymbol
  } ${convertNumberToCurrency(
    i18n.language,
    currency.code,
    orderData.tripcashInfo?.value
  )}`;

  return (
    <Template
      title={t('title')}
      description={t('description')}
      navbarHiddenOnMobile
      host={host}
      navbar={{
        fixed: false,
        backgroundColor: 'primary',
        foregroundColor: 'white',
        scrollingBackgroundColor: 'primary',
        scrollingForegroundColor: 'white'
      }}
    >
      <div className='container'>
        <div className='flex md:hidden justify-between w-full pt-6'>
          <Button color='white' size='icon' onClick={() => router.push('/')}>
            <House size={18} />
          </Button>
          <div className='flex gap-2'>
            <Button
              color='white'
              size='icon'
              onClick={() => router.push(t('routes.contact'))}
            >
              <Question size={18} />
            </Button>
            {orderData.status === 'CONFIRMED' && (
              <Button
                color='white'
                size='icon'
                onClick={() =>
                  window.open(
                    `/${i18n.language}/voucher/${orderData.id}`,
                    '_blank'
                  )
                }
              >
                <Download size={18} />
              </Button>
            )}
          </div>
        </div>
        <div className='flex gap-6 flex-col md:flex-row pt-6'>
          <div className='flex-10 flex flex-col gap-6 rounded-default bg-white p-6'>
            <HeaderStatusInfo info={info} />
            {orderData.paymentMethod === 'PIX' ? (
              <>
                <PixPaymentCard
                  base64={orderData?.paymentResponse?.qrCodeBase64 ?? ''}
                  qrcodeLink={orderData?.paymentResponse?.qrCode ?? ''}
                  price={`${
                    orderData.price.currencySymbol
                  } ${convertNumberToCurrency(
                    i18n.language,
                    currency.code,
                    orderData?.price?.finalPrice?.value ?? 0
                  )}`}
                  priceDescription={
                    orderData?.price?.finalPrice?.description ?? ''
                  }
                  secondsRemaining={
                    orderData?.paymentResponse?.secondsRemaining ?? 0
                  }
                />
                <Divider orientation='horizontal' />
              </>
            ) : null}
            {orderData.paymentMethod === PaymentMethodEnum.EXTERNAL ? (
              <>
                <ExternalPaymentCard
                  price={formattedValue}
                  priceDescription={
                    orderData?.price?.finalPrice?.description ?? ''
                  }
                  secondsRemaining={
                    orderData?.paymentResponse?.secondsRemaining ?? 0
                  }
                  iframeUrl={orderData.paymentResponse.iframe}
                  isPaymentOpen={isExternalPaymentOpen}
                  setPaymentOpen={setIsExternalPaymentOpen}
                  onTimeout={() => setIsTimedOut(true)}
                />
                <Divider orientation='horizontal' />
              </>
            ) : null}
            <div className='flex flex-col gap-2'>
              {orderData.itens.map(booking => (
                <ReservationCard
                  border={false}
                  key={booking.id}
                  images={[booking.booking.hotel.photoCover.url!]}
                  stars={parseFloat(booking.booking.hotel.stars)}
                  name={booking.booking.hotel.name}
                  address={booking.booking.hotel.address}
                  checkin={convertStringToFormatedDate(
                    getDateFNSLocale(i18n.language),
                    booking.booking.checkin
                  )}
                  checkout={convertStringToFormatedDate(
                    getDateFNSLocale(i18n.language),
                    booking.booking.checkout
                  )}
                  rooms={booking.booking.rooms}
                />
              ))}
            </div>
          </div>
          <div className='flex-5 hidden md:flex flex-col gap-6'>
            <div className='sticky flex flex-col gap-6 top-6'>
              {orderData.tripcashInfo ? (
                <CashbackCard value={tripcashFormattedValue} />
              ) : null}
              <PurchasePricing
                withDiscount={false}
                totalPrice={formattedValue}
                items={getPriceItems()}
                additionalTaxes={[]}
              />
              <div className='flex flex-col gap-2'>
                <Button
                  color='secondary'
                  onClick={() =>
                    router.push({ pathname: t('routes.myReservations') }, '', {
                      locale: i18n.language
                    })
                  }
                >
                  {t('goToMyReservations')}
                  <ArrowUpRight size={20} />
                </Button>
                {orderData.status === 'CONFIRMED' && (
                  <Button
                    color='primary'
                    onClick={() =>
                      window.open(
                        `/${i18n.language}/voucher/${orderData.id}`,
                        '_blank'
                      )
                    }
                  >
                    {t('downloadPdf')}
                    <Download size={20} />
                  </Button>
                )}
              </div>
            </div>
          </div>
          <div className='md:hidden fixed flex flex-col gap-2 bottom-0 left-0 w-[100vw] bg-white px-4 py-6 rounded-t-2xl z-10 shadow-2xl shadow-black/90'>
            <div className='flex justify-between items-end'>
              <div className='flex flex-col mb-1'>
                <p className='text-lg text-primary-900 font-bold leading-5'>
                  {formattedValue}
                </p>
                <p className='text-sm text-primary-900'>
                  {t('purchaseDetails.price.tripcash', {
                    value: tripcashFormattedValue
                  })}
                </p>
              </div>
            </div>
            <div className='flex flex-col gap-2'>
              <div className='flex gap-2'>
                <Button
                  color='primary'
                  variant='outline'
                  className='w-full'
                  onClick={() => setOverlayOpen(true)}
                >
                  {t('details')}
                  <ListBullets size={22} />
                </Button>
                <Button
                  color='primary'
                  variant='outline'
                  className={`w-full ${
                    orderData.paymentMethod === PaymentMethodEnum.EXTERNAL &&
                    !isTimedOut
                      ? ''
                      : 'hidden'
                  }`}
                  onClick={() => setIsExternalPaymentOpen(true)}
                >
                  {t('externalPaymentCard.buttonText')}
                  <CreditCard size={22} />
                </Button>
              </div>
              <Button
                fullWidth
                color='primary'
                className='w-full'
                onClick={() => router.push(t('routes.myReservations'))}
              >
                {t('goToMyReservations')}
                <ArrowSquareOut size={22} />
              </Button>
            </div>
            <Sheet
              open={overlayOpen}
              onOpenChange={(open: boolean) => setOverlayOpen(open)}
            >
              <SheetContent
                side='bottom'
                className='overflow-auto rounded-t-default max-h-[90vh]'
              >
                <PurchasePricing
                  withDiscount={false}
                  currencySymbol={orderData.price.currencySymbol}
                  totalPrice={convertNumberToCurrency(
                    i18n.language,
                    currency.code,
                    orderData?.price?.finalPrice?.value ?? 0
                  )}
                  items={getPriceItems()}
                  additionalTaxes={[]}
                />
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </Template>
  );
}

export const getServerSideProps: GetServerSideProps = async (
  ctx: GetServerSidePropsContext
) => {
  const id = ctx?.params?.id;
  try {
    const { data: orderData } = await getOrder(id as string, ctx);

    return {
      props: {
        orderData,
        ...(await serverSideTranslations(ctx.locale!))
      }
    };
  } catch (err) {
    return {
      redirect: {
        permanent: false,
        destination: '/404'
      },
      props: {}
    };
  }
};
