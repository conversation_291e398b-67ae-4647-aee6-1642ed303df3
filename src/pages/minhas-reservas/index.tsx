/* eslint-disable react-hooks/exhaustive-deps */
import { useCallback, useEffect, useState, useMemo } from 'react';
import EmptyState from '@components/molecules/EmptyState';
import { Bed } from '@phosphor-icons/react';
import { GetServerSideProps, GetServerSidePropsContext } from 'next';
import { useRouter } from 'next/router';
import AccountTemplate from '@components/templates/AccountTemplate';
import Pagination from '@components/molecules/Pagination';
import Button from '@components/atoms/Button';
import { IOrder, IOrderResponse } from 'src/types/order';
import OrderCard from '@components/organisms/OrderCard';
import { toast } from 'react-toastify';
import { getOrders } from 'src/server/order';
import OrderCardSkeleton from '@components/molecules/Skeletons/OrderCard';
import Skeleton from 'react-loading-skeleton';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { i18n, useTranslation } from 'next-i18next';

interface IBookingsProps {
  host: string;
}

const Bookings = ({ host }: IBookingsProps) => {
  const { t } = useTranslation('bookings');
  const [loading, setLoading] = useState<boolean>(true);
  const [currentOrders, setCurrentOrders] = useState<IOrderResponse>(
    {} as IOrderResponse
  );
  const router = useRouter();

  const hasOrders = useMemo(
    () => Boolean(currentOrders?.content?.length),
    [currentOrders?.content?.length]
  );

  const currentPage = useMemo(
    () => currentOrders?.pageable?.pageNumber ?? 0,
    [currentOrders?.pageable?.pageNumber]
  );

  const showPagination = useMemo(
    () => (currentOrders?.totalPages ?? 0) > 1,
    [currentOrders?.totalPages]
  );

  const updateOrder = useCallback((orderId: string, order?: IOrder) => {
    if (!order) return;

    setCurrentOrders(prev => {
      const orderIndex = prev?.content?.findIndex(
        orderValue => orderValue.id === orderId
      );

      if (orderIndex === -1 || orderIndex === undefined) return prev;

      const updatedOrders = [...prev.content];
      updatedOrders[orderIndex] = order;

      return {
        ...prev,
        content: updatedOrders
      };
    });
  }, []);

  const fetchOrders = useCallback(
    async (
      page: number,
      size: number,
      updatedOrderId: string | null = null,
      skipScroll: boolean = false
    ) => {
      if (!currentOrders?.content?.length && !updatedOrderId) {
        setLoading(true);
      }

      try {
        const { data } = await getOrders(page, size);

        if (updatedOrderId) {
          const updatedOrder = data.content.find(
            order => order.id === updatedOrderId
          );
          updateOrder(updatedOrderId, updatedOrder);
        } else {
          setCurrentOrders(data);
        }
      } catch (err) {
        toast(t('reservations.toastError'), {
          type: 'error'
        });
      } finally {
        if (!skipScroll) {
          setTimeout(
            () => window.scrollTo({ top: 0, behavior: 'smooth' }),
            250
          );
        }
        setLoading(false);
      }
    },
    [t, updateOrder, currentOrders?.content?.length]
  );

  const handlePageChange = useCallback(
    (page: number) => {
      if (page > 0 && page <= (currentOrders?.totalPages || 0)) {
        fetchOrders(page - 1, 5);
      }
    },
    [fetchOrders, currentOrders?.totalPages]
  );

  const onOrderCancel = useCallback(
    (orderId: string) => fetchOrders(currentPage, 5, orderId, true),
    [fetchOrders, currentPage]
  );

  useEffect(() => {
    fetchOrders(0, 5, null, true);
  }, [fetchOrders]);

  const renderContent = () => {
    if (loading) {
      return <Skeleton count={2} wrapper={OrderCardSkeleton} />;
    }

    if (!hasOrders) {
      return (
        <EmptyState
          icon={<Bed size={40} weight='duotone' />}
          title={t('reservations.emptyState.title')}
          description={t('reservations.emptyState.description')}
          action={
            <Button color='primary' onClick={() => router.push('/')}>
              {t('reservations.emptyState.actionButton')}
            </Button>
          }
        />
      );
    }

    return currentOrders.content.map(order => (
      <OrderCard key={order.id} {...order} onCancel={onOrderCancel} />
    ));
  };

  return (
    <AccountTemplate
      title={t('title')}
      description={t('description')}
      host={host}
      navbar={{
        fixed: false,
        backgroundColor: 'white',
        foregroundColor: 'primary',
        scrollingBackgroundColor: 'white',
        scrollingForegroundColor: 'primary'
      }}
    >
      <div className='flex flex-col gap-6 mb-6'>
        {renderContent()}
        {showPagination && (
          <Pagination
            total={currentOrders.totalPages}
            page={currentPage + 1}
            onChange={handlePageChange}
          />
        )}
      </div>
    </AccountTemplate>
  );
};

export const getServerSideProps: GetServerSideProps = async (
  ctx: GetServerSidePropsContext
) => {
  const { token } = ctx.req.cookies;

  if (!token) {
    const destination =
      ctx.locale === ctx.defaultLocale
        ? `${i18n!.t('routes.signIn')}?referrer=${i18n!.t(
            'routes.myReservations'
          )}`
        : `/${ctx.locale}${i18n!.t('routes.signIn')}?referrer=${i18n!.t(
            'routes.myReservations'
          )}`;

    return {
      redirect: {
        destination,
        permanent: false
      }
    };
  }

  return {
    props: {
      ...(await serverSideTranslations(ctx.locale!))
    }
  };
};

export default Bookings;
