import { GetServerSideProps, GetServerSidePropsContext } from 'next';
import Menu, { MenuItem } from '@components/molecules/Menu';
import Template from '@components/templates/Template';
import { useRouter } from 'next/router';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useTranslation } from 'next-i18next';

interface IPrivacyPolicyProps {
  host: string;
}

const PrivacyPolicy = ({ host }: IPrivacyPolicyProps) => {
  const { t, i18n } = useTranslation(['privacy', 'common'], {
    nsMode: 'fallback'
  });
  const router = useRouter();
  const links: MenuItem[] = [
    {
      text: t('links.terms'),
      onClick: () =>
        router.push({ pathname: t('routes.termsAndConditions') }, '', {
          locale: i18n.language
        })
    },
    {
      text: t('links.privacy'),
      onClick: () =>
        router.push({ pathname: t('routes.privacyPolicy') }, '', {
          locale: i18n.language
        })
    },
    {
      text: t('links.security'),
      onClick: () =>
        router.push({ pathname: t('routes.security') }, '', {
          locale: i18n.language
        })
    }
  ];

  return (
    <Template
      title={t('title')}
      description={t('description')}
      leftContent={<Menu items={links} />}
      host={host}
      navbar={{
        fixed: false,
        backgroundColor: 'white',
        foregroundColor: 'primary',
        scrollingBackgroundColor: 'white',
        scrollingForegroundColor: 'primary'
      }}
    >
      <div className='w-full flex items-start gap-6'>
        <div className='w-full flex flex-col flex-4 text-primary-900'>
          <h1 className='text-2xl font-semibold mb-4'>{t('heading')}</h1>

          <h2 className='text-lg font-medium mb-2.5'>
            {t('sections.rights.title')}
          </h2>
          <p className='text-base mb-6 leading-8'>
            {t('sections.rights.intro')}
          </p>
          <p className='text-base mb-6 leading-8'>
            {t('sections.rights.list.0')}
            <br />
            {t('sections.rights.list.1')}
            <br />
            {t('sections.rights.list.2')}
            <br />
            {t('sections.rights.list.3')}
            <br />
            {t('sections.rights.list.4')}
            <br />
            {t('sections.rights.list.5')}
            <br />
            {t('sections.rights.list.6')}
            <br />
            {t('sections.rights.list.7')}
          </p>

          <h2 className='text-lg font-medium mb-2.5'>
            {t('sections.unsubscribe.title')}
          </h2>
          <p className='text-base mb-6 leading-8'>
            {t('sections.unsubscribe.description')}
          </p>

          <h2 className='text-lg font-medium mb-2.5'>
            {t('sections.unsubscribe.howTo.title')}
          </h2>
          <p className='text-base mb-6 leading-8'>
            {t('sections.unsubscribe.howTo.methods.0')}
            <br />
            {t('sections.unsubscribe.howTo.methods.1')}
            <br />
            {t('sections.unsubscribe.howTo.methods.2')}
          </p>

          <h2 className='text-lg font-medium mb-2.5'>
            {t('sections.importantToKnow.title')}
          </h2>
          <p className='text-base mb-6 leading-8'>
            {t('sections.importantToKnow.content.0')}
            <br />
            {t('sections.importantToKnow.content.1')}
          </p>

          <h2 className='text-lg font-medium mb-2.5'>
            {t('sections.personalData.title')}
          </h2>
          <p className='text-base mb-6 leading-8'>
            {t('sections.personalData.description')}
          </p>

          <h2 className='text-lg font-medium mb-2.5'>
            {t('sections.personalData.requestCopy.title')}
          </h2>
          <p className='text-base mb-6 leading-8'>
            {t('sections.personalData.requestCopy.content')}
          </p>

          <h2 className='text-lg font-medium mb-2.5'>
            {t('sections.personalData.requestDeletion.title')}
          </h2>
          <p className='text-base mb-6 leading-8'>
            {t('sections.personalData.requestDeletion.noAccount')}
          </p>
          <p className='text-base mb-6 leading-8'>
            {t('sections.personalData.requestDeletion.hasAccount')}
          </p>

          <h2 className='text-lg font-medium mb-2.5'>
            {t('sections.accountUpdate.title')}
          </h2>
          <p className='text-base mb-6 leading-8'>
            {t('sections.accountUpdate.description')}
          </p>

          <h2 className='text-lg font-medium mb-2.5'>
            {t('sections.accountUpdate.howTo.title')}
          </h2>
          <p className='text-base mb-6 leading-8'>
            {t('sections.accountUpdate.howTo.steps')}
          </p>
          <p className='text-base mb-6 leading-8'>
            {t('sections.accountUpdate.needHelp.question')}
          </p>
          <p className='text-base mb-6 leading-8'>
            {t('sections.accountUpdate.needHelp.content')}
          </p>

          <h2 className='text-lg font-medium mb-2.5'>
            {t('sections.deleteAccount.title')}
          </h2>
          <p className='text-base mb-6 leading-8'>
            {t('sections.deleteAccount.sorry')}
          </p>
          <p className='text-base mb-6 leading-8'>
            {t('sections.deleteAccount.warning.0')}
            <br />
            {t('sections.deleteAccount.warning.1')}
          </p>

          <h2 className='text-lg font-medium mb-2.5'>
            {t('sections.otherConcerns.title')}
          </h2>
          <p className='text-base mb-6 leading-8'>
            {t('sections.otherConcerns.content.0')}
          </p>
          <p className='text-base mb-6 leading-8'>
            {t('sections.otherConcerns.content.1')}
          </p>

          <h2 className='text-lg font-medium mb-2.5'>
            {t('sections.privacyPolicyIncludes.title')}
          </h2>
          <p className='text-base mb-6 leading-8'>
            {t('sections.privacyPolicyIncludes.content.0')}
            <br />
            {t('sections.privacyPolicyIncludes.content.1')}
            <br />
            {t('sections.privacyPolicyIncludes.content.2')}
            <br />
            {t('sections.privacyPolicyIncludes.content.3')}
          </p>

          <h2 className='text-lg font-medium mb-2.5'>
            {t('sections.privacyInformation.title')}
          </h2>
          <p className='text-base mb-6 leading-8'>
            {t('sections.privacyInformation.content.0')}
          </p>
          <p className='text-base mb-6 leading-8'>
            {t('sections.privacyInformation.content.1')}
          </p>
          <p className='text-base mb-6 leading-8'>
            {t('sections.privacyInformation.content.2')}
            <br />
            {t('sections.privacyInformation.content.3')}
            <br />
            {t('sections.privacyInformation.content.4')}
            <br />
            {t('sections.privacyInformation.content.5')}
            <br />
            {t('sections.privacyInformation.content.6')}
          </p>

          <p className='text-base mb-6 leading-8'>
            {t('sections.cookiePolicy.content.0')}
          </p>
          <p className='text-base mb-6 leading-8'>
            {t('sections.cookiePolicy.content.1')}
            <br />
            {t('sections.cookiePolicy.content.2')}
            <br />
            {t('sections.cookiePolicy.content.3')}
            <br />
            {t('sections.cookiePolicy.content.4')}
            <br />
            {t('sections.cookiePolicy.content.5')}
          </p>

          <p className='text-base mb-6 leading-8'>
            {t('sections.contactUs.content')}
          </p>
        </div>
      </div>
    </Template>
  );
};

export const getServerSideProps: GetServerSideProps = async (
  ctx: GetServerSidePropsContext
) => {
  return {
    props: {
      ...(await serverSideTranslations(ctx.locale!))
    }
  };
};

export default PrivacyPolicy;
