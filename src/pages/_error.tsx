import * as Sentry from '@sentry/nextjs';
import Error from 'next/error';

const CustomErrorComponent = (props: any) => {
  const { statusCode } = props;

  return <Error statusCode={statusCode} />;
};

CustomErrorComponent.getInitialProps = async (contextData: any) => {
  await Sentry.captureUnderscoreErrorException(contextData);

  return Error.getInitialProps(contextData);
};

export default CustomErrorComponent;
