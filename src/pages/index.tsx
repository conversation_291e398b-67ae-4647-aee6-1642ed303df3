import { GetServerSideProps, GetServerSidePropsContext } from 'next';
import Template from '@components/templates/Template';
import Header from '@components/organisms/Header';
import Showcase from '@components/molecules/Showcase';
import CityCard from '@components/atoms/CityCard';
import CardGrid from '@components/molecules/CardGrid';
import Button from '@components/atoms/Button';
import HotelCard from '@components/molecules/HotelCard';
import CardCarrousel from '@components/molecules/CardCarrousel';
import { searchPayloadDefaultValue } from '@consts/search';
import { mountSearchUrlByPayload } from 'src/utils/search';
import { useRouter } from 'next/router';
import { IDestination, ISearchPayload } from 'src/types/search';
import { addDays, format } from 'date-fns';
import { useState } from 'react';
import { AnimatePresence } from 'framer-motion';
import TripcashHero from '@components/organisms/TripcashHero';
import Head from 'next/head';
import { homeMetaTags } from '@consts/meta/home';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useTranslation } from 'next-i18next';

import highlightedCities from '@consts/hightlightedCities';
import highlightedHotels from '@consts/hightlighedHotels';
import { useSearch } from 'src/context/SearchContext';

interface IHomeProps {
  host: string;
}

const Home = ({ host }: IHomeProps) => {
  const router = useRouter();
  const { setSearchPayload } = useSearch();
  const { t, i18n } = useTranslation(['home', 'common', 'search'], {
    nsMode: 'fallback'
  });
  const [expandedHotels, setExpandedHotels] = useState<boolean>(false);
  const handleSearch = (destination: IDestination) => {
    const checkin = format(addDays(new Date(), 15), 'yyyy-MM-dd');
    const checkout = format(addDays(new Date(), 16), 'yyyy-MM-dd');

    const searchPayload: ISearchPayload = {
      ...searchPayloadDefaultValue,
      destination,
      checkin,
      checkout
    };

    const [url] = mountSearchUrlByPayload(searchPayload);
    setSearchPayload(searchPayload);

    router.push(`${t('routes.search')}?${url}`);
  };

  return (
    <Template
      title={t('title')}
      description={t('description')}
      mobileBottomNavigationBar
      navbar={{
        fixed: true,
        backgroundColor: 'transparent',
        foregroundColor: 'white',
        scrollingBackgroundColor: 'primary',
        scrollingForegroundColor: 'white'
      }}
      extraTags={homeMetaTags}
      host={host}
    >
      <Head>
        <meta
          name='keywords'
          content='reserva, hospedagem, hotel, resort, viagem, desconto, tripcash, cashback'
        />
        <script
          type='application/ld+json'
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              '@context': 'https://schema.org',
              '@type': 'Organization',
              name: 'OurTrip',
              url: `https://${host}`,
              logo: `https://${host}/images/horizontal_icon_text_blue.png`,
              email: '<EMAIL>',
              sameAs: [
                'https://www.instagram.com/ourtrip_viagens/',
                'https://www.linkedin.com/company/ourtrip-viagens/'
              ]
            })
          }}
        />
      </Head>
      <Header />
      <Showcase
        title={t('cities.title')}
        description={t('cities.description')}
        className='mt-[-20px] md:mt-0'
      >
        <CardGrid>
          {highlightedCities.map(city => (
            <CityCard
              key={city.destination.id}
              title={city.title}
              image={city.image}
              onClick={() => handleSearch(city.destination)}
            />
          ))}
        </CardGrid>
      </Showcase>
      <Showcase
        title={t('hotels.title')}
        description={t('hotels.description')}
        options={
          <Button color='gray' onClick={() => setExpandedHotels(prev => !prev)}>
            {expandedHotels ? t('hotels.showLess') : t('hotels.showMore')}
          </Button>
        }
      >
        <CardCarrousel>
          <AnimatePresence>
            {highlightedHotels
              .slice(0, expandedHotels ? 8 : 4)
              .map((hotel, index) => (
                <HotelCard
                  key={hotel.destination.id}
                  image={hotel.image}
                  name={hotel.name}
                  address={hotel.address}
                  onClick={() => handleSearch(hotel.destination)}
                  index={expandedHotels ? index - 4 : index}
                />
              ))}
          </AnimatePresence>
        </CardCarrousel>
      </Showcase>
      <TripcashHero
        title={t('tripcash.title')}
        description={t('tripcash.description')}
        action={() =>
          router.push({ pathname: t('routes.tripcash') }, '', {
            locale: i18n.language
          })
        }
        ctaText={t('tripcash.ctaText')}
      />
    </Template>
  );
};

export const getServerSideProps: GetServerSideProps = async (
  ctx: GetServerSidePropsContext
) => {
  return {
    props: {
      ...(await serverSideTranslations(ctx.locale!))
    }
  };
};

export default Home;
