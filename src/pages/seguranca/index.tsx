import { GetServerSideProps, GetServerSidePropsContext } from 'next';
import Menu, { MenuItem } from '@components/molecules/Menu';
import Template from '@components/templates/Template';
import { useRouter } from 'next/router';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useTranslation } from 'next-i18next';

interface ISecurityProps {
  host: string;
}

const Security = ({ host }: ISecurityProps) => {
  const { t, i18n } = useTranslation(['security', 'common'], {
    nsMode: 'fallback'
  });
  const router = useRouter();
  const links: MenuItem[] = [
    {
      text: t('links.terms'),
      onClick: () =>
        router.push({ pathname: t('routes.termsAndConditions') }, '', {
          locale: i18n.language
        })
    },
    {
      text: t('links.privacy'),
      onClick: () =>
        router.push({ pathname: t('routes.privacyPolicy') }, '', {
          locale: i18n.language
        })
    },
    {
      text: t('links.security'),
      onClick: () =>
        router.push({ pathname: t('routes.security') }, '', {
          locale: i18n.language
        })
    }
  ];

  return (
    <Template
      title={t('title')}
      description={t('description')}
      leftContent={<Menu items={links} />}
      host={host}
      navbar={{
        fixed: false,
        backgroundColor: 'white',
        foregroundColor: 'primary',
        scrollingBackgroundColor: 'white',
        scrollingForegroundColor: 'primary'
      }}
    >
      <div className='w-full flex items-start gap-6'>
        <div className='w-full flex flex-col flex-4 text-primary-900'>
          <h1 className='text-2xl font-semibold mb-4'>{t('heading')}</h1>

          <h2 className='text-lg font-medium mb-2.5'>
            {t('sections.vulnerabilities.title')}
          </h2>
          <p className='text-base mb-6 leading-8'>
            {t('sections.vulnerabilities.content')} <br />-{' '}
            {t('sections.vulnerabilities.requirements.0')} <br />-{' '}
            {t('sections.vulnerabilities.requirements.1')} <br />
            {t('sections.vulnerabilities.gratitude')}
          </p>

          <h2 className='text-lg font-medium mb-2.5'>
            {t('sections.phoneScams.title')}
          </h2>
          <p className='text-base mb-6 leading-8'>
            {t('sections.phoneScams.warning')}
          </p>

          <h2 className='text-lg font-medium mb-2.5'>
            {t('sections.phoneScams.whatIsIt.title')}
          </h2>
          <p className='text-base mb-6 leading-8'>
            {t('sections.phoneScams.whatIsIt.content')}
          </p>
          <p className='text-base mb-6 leading-8'>
            {t('sections.phoneScams.whatIsIt.details')}
          </p>

          <h2 className='text-lg font-medium mb-2.5'>
            {t('sections.phoneScams.whatToDo.title')}
          </h2>
          <p className='text-base mb-6 leading-8'>
            {t('sections.phoneScams.whatToDo.content')}
          </p>
          <p className='text-base mb-6 leading-8'>
            {t('sections.phoneScams.whatToDo.steps')}
          </p>

          <h2 className='text-lg font-medium mb-2.5'>
            {t('sections.importantToKnow.title')}
          </h2>
          <p className='text-base mb-6 leading-8'>
            {t('sections.importantToKnow.content')}
          </p>

          <h2 className='text-lg font-medium mb-2.5'>
            {t('sections.emailScams.title')}
          </h2>
          <p className='text-base mb-6 leading-8'>
            {t('sections.emailScams.description')}
          </p>
          <p className='text-base mb-6 leading-8'>
            {t('sections.emailScams.details')}
          </p>

          <h2 className='text-lg font-medium mb-2.5'>
            {t('sections.emailScams.whatToDo.title')}
          </h2>
          <p className='text-base mb-6 leading-8'>
            {t('sections.emailScams.whatToDo.content')}
          </p>

          <h2 className='text-lg font-medium mb-2.5'>
            {t('sections.emailScams.suspicious.title')}
          </h2>
          <p className='text-base mb-6 leading-8'>
            - {t('sections.emailScams.suspicious.steps.0')} <br />-{' '}
            {t('sections.emailScams.suspicious.steps.1')}
          </p>

          <h2 className='text-lg font-medium mb-2.5'>
            {t('sections.pricing.title')}
          </h2>
          <p className='text-base mb-6 leading-8'>
            {t('sections.pricing.content')}
          </p>
        </div>
      </div>
    </Template>
  );
};

export const getServerSideProps: GetServerSideProps = async (
  ctx: GetServerSidePropsContext
) => {
  return {
    props: {
      ...(await serverSideTranslations(ctx.locale!))
    }
  };
};

export default Security;
