/* eslint-disable no-console */
import { useRouter } from 'next/router';
import { BreadcrumbsItemType } from '@components/atoms/Breadcrumbs';
import Template from '@components/templates/Template';
import HeaderBreadcrumbs from '@components/molecules/HeaderBreadcrumbs';
import HotelHeader from '@components/molecules/HotelHeader';
import HotelImages from '@components/molecules/HotelImages';
import HotelAmenities from '@components/molecules/HotelAmenities';
import { GetServerSideProps, GetServerSidePropsContext } from 'next';
import HotelOverview from '@components/molecules/HotelOverview';
import HotelAvailableRooms from '@components/molecules/HotelAvailableRooms';
import { PurchasePricingItemType } from '@components/molecules/PurchasePricing';
import { PurchaseResumeConfigType } from '@components/molecules/PurchaseResume';
import { hotelDetails, hotelOffers } from 'src/server/hotel';
import {
  IHotelOfferPlan,
  IHotelDetails,
  IHotelOfferPayload,
  IHotelOffersCashbackInfo,
  IHotelOfferAdditionalTaxes
} from 'src/types/hotel';
import { useCallback, useEffect, useMemo, useState } from 'react';
import {
  mountPayloadBySearchUrl,
  mountRoomDistributionObjectFromString
} from 'src/utils/search';
import { ICreateCartPayload } from 'src/types/cart';
import { createCart } from 'src/server/cart';
import { toast } from 'react-toastify';
import { OffersProvider } from 'src/context/OffersContext';
import { convertStringToFormatedDate, getDateFNSLocale } from 'src/utils/date';
import {
  getPurchasePriceDetails,
  getRoomNameAndDistribution,
  getSelectedOfferPlansByTokens,
  getSelectedOffersByTokens,
  getSelectedOffersPrices,
  getTotalPriceBySelectedOffersPrices
} from 'src/utils/hotel';
import { getDestination } from 'src/server/search';
import { ISearchPayloadRooms, SearchUrlParams } from 'src/types/search';
import EmptyState from '@components/molecules/EmptyState';
import { ArrowLeft, ArrowSquareOut, Bed, Tag } from '@phosphor-icons/react';
import PurchaseDetails from '@components/organisms/PurchaseDetails';
import RoomsSearch from '@components/molecules/RoomsSearch';
import Button from '@components/atoms/Button';
import { hotelMetaTags } from '@consts/meta/hotel';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { i18n, useTranslation } from 'next-i18next';
import { useCurrency } from 'src/context/CurrencyContext';
import ImageCarousel from '@components/molecules/ImageCarousel';
import DateSearch, { DatesType } from '@components/molecules/DateSearch';
import Loading from '@components/molecules/Loading';
import { convertNumberToCurrency } from 'src/utils/currency';
import { Sheet, SheetContent } from '@components/atoms/Sheet';
import CurrencySwitch from '@components/molecules/CurrencySwitch';
import LanguageSwitch from '@components/molecules/LanguageSwitch';
import Badge from '@components/atoms/Badge';
import { useSearch } from 'src/context/SearchContext';
import * as Sentry from '@sentry/nextjs';

interface IHotel extends IHotelDetails {
  id: string;
  breadcrumbsItems: BreadcrumbsItemType[];
  backUrl: string;
  host: string;
  searchQuery: SearchUrlParams;
}

const Hotel = ({
  id,
  name,
  description,
  stars,
  address,
  photoCover,
  photos,
  amenitiesGroups,
  breadcrumbsItems,
  backUrl,
  searchQuery,
  type,
  host
}: IHotel) => {
  const router = useRouter();
  const { query } = router;
  const { currency } = useCurrency();
  const { setSearchPayload } = useSearch();
  const { t } = useTranslation(['hotel', 'common', 'purchase'], {
    nsMode: 'fallback'
  });
  const [finishLoading, setFinishLoading] = useState<boolean>(false);
  const [loadingRooms, setLoadingRooms] = useState<boolean>(true);
  const [offerPlans, setOfferPlans] = useState<IHotelOfferPlan[]>([]);
  const [searchToken, setSearchToken] = useState<string>('');
  const [selectedOfferTokens, setSelectedOfferTokens] = useState<string[]>([]);
  const [totalPrice, setTotalPrice] = useState<number>(0);
  const [withDiscount, setWithDiscount] = useState<boolean>(false);
  const [overlayOpen, setOverlayOpen] = useState<boolean>(false);
  const [selectedCurrencySymbol, setSelectedCurrencySymbol] = useState<string>(
    currency.symbol
  );
  const [tripcash, setTripcash] = useState<IHotelOffersCashbackInfo>({
    formatedPercent: '',
    formatedValue: '',
    percent: 0,
    value: 0
  });
  const [distribution, setDistribution] = useState<ISearchPayloadRooms[]>(
    mountRoomDistributionObjectFromString(
      t,
      router.query.distribution?.toString()
    )
  );
  const [distributionDetails, setDistributionDetails] = useState<
    PurchaseResumeConfigType[]
  >([]);
  const [purchasePriceDetails, setPurchasePriceDetails] = useState<
    PurchasePricingItemType[]
  >([]);

  const additionalTaxes = useMemo((): IHotelOfferAdditionalTaxes[] => {
    return (
      getSelectedOffersByTokens(offerPlans, selectedOfferTokens)
        ?.map(offer => offer.additionalTaxes)
        .filter(
          (tax): tax is IHotelOfferAdditionalTaxes => tax !== undefined
        ) ?? []
    );
  }, [offerPlans, selectedOfferTokens]);

  const [checkinDate, setCheckinDate] = useState<string>(
    router.query.checkin?.toString() ?? ''
  );
  const [checkoutDate, setCheckoutDate] = useState<string>(
    router.query.checkout?.toString() ?? ''
  );

  const [updatedCheckinDate, setUpdatedCheckinDate] =
    useState<string>(checkinDate);
  const [updatedCheckoutDate, setUpdatedCheckoutDate] =
    useState<string>(checkoutDate);
  const [updatedDistributionCode, setUpdatedDistributionCode] =
    useState<string>(router.query.distribution?.toString() || '2');

  const setUpdatedDates = (dates: DatesType) => {
    setUpdatedCheckinDate(dates[0]?.toISOString()?.split('T')[0] ?? '');
    setUpdatedCheckoutDate(dates[1]?.toISOString()?.split('T')[0] ?? '');
  };

  const setUpdatedDistribution = ({
    distributionCode
  }: {
    distributionCode: string;
  }) => {
    setUpdatedDistributionCode(distributionCode);
  };

  const handleSearch = () => {
    if (
      !updatedCheckinDate &&
      !updatedCheckoutDate &&
      !updatedDistributionCode
    ) {
      return;
    }

    router.replace(
      {
        query: {
          ...query,
          checkin: updatedCheckinDate,
          checkout: updatedCheckoutDate,
          distribution: updatedDistributionCode
        }
      },
      '',
      { shallow: true }
    );
  };

  const getChannel = useCallback(() => {
    if (router.query.UTM_SOURCE) {
      return router.query.UTM_SOURCE.toString();
    }

    if (currency.code === 'EUR') return 'SITE_EUROPA';
    if (currency.code === 'USD') return 'SITE_AMERICA';
    if (currency.code === 'ARS') return 'SITE_ARGENTINA';
    if (currency.code === 'CLP') return 'SITE_CHILE';

    return 'SITE';
  }, [router.query.UTM_SOURCE, currency.code]);

  const getPurchaseDetailsFromOffers = useCallback(() => {
    const selectedOffersObject = getSelectedOffersByTokens(
      offerPlans,
      selectedOfferTokens
    );
    const selectedOffersPrices = getSelectedOffersPrices(selectedOffersObject);

    setDistributionDetails(
      getRoomNameAndDistribution(
        t,
        getDateFNSLocale(i18n!.language),
        getSelectedOfferPlansByTokens(offerPlans, selectedOfferTokens),
        distribution
      )
    );

    setPurchasePriceDetails(
      getPurchasePriceDetails(
        t,
        i18n!.language,
        currency.code,
        checkinDate,
        checkoutDate,
        selectedOffersObject,
        selectedOffersPrices
      )
    );

    setSelectedCurrencySymbol(selectedOffersPrices[0]?.currencySymbol);
    setTotalPrice(getTotalPriceBySelectedOffersPrices(selectedOffersPrices));
    setWithDiscount(
      selectedOffersPrices.some(offer => offer.promotionalAmount)
    );
  }, [
    t,
    offerPlans,
    selectedOfferTokens,
    distribution,
    currency.code,
    checkinDate,
    checkoutDate
  ]);

  const getHotelOfferPayload = useCallback((): IHotelOfferPayload => {
    return {
      hotelId: id,
      checkin: router.query.checkin?.toString() || '',
      checkout: router.query.checkout?.toString() || '',
      distribution: mountRoomDistributionObjectFromString(
        t,
        router.query.distribution?.toString() || ''
      ),
      channel: router.query.utm_source?.toString() || getChannel(),
      plans: router.query.utm_params?.toString() || undefined,
      currency: currency.code,
      lang: i18n!.language.toUpperCase()
    };
  }, [
    t,
    id,
    router.query.checkin,
    router.query.checkout,
    router.query.distribution,
    router.query.utm_source,
    router.query.utm_params,
    getChannel,
    currency.code
  ]);

  const fetchHotelOffers = useCallback(async () => {
    setLoadingRooms(true);
    try {
      const payload: IHotelOfferPayload = getHotelOfferPayload();
      const { data } = await hotelOffers(payload);

      setOfferPlans(data.offerPlans);
      setTripcash(data.tripcashInfo);
      setSearchToken(data.searchToken);
      setCheckinDate(data.search.checkin);
      setCheckoutDate(data.search.checkout);
      setDistribution(data.search.distribution);
    } catch (err) {
      //
    } finally {
      setLoadingRooms(false);
    }
  }, [getHotelOfferPayload]);

  const fetchCreateCart = useCallback(async () => {
    try {
      setFinishLoading(true);
      const payload: ICreateCartPayload = {
        searchToken,
        offerTokens: selectedOfferTokens,
        partnerReferenceId:
          router.query.gclid?.toString() ||
          router.query.trv_ref?.toString() ||
          router.query.awc?.toString() ||
          undefined,
        channel: router.query.utm_source?.toString() || getChannel(),
        utmId: router.query.utm_id?.toString() || undefined,
        lang: i18n!.language.toUpperCase(),
        pos: router.query.pos?.toString() || undefined
      };

      const { data } = await createCart(payload);
      if (data) {
        const url = `${t('routes.checkout')}/${data}?${new URLSearchParams(
          router.query as any
        ).toString()}`;

        router.push({ pathname: url, query }, '', {
          locale: i18n!.language
        });
      }
    } catch (err) {
      toast(t('errors.checkout'), {
        type: 'error'
      });
      setFinishLoading(false);
    }
  }, [getChannel, query, router, searchToken, selectedOfferTokens, t]);

  useEffect(() => {
    fetchHotelOffers();
  }, [fetchHotelOffers]);

  useEffect(() => {
    getPurchaseDetailsFromOffers();
  }, [getPurchaseDetailsFromOffers]);

  useEffect(() => {
    const searchPayload = mountPayloadBySearchUrl(searchQuery);
    setSearchPayload(searchPayload);
  }, [searchQuery, setSearchPayload]);

  return (
    <Template
      title={t('title', { hotelName: name || 'Hotel' })}
      description={t('description', { hotelName: name })}
      navbarHiddenOnMobile
      image={photoCover?.url || photos![0]?.url}
      extraTags={hotelMetaTags}
      host={host}
      navbar={{
        fixed: false,
        backgroundColor: 'primary',
        foregroundColor: 'white',
        scrollingBackgroundColor: 'primary',
        scrollingForegroundColor: 'white'
      }}
    >
      <div className='hidden md:block'>
        <HeaderBreadcrumbs items={breadcrumbsItems} />
      </div>
      <div className='relative flex md:hidden'>
        <div className='container flex justify-between w-full absolute top-0 z-10 py-6'>
          <Button color='white' size='icon' className='opacity-90'>
            <ArrowLeft size={18} onClick={() => router.push(backUrl)} />
          </Button>
          <div className='flex gap-2'>
            <div className='flex items-center bg-white pl-3 pr-3 opacity-90 rounded-button'>
              <LanguageSwitch color='primary' />
            </div>
            <div className='flex items-center bg-white pl-3 pr-3 opacity-90 rounded-button'>
              <CurrencySwitch color='primary' />
            </div>
          </div>
        </div>
        <ImageCarousel
          cover={photoCover?.url}
          images={photos?.map(photo => photo?.url!) ?? []}
          indicator
          className='w-full h-[275px]'
        />
        <div className='w-full h-6 rounded-t-2xl bottom-0 absolute bg-gray-100 mb-[-1px]' />
      </div>
      <div className='container'>
        <HotelHeader
          name={name}
          location={address?.fullAddress}
          stars={stars ? parseInt(stars, 10) : null}
          type={type}
        />
        <div className='hidden md:block'>
          <HotelImages cover={photoCover} images={photos} />
        </div>
        <div className='flex gap-6 md:mt-12 md:mb-6'>
          <div className='flex flex-col gap-6 md:gap-8 flex-10'>
            <HotelAmenities
              title={t('amenities.title')}
              items={amenitiesGroups}
            />
            <HotelOverview content={description} />
            <div className='flex flex-col md:flex-row gap-4 bg-white rounded-default p-4'>
              <DateSearch
                label={t('search.dateLabel')}
                placeholder={t('search.datePlaceholder')}
                handleChange={setUpdatedDates}
                initialStartDate={updatedCheckinDate}
                initialEndDate={updatedCheckoutDate}
              />
              <RoomsSearch
                initial={mountRoomDistributionObjectFromString(
                  t,
                  updatedDistributionCode
                )}
                onChange={(_, distributionCode) =>
                  setUpdatedDistribution({ distributionCode })
                }
              />
              <Button type='button' onClick={handleSearch}>
                {t('search.button')}
              </Button>
            </div>
            <OffersProvider>
              {!loadingRooms && !offerPlans.length ? (
                <EmptyState
                  icon={<Bed size={42} />}
                  title={t('emptyState.title')}
                  description={t('emptyState.description')}
                />
              ) : (
                <HotelAvailableRooms
                  rooms={offerPlans}
                  loading={loadingRooms}
                  onChange={(selectedTokens: string[]) =>
                    setSelectedOfferTokens(selectedTokens)
                  }
                />
              )}
            </OffersProvider>
          </div>
          <div className='hidden md:block flex-5'>
            <PurchaseDetails
              resume={{
                checkinDate: convertStringToFormatedDate(
                  getDateFNSLocale(i18n!.language),
                  checkinDate
                ),
                checkoutDate: convertStringToFormatedDate(
                  getDateFNSLocale(i18n!.language),
                  checkoutDate
                ),
                distribution: distributionDetails
              }}
              tripcash={{
                ...tripcash,
                value: tripcash.percent * (totalPrice ?? 0),
                currencySymbol: selectedCurrencySymbol
              }}
              pricing={{
                totalPrice,
                withDiscount,
                details: purchasePriceDetails,
                disabled:
                  distributionDetails.length < distribution.length ||
                  loadingRooms,
                additionalTaxes,
                loading: finishLoading,
                onPurchase: () => fetchCreateCart(),
                position: 'sticky',
                currencySymbol: selectedCurrencySymbol
              }}
            />
          </div>
        </div>
      </div>
      <div className='md:hidden fixed flex flex-col gap-2 bottom-0 left-0 w-[100vw] bg-white px-4 py-6 rounded-t-2xl z-10 shadow-2xl shadow-black/90'>
        {!(
          distributionDetails.length < distribution.length || loadingRooms
        ) && (
          <div className='flex flex-col mb-1'>
            <div className='flex items-center gap-1'>
              <p className='text-xl text-primary-900 font-bold leading-5'>
                {`${selectedCurrencySymbol} ${convertNumberToCurrency(
                  i18n!.language,
                  currency.code,
                  totalPrice
                )}`}
              </p>
              {withDiscount && (
                <Badge
                  className='md:hidden'
                  icon={<Tag weight='bold' size={14} />}
                  type='success'
                  size='small'
                >
                  {t('availableRooms.content.pixDiscount')}
                </Badge>
              )}
            </div>
            {additionalTaxes.length === 0 && (
              <p className='text-sm text-gray-500 mb-2'>
                {t('purchasePricing.taxesAndFeesIncluded')}
              </p>
            )}
            <p className='text-sm text-primary-900'>
              {t('purchaseDetails.price.tripcash', {
                value: `${selectedCurrencySymbol} ${convertNumberToCurrency(
                  i18n!.language,
                  currency.code,
                  tripcash.percent * (totalPrice ?? 0)
                )}`
              })}
            </p>
          </div>
        )}
        <div className='flex gap-2'>
          <Button
            color='primary'
            variant='outline'
            className='h-[50px]'
            onClick={() => setOverlayOpen(true)}
          >
            {t('purchaseDetails.details')}
          </Button>
          <Button
            fullWidth
            color='primary'
            className='h-[50px]'
            onClick={() => fetchCreateCart()}
            loading={finishLoading || loadingRooms}
            disabled={distributionDetails.length < distribution.length}
          >
            {t('purchasePricing.reserveNow')}
            <ArrowSquareOut weight='bold' size={18} />
          </Button>
        </div>
      </div>
      <Sheet
        open={overlayOpen}
        onOpenChange={(open: boolean) => setOverlayOpen(open)}
      >
        <SheetContent
          side='bottom'
          className='max-h-[75vh] overflow-auto rounded-t-default'
        >
          <PurchaseDetails
            resume={{
              checkinDate: convertStringToFormatedDate(
                getDateFNSLocale(i18n!.language),
                checkinDate
              ),
              checkoutDate: convertStringToFormatedDate(
                getDateFNSLocale(i18n!.language),
                checkoutDate
              ),
              distribution: distributionDetails
            }}
            tripcash={{
              ...tripcash,
              value: tripcash.percent * (totalPrice ?? 0),
              currencySymbol: selectedCurrencySymbol
            }}
            pricing={{
              totalPrice,
              withDiscount,
              currencySymbol: selectedCurrencySymbol,
              details: purchasePriceDetails,
              disabled:
                distributionDetails.length < distribution.length ||
                loadingRooms,
              additionalTaxes,
              loading: finishLoading || loadingRooms,
              onPurchase: () => fetchCreateCart(),
              position: 'sticky'
            }}
          />
        </SheetContent>
      </Sheet>
      <Loading loading={finishLoading}>
        <div className='mt-4 text-center'>
          <h3 className='text-2xl font-medium'>{t('loading.message')}</h3>
          <p className='text-gray-500'>{t('loading.description')}</p>
        </div>
      </Loading>
    </Template>
  );
};

export const getServerSideProps: GetServerSideProps = async (
  ctx: GetServerSidePropsContext
) => {
  const id = ctx?.params?.id;
  const host = ctx.req.headers.host || '';
  const {
    distribution,
    checkin,
    checkout,
    destination,
    code,
    group,
    channel,
    UTM_SOURCE
  } = ctx?.query || '';

  const defaultCurrency = host.includes('.br') ? 'BRL' : 'USD';
  const cookieCurrency = (ctx?.req as any)?.cookies?.currency;
  const queryCurrency = ctx?.query.currency;

  const currency = queryCurrency || cookieCurrency || defaultCurrency;

  const getChannel = () => {
    if (UTM_SOURCE) {
      return UTM_SOURCE.toString();
    }

    if (currency === 'EUR') return 'SITE_EUROPA';
    if (currency === 'USD') return 'SITE_AMERICA';
    if (currency.code === 'ARS') return 'SITE_ARGENTINA';
    if (currency.code === 'CLP') return 'SITE_CHILE';

    return 'SITE';
  };

  try {
    const { data: hotel } = await hotelDetails(
      id?.toString() ?? '',
      channel?.toString() ?? getChannel(),
      ctx.locale!.toUpperCase()
    );
    const { data: destinationObject } = await getDestination(
      code?.toString() ?? '',
      group?.toString() ?? ''
    );

    const getSuggestedLocationSearchUrl = () => {
      return `${i18n!.t('routes.search')}?destination=${
        destinationObject.display
      }&code=${destinationObject.id}&group=${destinationObject.group}&latlng=${
        destinationObject?.location?.latitude
      }!${
        destinationObject?.location?.longitude
      }&checkin=${checkin}&checkout=${checkout}&distribution=${distribution}`;
    };

    return {
      props: {
        ...hotel,
        destination: destinationObject,
        backUrl: getSuggestedLocationSearchUrl(),
        searchQuery: {
          code,
          group,
          checkin,
          checkout,
          distribution,
          destination,
          currency,
          lang: i18n!.language
        },
        breadcrumbsItems: [
          {
            id: 'home',
            text: 'Início',
            href: '/'
          },
          {
            id: 'destination',
            text: destination,
            href: getSuggestedLocationSearchUrl()
          },
          {
            id: 'hotel',
            text: hotel.name || 'Hotel',
            href: `/hotel/${hotel.id}`
          }
        ],
        ...(await serverSideTranslations(ctx.locale!))
      }
    };
  } catch (err) {
    console.log(err);
    Sentry.captureException(err);

    return {
      redirect: {
        permanent: false,
        destination: '/'
      }
    };
  }
};

export default Hotel;
