import Template from '@components/templates/Template';
import LoginBox from '@components/organisms/LoginBox';

import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useTranslation } from 'next-i18next';
import { GetServerSideProps, GetServerSidePropsContext } from 'next';

interface ILoginProps {
  host: string;
}

const Login = ({ host }: ILoginProps) => {
  const { t } = useTranslation('login');

  return (
    <Template
      title={t('title')}
      description={t('description')}
      host={host}
      navbar={{
        fixed: false,
        backgroundColor: 'white',
        foregroundColor: 'primary',
        scrollingBackgroundColor: 'white',
        scrollingForegroundColor: 'primary'
      }}
    >
      <div className='container my-6 md:my-16'>
        <LoginBox />
      </div>
    </Template>
  );
};

export const getServerSideProps: GetServerSideProps = async (
  ctx: GetServerSidePropsContext
) => {
  return {
    props: {
      ...(await serverSideTranslations(ctx.locale!))
    }
  };
};

export default Login;
