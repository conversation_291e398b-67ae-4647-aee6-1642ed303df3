/* eslint-disable no-console */
import '@styles/globals.css';
import { ToastContainer } from 'react-toastify';
import type { AppContext, AppProps } from 'next/app';
import App from 'next/app';
import { useEffect, useMemo, useState } from 'react';
import {
  ISearchPayload,
  ISearchResponse,
  SearchUrlParams
} from 'src/types/search';
import {
  SearchContext,
  searchContextDefaultValues
} from 'src/context/SearchContext';
import 'react-datepicker/dist/react-datepicker.css';
import { searchResponseDefaultValues } from '@consts/search';
import 'react-toastify/dist/ReactToastify.css';
import 'react-loading-skeleton/dist/skeleton.css';
import 'react-credit-cards-2/dist/es/styles-compiled.css';
import { AuthProvider } from 'src/context/AuthContext';
import { Jost } from 'next/font/google';
import CookiesPreferences from '@components/organisms/CookiesPreferences';
import { appWithTranslation } from 'next-i18next';
import { CurrencyProvider } from 'src/context/CurrencyContext';
import TransitionLoading from '@components/organisms/TransitionLoading';
import Head from 'next/head';
import { mountPayloadBySearchUrl } from '@utils/search';
import { addDays, format } from 'date-fns';
import { convertStringToUTCDate } from '@utils/date';
import { ICustomer } from 'src/types/customer';
import { getCustomer } from 'src/server/customer';
import { ReferralProvider } from 'src/context/ReferralContext';

const jost = Jost({
  subsets: ['latin']
});

interface MyAppProps extends AppProps {
  currency: string;
  search: ISearchPayload | null;
  user: ICustomer | null;
  host: string;
}

function MyApp({
  Component,
  pageProps,
  currency,
  search,
  user,
  host
}: MyAppProps) {
  const [searchPayload, setSearchPayload] = useState<ISearchPayload>(
    search || searchContextDefaultValues.searchPayload
  );
  const [searchResponse, setSearchResponse] = useState<ISearchResponse>(
    searchResponseDefaultValues
  );
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker
        .register('/sw.js')
        .then(registration => {
          console.log('Service Worker registered:', registration);
        })
        .catch(error => {
          console.log('Service Worker registration failed:', error);
        });
    }
  }, []);

  const searchContextValues = useMemo(
    () => ({
      searchPayload,
      setSearchPayload,
      loading,
      setLoading,
      searchResponse,
      setSearchResponse
    }),
    [loading, searchPayload, searchResponse]
  );

  return (
    <main className={jost.className}>
      <Head>
        <meta
          name='viewport'
          content='width=device-width, initial-scale=1, viewport-fit=cover'
        />
      </Head>
      <ReferralProvider>
        <CurrencyProvider currency={currency}>
          <AuthProvider currentUser={user}>
            <SearchContext.Provider value={searchContextValues}>
              <Component {...pageProps} host={host} />
              <CookiesPreferences />
            </SearchContext.Provider>
            <ToastContainer
              closeOnClick
              theme='colored'
              hideProgressBar
              className='z-10001'
              position='bottom-right'
              bodyClassName='font-medium px-4 py-6 items-start'
              toastClassName='shadow-none'
            />
            <TransitionLoading />
          </AuthProvider>
        </CurrencyProvider>
      </ReferralProvider>
    </main>
  );
}

MyApp.getInitialProps = async (ctx: AppContext) => {
  const initialProps = await App.getInitialProps(ctx);
  const host = ctx.ctx.req?.headers.host || '';
  const params = Object(ctx.ctx.query) as SearchUrlParams;
  const token = (ctx.ctx.req as any)?.cookies?.token;

  const defaultCurrency = host.includes('.br') ? 'BRL' : 'USD';
  const cookieCurrency = (ctx.ctx.req as any)?.cookies?.currency;
  const queryCurrency = ctx.ctx.query.currency;

  const currency = queryCurrency || cookieCurrency || defaultCurrency;

  let search: ISearchPayload | null = null;
  if (params.latlng) {
    search = mountPayloadBySearchUrl(params);
    if (!search.checkin)
      search.checkin = format(addDays(new Date(), 15), 'yyyy-MM-dd');
    else
      search.checkin = format(
        convertStringToUTCDate(search.checkin),
        'yyyy-MM-dd'
      );
    if (!search.checkout)
      search.checkout = format(addDays(new Date(), 16), 'yyyy-MM-dd');
    else
      search.checkout = format(
        convertStringToUTCDate(search.checkout),
        'yyyy-MM-dd'
      );
  }

  let user: ICustomer | null = null;
  if (token) {
    const { data } = await getCustomer(ctx.ctx as any);
    user = data;
  }

  return {
    ...initialProps,
    search,
    currency,
    host,
    user
  };
};

export default appWithTranslation(MyApp);
