import Document, { Html, Head, Main, NextScript } from 'next/document';
import Script from 'next/script';
import { gtmId } from '@consts/env';

class MyDocument extends Document {
  render() {
    return (
      <Html lang='pt-BR' translate='no' className='notranslate'>
        <Head>
          {process.env.NODE_ENV === 'production' && (
            <Script
              src={`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS}`}
              strategy='afterInteractive'
            />
          )}
          {process.env.NODE_ENV === 'production' && (
            <Script id='gtag-script' strategy='beforeInteractive'>
              {`window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());

                gtag('config', '${process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS}');
              `}
            </Script>
          )}
          {process.env.NODE_ENV === 'production' && (
            <Script id='clarity-script' strategy='beforeInteractive'>
              {`(function(c,l,a,r,i,t,y){
                  c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                  t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                  y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
                })(window, document, "clarity", "script", "${process.env.NEXT_PUBLIC_CLARITY}");
              `}
            </Script>
          )}
          {process.env.NODE_ENV === 'production' && (
            <Script id='gtm-script' strategy='beforeInteractive'>
              {`(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','${gtmId}');`}
            </Script>
          )}
          <meta charSet='utf-8' />
          <meta name='author' content='OurTrip Viagens e Turismo' />
          <meta name='google' content='notranslate' />
          <link rel='manifest' href='manifest.json' />
          <meta name='mobile-web-app-capable' content='yes' />
          <meta name='apple-mobile-web-app-capable' content='yes' />
          <meta name='apple-mobile-web-app-status-bar-style' content='black' />
          <link rel='icon' href='/favicon.ico' sizes='any' />
        </Head>
        <body>
          <noscript>
            <iframe
              title='gtm'
              src={`https://www.googletagmanager.com/ns.html?id=${gtmId}`}
              height='0'
              width='0'
              style={{ display: 'none', visibility: 'hidden' }}
            />
          </noscript>
          <Main />
          <NextScript />
        </body>
      </Html>
    );
  }
}

export default MyDocument;
