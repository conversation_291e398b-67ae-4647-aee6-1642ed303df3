import { GetStaticProps, GetStaticPropsContext } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useTranslation } from 'next-i18next';
import Link from 'next/link';
import Button from '@components/atoms/Button';
import Hero from '@components/molecules/Hero';
import Template from '@components/templates/Template';

interface INotFoundProps {
  host: string;
}

const NotFound = ({ host }: INotFoundProps) => {
  const { t } = useTranslation('404');

  return (
    <Template
      title={t('title')}
      description={t('description')}
      host={host}
      navbar={{
        fixed: false,
        backgroundColor: 'white',
        foregroundColor: 'primary',
        scrollingBackgroundColor: 'white',
        scrollingForegroundColor: 'primary'
      }}
    >
      <div className='container my-6 md:my-16'>
        <Hero
          title={t('page.title')}
          description={t('page.description')}
          image='https://creativelayers.net/themes/gotrip-html/img/general/404.svg'
          action={
            <Link href='/'>
              <Button color='primary'>{t('page.back')}</Button>
            </Link>
          }
        />
      </div>
    </Template>
  );
};

export const getStaticProps: GetStaticProps = async (
  ctx: GetStaticPropsContext
) => {
  return {
    props: {
      ...(await serverSideTranslations(ctx.locale!))
    }
  };
};

export default NotFound;
