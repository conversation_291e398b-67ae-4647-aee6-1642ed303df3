/* eslint-disable @next/next/no-img-element */
import dynamic from 'next/dynamic';
import { GetServerSideProps, GetServerSidePropsContext } from 'next/types';
import { getOrder } from 'src/server/order';
import { IGuestOrderResponse } from 'src/types/buy';
import { Receipt as ReceiptIcon } from '@phosphor-icons/react';
import { convertStringToFormatedDate, getDateFNSLocale } from 'src/utils/date';
import { convertNumberToCurrency } from 'src/utils/currency';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useTranslation } from 'next-i18next';
import { useCurrency } from 'src/context/CurrencyContext';

const GeneratePDF = dynamic(
  () => import('../../components/atoms/GeneratePDF'),
  { ssr: false }
);

interface IReceipt {
  order: IGuestOrderResponse;
  responsable: string;
}

const Receipt = ({ order, responsable }: IReceipt) => {
  const { currency } = useCurrency();
  const { t, i18n } = useTranslation('receipt');
  const { payer } = order.payment;
  const guest = order.itens[0].booking.rooms[0].guests[0];
  const responsableName =
    responsable === 'payer' && payer
      ? `${payer?.firstName} ${payer?.lastName}`
      : `${guest.name} ${guest.surname}`;

  const paymentMethodLabel = {
    CREDIT_CARD:
      order.payment.installments === 1
        ? t('creditCardSinglePayment')
        : t('creditCardInstallments', {
            installments: order.payment.installments
          }),
    PIX: t('pixSinglePayment'),
    TRIPCASH: t('tripcash'),
    EXTERNAL: t('external')
  };

  return (
    <div className='w-[794px] flex flex-col items-center bg-gray-100 p-[50px] mx-auto recibo'>
      <GeneratePDF fileName='recibo' />
      <img
        width={200}
        height={50}
        src='/images/horizontal_icon_text_blue.png'
        alt={t('ourtripLogoAlt')}
        className='object-contain my-[15px]'
      />
      <div className='w-full flex flex-col items-center p-[50px] bg-white mt-[25px] rounded-xs'>
        <ReceiptIcon size={75} className='text-primary-900' />
        <h1 className='text-primary-900 text-xl mt-[15px] leading-normal'>
          {t('title')}
        </h1>
        <div className='w-[90%] flex flex-col gap-[10px] p-[25px] border border-gray-200 rounded-xs mt-[25px]'>
          <div className='w-full flex justify-between'>
            <p className='text-gray-500 text-sm'>{t('lodging')}</p>
            <p className='text-end flex flex-col items-end text-sm'>
              {order.itens[0].booking.hotel.name}
            </p>
          </div>
          <div className='w-full flex justify-between'>
            <p className='text-gray-500 text-sm'>{t('period')}</p>
            <p className='text-end flex flex-col items-end text-sm'>
              {t('checkin')}{' '}
              {convertStringToFormatedDate(
                getDateFNSLocale(i18n.language),
                order.itens[0].booking.checkin
              )}
              <br />
              {t('checkout')}{' '}
              {convertStringToFormatedDate(
                getDateFNSLocale(i18n.language),
                order.itens[0].booking.checkout
              )}
            </p>
          </div>
          <div className='w-full flex justify-between'>
            <p className='text-gray-500 text-sm'>{t('reservationUnderName')}</p>
            <p className='text-end flex flex-col items-end text-sm'>
              {responsableName}
            </p>
          </div>
          <div className='w-full flex justify-between'>
            <p className='text-gray-500 text-sm'>{t('orderNumber')}</p>
            <p className='text-end flex flex-col items-end text-sm'>
              {order.orderCode}
            </p>
          </div>
          <div className='w-full flex justify-between'>
            <p className='text-gray-500 text-sm'>{t('amount')}</p>
            <p className='text-end flex flex-col items-end text-sm'>
              {order.price.currencySymbol}
              {` `}
              {convertNumberToCurrency(
                i18n.language,
                currency.code,
                order.payment.transactionAmount
              )}
            </p>
          </div>
          <div className='w-full flex justify-between'>
            <p className='text-gray-500 text-sm'>{t('purchaseDate')}</p>
            <p className='text-end flex flex-col items-end text-sm'>
              {convertStringToFormatedDate(
                getDateFNSLocale(i18n.language),
                order.sentDate
              )}
            </p>
          </div>
        </div>

        <div className='w-[90%] flex flex-col gap-[10px] p-[25px] border border-gray-200 rounded-xs mt-[25px]'>
          <div className='w-full flex justify-between'>
            <p className='text-gray-500 text-sm'>{t('total')}</p>
            <p className='text-end flex flex-col items-end text-sm'>
              {order.price.currencySymbol}
              {` `}
              {convertNumberToCurrency(
                i18n.language,
                currency.code,
                order.payment.transactionAmount
              )}
            </p>
          </div>
          <div className='w-full flex justify-between'>
            <p className='text-gray-500 text-sm'>{t('paymentMethod')}</p>
            <p className='text-end flex flex-col items-end text-sm'>
              {paymentMethodLabel[order.payment.method]}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async (
  ctx: GetServerSidePropsContext
) => {
  const { id, responsable } = ctx.query;

  try {
    const { data: order } = await getOrder(id as string, ctx);
    return {
      props: {
        order,
        responsable,
        ...(await serverSideTranslations(ctx.locale!))
      }
    };
  } catch (err) {
    return {
      redirect: {
        permanent: false,
        destination: '/404'
      },
      props: {}
    };
  }
};

export default Receipt;
