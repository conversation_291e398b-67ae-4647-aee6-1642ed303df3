import { GetServerSideProps, GetServerSidePropsContext } from 'next/types';
import { getOrder } from 'src/server/order';
import { IGuestOrderResponse } from 'src/types/buy';
import { Ticket } from '@phosphor-icons/react';
import Divider from '@components/atoms/Divider';
import { convertStringToFormatedDate, getDateFNSLocale } from 'src/utils/date';
import dynamic from 'next/dynamic';
import Stars from '@components/atoms/Stars';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useTranslation } from 'next-i18next';

const GeneratePDF = dynamic(
  () => import('../../components/atoms/GeneratePDF'),
  { ssr: false }
);

interface IVoucher {
  order: IGuestOrderResponse;
}

const Voucher = ({ order }: IVoucher) => {
  const { t, i18n } = useTranslation('voucher');

  return (
    <div className='w-[794px] flex flex-col items-center bg-gray-100 p-12 mx-auto voucher'>
      <GeneratePDF fileName='voucher' />
      <img
        width={200}
        height={50}
        src='/images/horizontal_icon_text_blue.png'
        alt='Logo'
        className='object-contain my-[15px]'
      />
      <div className='w-full flex flex-col items-center p-[50px] bg-white mt-[25px] rounded-default'>
        <Ticket weight='duotone' size={75} className='text-primary-900' />
        <h1 className='text-primary-900 text-[24px] mt-[15px] leading-normal'>
          {t('title')}
        </h1>
        <div className='mt-[35px] text-primary-900 text-[20px]'>
          {t('reservationNumber')}{' '}
          <span className='font-semibold'>
            {order.itens[0].booking.reference}
          </span>
        </div>
        <div className='flex justify-center gap-[15px] my-[25px]'>
          <div className='flex flex-col text-right gap-[5px]'>
            <p className='text-gray-500'>{t('checkin')}</p>
            <h3 className='text-primary-900 text-[16px] font-medium'>
              {convertStringToFormatedDate(
                getDateFNSLocale(i18n.language),
                order.itens[0].booking.checkin
              )}
            </h3>
          </div>
          <Divider orientation='vertical' />
          <div className='flex flex-col gap-[5px]'>
            <p className='text-gray-500'>{t('checkout')}</p>
            <h3 className='text-primary-900 text-[16px] font-medium'>
              {convertStringToFormatedDate(
                getDateFNSLocale(i18n.language),
                order.itens[0].booking.checkout
              )}
            </h3>
          </div>
        </div>
        {order.status === 'CONFIRMED' && (
          <div className='text-danger-500 w-[90%]'>
            {t('paidReservationAlert')}
          </div>
        )}
        <div className='w-[90%] flex flex-col gap-[10px] p-[25px] border border-gray-200 rounded-default mt-[25px]'>
          <div className='w-full flex justify-between'>
            <p className='text-gray-500 text-[14px]'>{t('hotel')}</p>
            <div className='text-end flex flex-col items-end text-[14px]'>
              <Stars rate={parseFloat(order.itens[0].booking.hotel.stars)} />
              {order.itens[0].booking.hotel.name}{' '}
            </div>
          </div>
          <div className='w-full flex justify-between'>
            <p className='text-gray-500 text-[14px]'>{t('address')}</p>
            <p className='text-end flex flex-col items-end text-[14px]'>
              {order.itens[0].booking.hotel.address}
            </p>
          </div>
          <div className='w-full flex justify-between'>
            <p className='text-gray-500 text-[14px]'>{t('phone')}</p>
            <p className='text-end flex flex-col items-end text-[14px]'>
              {order.itens[0].booking.hotel.phone}
            </p>
          </div>
          <div className='w-full flex justify-between'>
            <p className='text-gray-500 text-[14px]'>{t('lodgingType')}</p>
            <p className='text-end flex flex-col items-end text-[14px]'>
              {order.itens[0].booking.hotel.type}
            </p>
          </div>
        </div>
        {order.itens[0].booking.rooms.map(room => (
          <div
            className='w-[90%] flex flex-col p-[25px] border border-gray-200 rounded-default mt-[25px]'
            key={room.accommodationId}
          >
            <p className='text-gray-500 text-[14px]'>
              {t('room')} {room.accommodationIndex + 1}
            </p>
            <div className='mb-[15px] font-medium text-[14px]'>
              {room.accommodationName}
            </div>
            <p className='text-gray-500 text-[14px]'>{t('guests')}</p>
            <div className='flex flex-col gap-[10px] mt-[5px] mb-[15px]'>
              {room.guests.map(guest => (
                <div
                  className='w-full flex justify-between font-medium'
                  key={guest.id}
                >
                  <p className='text-primary-900 text-[14px]'>
                    {guest.name} {guest.surname}
                  </p>
                  <p className='text-end flex flex-col items-end text-[14px]'>
                    {guest.child
                      ? `${guest.age} ${t('yearsOld')}`
                      : guest.document}
                  </p>
                </div>
              ))}
            </div>
            <Divider orientation='horizontal' />
            <div className='w-full flex justify-between my-[15px]'>
              <p className='text-gray-500 text-[14px]'>{t('mealPlanType')}</p>
              <p className='text-end flex flex-col items-end text-[14px]'>
                {room.mealPlanDisplay}
              </p>
            </div>
            {room.rateComments && room.rateComments.length > 0 && (
              <>
                <Divider orientation='horizontal' />
                <p className='text-gray-500 text-[14px] mt-4'>
                  {t('observations')}
                </p>
                <ul className='w-full'>
                  {room.rateComments?.map(rateComment => (
                    <li
                      className='float-right text-gray-500 my-[10px] text-[14px] ml-[25px]'
                      key={rateComment}
                    >
                      {rateComment}
                    </li>
                  ))}
                </ul>
              </>
            )}
          </div>
        ))}
        <div className='w-[90%] text-center text-gray-500'>
          <p className='my-[25px] text-[14px]'>
            {order.itens[0]?.booking?.additionalInformation}
          </p>
        </div>
      </div>
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async (
  ctx: GetServerSidePropsContext
) => {
  const id = ctx?.params?.id;
  try {
    const { data: order } = await getOrder(id as string, ctx);
    return {
      props: {
        order,
        ...(await serverSideTranslations(ctx.locale!))
      }
    };
  } catch (err) {
    return {
      redirect: {
        permanent: false,
        destination: '/404'
      },
      props: {}
    };
  }
};

export default Voucher;
