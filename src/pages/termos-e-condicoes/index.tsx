import { GetServerSideProps, GetServerSidePropsContext } from 'next';
import Menu, { MenuItem } from '@components/molecules/Menu';
import Template from '@components/templates/Template';
import { useRouter } from 'next/router';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useTranslation } from 'next-i18next';
import Terms from 'public/locales/pt_BR/terms.mdx';

interface ITermsAndConditionsProps {
  host: string;
}

const TermsAndConditions = ({ host }: ITermsAndConditionsProps) => {
  const { t, i18n } = useTranslation(['terms', 'common'], {
    nsMode: 'fallback'
  });
  const router = useRouter();
  const links: MenuItem[] = [
    {
      text: t('links.terms'),
      onClick: () =>
        router.push({ pathname: t('routes.termsAndConditions') }, '', {
          locale: i18n.language
        })
    },
    {
      text: t('links.privacy'),
      onClick: () =>
        router.push({ pathname: t('routes.privacyPolicy') }, '', {
          locale: i18n.language
        })
    },
    {
      text: t('links.security'),
      onClick: () =>
        router.push({ pathname: t('routes.security') }, '', {
          locale: i18n.language
        })
    }
  ];

  return (
    <Template
      title={t('title')}
      description={t('description')}
      leftContent={<Menu items={links} />}
      host={host}
      navbar={{
        fixed: false,
        backgroundColor: 'white',
        foregroundColor: 'primary',
        scrollingBackgroundColor: 'white',
        scrollingForegroundColor: 'primary'
      }}
    >
      <div
        className='
          [&>h1]:text-2xl [&>h1]:font-semibold [&>h1]:mb-4 [&>h1]:mt-6
          [&>h2]:text-xl [&>h2]:font-medium [&>h2]:mb-2.5 [&>h2]:mt-5
          [&>h3]:text-lg [&>h3]:font-medium [&>h3]:mb-2 [&>h3]:mt-4
          [&>h4]:text-md [&>h4]:font-medium [&>h4]:mb-1.5 [&>h4]:mt-3
          [&>h5]:text-sm [&>h5]:font-medium [&>h5]:mb-1 [&>h5]:mt-2
          [&>h6]:text-xs [&>h6]:font-medium [&>h6]:mb-0.5 [&>h6]:mt-1
          
          [&>p]:leading-8 [&>p]:mb-4 [&>p]:text-gray-700
          
          [&>ul]:mb-4 [&>ul]:pl-6 [&>ul]:list-disc [&>ul]:space-y-2
          [&>ol]:mb-4 [&>ol]:pl-6 [&>ol]:list-decimal [&>ol]:space-y-2
          [&>li]:leading-7
          
          [&>blockquote]:border-l-4 [&>blockquote]:border-gray-300 [&>blockquote]:pl-4 [&>blockquote]:italic [&>blockquote]:my-4 [&>blockquote]:bg-gray-50 [&>blockquote]:py-2
          
          [&>pre]:bg-gray-900 [&>pre]:text-white [&>pre]:p-4 [&>pre]:rounded-lg [&>pre]:overflow-x-auto [&>pre]:mb-4 [&>pre]:text-sm
          [&>code]:bg-gray-100 [&>code]:px-1.5 [&>code]:py-0.5 [&>code]:rounded [&>code]:text-sm [&>code]:font-mono
          [&>pre>code]:bg-transparent [&>pre>code]:p-0
          
          [&>table]:w-full [&>table]:mb-4 [&>table]:border-collapse
          [&>th]:border [&>th]:border-gray-300 [&>th]:px-4 [&>th]:py-2 [&>th]:bg-gray-100 [&>th]:font-semibold [&>th]:text-left
          [&>td]:border [&>td]:border-gray-300 [&>td]:px-4 [&>td]:py-2
          [&>tbody>tr:nth-child(even)]:bg-gray-50
          
          [&>a]:text-blue-600 [&>a]:underline [&>a]:hover:text-blue-800 [&>a]:transition-colors
          
          [&>strong]:font-semibold
          [&>em]:italic
          [&>del]:line-through [&>del]:text-gray-500
          
          [&>hr]:border-t [&>hr]:border-gray-300 [&>hr]:my-6
          
          [&>img]:max-w-full [&>img]:h-auto [&>img]:rounded-lg [&>img]:my-4
          
          [&>details]:mb-4 [&>details]:border [&>details]:border-gray-200 [&>details]:rounded-lg [&>details]:p-4
          [&>summary]:font-medium [&>summary]:cursor-pointer [&>summary]:mb-2
          
          [&>dl]:mb-4
          [&>dt]:font-semibold [&>dt]:mt-2
          [&>dd]:ml-4 [&>dd]:mb-2
          
          [&>kbd]:bg-gray-200 [&>kbd]:px-2 [&>kbd]:py-1 [&>kbd]:rounded [&>kbd]:text-xs [&>kbd]:font-mono [&>kbd]:border [&>kbd]:border-gray-300
          
          [&>mark]:bg-yellow-200 [&>mark]:px-1
          
          [&>sub]:text-xs [&>sub]:align-sub
          [&>sup]:text-xs [&>sup]:align-super
      '
      >
        <Terms />
      </div>
      {/* <div className='w-full flex items-start gap-6'>
        <div className='w-full flex flex-col flex-4 text-primary-900'>
          <h1 className='text-2xl font-semibold mb-4'>
            {t('sections.introduction.title')}
          </h1>
          <p className='text-base mb-6 leading-8'>
            {t('sections.introduction.text')}
          </p>

          <h2 className='text-lg font-medium mb-2.5'>
            {t('sections.roles.title')}
          </h2>
          <p className='text-base mb-6 leading-8'>
            {t('sections.roles.ourtrip.title')}
            <br />
            {t('sections.roles.ourtrip.data')}
            <br />
            {t('sections.roles.ourtrip.role')}
          </p>
          <p className='text-base mb-6 leading-8'>
            {t('sections.roles.client.title')}
            <br />
            {t('sections.roles.client.definition')}
            <br />
            {t('sections.roles.client.additionalDefinition')}
          </p>
          <p className='text-base mb-6 leading-8'>
            {t('sections.roles.thirdParty.title')}
            <br />
            {t('sections.roles.thirdParty.definition')}
          </p>

          <h2 className='text-lg font-medium mb-2.5'>
            {t('sections.register.title')}
          </h2>
          <p className='text-base mb-6 leading-8'>
            {t('sections.register.usage.title')}
            <br />
            {t('sections.register.usage.points.1')}
            <br />
            {t('sections.register.usage.points.2')}
            <br />
            {t('sections.register.usage.points.3')}
            <br />
            {t('sections.register.usage.points.4')}
            <br />
            {t('sections.register.usage.points.5')}
            <br />
            {t('sections.register.usage.points.6')}
          </p>
          <p className='text-base mb-6 leading-8'>
            {t('sections.register.dataCollection.title')}
            <br />
            {t('sections.register.dataCollection.points.1')}
            <br />
            {t('sections.register.dataCollection.points.2')}
            <br />
            {t('sections.register.dataCollection.points.3')}
          </p>
          <p className='text-base mb-6 leading-8'>
            {t('sections.register.registrationRestriction.title')}
            <br />
            {t('sections.register.registrationRestriction.points.1')}
            <br />
            {t('sections.register.registrationRestriction.points.2')}
            <br />
            {t('sections.register.registrationRestriction.points.3')}
            <br />
            {t('sections.register.registrationRestriction.points.4')}
            <br />
            {t('sections.register.registrationRestriction.points.5')}
            <br />
            {t('sections.register.registrationRestriction.points.6')}
          </p>
          <h2 className='text-lg font-medium mb-2.5'>
            {t('sections.services.title')}
          </h2>
          <p className='text-base mb-6 leading-8'>
            {t('sections.services.serviceUsage.title')}
            <br />
            {t('sections.services.serviceUsage.points.1')}
            <br />
            {t('sections.services.serviceUsage.points.2')}
          </p>
          <p className='text-base mb-6 leading-8'>
            {t('sections.services.accommodationServices.title')}
            <br />
            {t('sections.services.accommodationServices.points.1')}
            <br />
            {t('sections.services.accommodationServices.points.2')}
          </p>
          <p className='text-base mb-6 leading-8'>
            {t('sections.services.generalObligations.title')}
            <br />
            {t('sections.services.generalObligations.points.1')}
            <br />
            {t('sections.services.generalObligations.points.1.1')}
            <br />
            {t('sections.services.generalObligations.points.1.2')}
            <br />
            {t('sections.services.generalObligations.points.1.3')}
            <br />
            {t('sections.services.generalObligations.points.1.4')}
            <br />
            {t('sections.services.generalObligations.points.2')}
            <br />
            {t('sections.services.generalObligations.points.3')}
            <br />
            {t('sections.services.generalObligations.points.3.1')}
            <br />
            {t('sections.services.generalObligations.points.4')}
            <br />
            {t('sections.services.generalObligations.points.4.1')}
            <br />
            {t('sections.services.generalObligations.points.4.2')}
          </p>

          <h2 className='text-lg font-medium mb-2.5'>
            {t('sections.paymentMethods.title')}
          </h2>
          <p className='text-base mb-6 leading-8'>
            {t('sections.paymentMethods.accommodationServices.title')}
            <br />
            {t('sections.paymentMethods.accommodationServices.points.1')}
            <br />
            {t('sections.paymentMethods.accommodationServices.points.2')}
            <br />
            {t('sections.paymentMethods.accommodationServices.points.3')}
          </p>

          <h2 className='text-lg font-medium mb-2.5'>
            {t('sections.changeOrCancelServices.title')}
          </h2>
          <p className='text-base mb-6 leading-8'>
            {t('sections.changeOrCancelServices.generalRules.title')}
            <br />
            {t('sections.changeOrCancelServices.generalRules.points.1')}
            <br />
            {t('sections.changeOrCancelServices.generalRules.points.2')}
            <br />
            {t('sections.changeOrCancelServices.generalRules.points.3')}
          </p>

          <p className='text-base mb-6 leading-8'>
            {t('sections.changeOrCancelServices.accommodationServices.title')}
            <br />
            {t(
              'sections.changeOrCancelServices.accommodationServices.points.1'
            )}
            <br />
            {t(
              'sections.changeOrCancelServices.accommodationServices.points.1.1'
            )}
            <br />
            {t(
              'sections.changeOrCancelServices.accommodationServices.points.2'
            )}
            <br />
            {t(
              'sections.changeOrCancelServices.accommodationServices.points.2.1'
            )}
          </p>

          <p className='text-base mb-6 leading-8'>
            {t('sections.changeOrCancelServices.refundsAndPayments.title')}
            <br />
            {t('sections.changeOrCancelServices.refundsAndPayments.points.1')}
            <br />
            {t('sections.changeOrCancelServices.refundsAndPayments.points.1.1')}
            <br />
            {t('sections.changeOrCancelServices.refundsAndPayments.points.2')}
            <br />
            {t('sections.changeOrCancelServices.refundsAndPayments.points.2.1')}
            <br />
            {t('sections.changeOrCancelServices.refundsAndPayments.points.3')}
          </p>

          <p className='text-base mb-6 leading-8'>
            {t('sections.changeOrCancelServices.charges.title')}
            <br />
            {t('sections.changeOrCancelServices.charges.points.1')}
            <br />
            {t('sections.changeOrCancelServices.charges.points.2')}
            <br />
            {t('sections.changeOrCancelServices.charges.points.3')}
            <br />
            {t('sections.changeOrCancelServices.charges.points.4')}
            <br />
            {t('sections.changeOrCancelServices.charges.points.4.1')}
          </p>

          <h2 className='text-lg font-medium mb-2.5'>
            {t('sections.tripcash.title')}
          </h2>
          <p className='text-base mb-6 leading-8'>
            {t('sections.tripcash.points.1')}
            <br />
            {t('sections.tripcash.points.2')}
          </p>

          <h2 className='text-lg font-medium mb-2.5'>
            {t('sections.privacy.title')}
          </h2>
          <p className='text-base mb-6 leading-8'>
            {t('sections.privacy.points.1')}
            <br />
            {t('sections.privacy.points.2')}
          </p>

          <h2 className='text-lg font-medium mb-2.5'>
            {t('sections.supportChannels.title')}
          </h2>
          <p className='text-base mb-6 leading-8'>
            {t('sections.supportChannels.points.1')}
            <br />
            {t('sections.supportChannels.points.2')}
          </p>

          <h2 className='text-lg font-medium mb-2.5'>
            {t('sections.termsChanges.title')}
          </h2>
          <p className='text-base mb-6 leading-8'>
            {t('sections.termsChanges.points.1')}
          </p>

          <h2 className='text-lg font-medium mb-2.5'>
            {t('sections.intellectualProperty.title')}
          </h2>
          <p className='text-base mb-6 leading-8'>
            {t('sections.intellectualProperty.points.1')}
            <br />
            {t('sections.intellectualProperty.points.2')}
            <br />
            {t('sections.intellectualProperty.points.3')}
            <br />
            {t('sections.intellectualProperty.points.4')}
          </p>

          <h2 className='text-lg font-medium mb-2.5'>
            {t('sections.generalProvisions.title')}
          </h2>
          <p className='text-base mb-6 leading-8'>
            {t('sections.generalProvisions.points.1')}
            <br />
            {t('sections.generalProvisions.points.2')}
          </p>

          <h2 className='text-lg font-medium mb-2.5'>
            {t('sections.applicableLaw.title')}
          </h2>
          <p className='text-base mb-6 leading-8'>
            {t('sections.applicableLaw.points.1')}
            <br />
            {t('sections.applicableLaw.points.2')}
          </p>
          <p className='text-base mb-6 leading-8'>{t('sections.lastUpdate')}</p>
        </div>
      </div> */}
    </Template>
  );
};

export const getServerSideProps: GetServerSideProps = async (
  ctx: GetServerSidePropsContext
) => {
  return {
    props: {
      ...(await serverSideTranslations(ctx.locale!))
    }
  };
};

export default TermsAndConditions;
