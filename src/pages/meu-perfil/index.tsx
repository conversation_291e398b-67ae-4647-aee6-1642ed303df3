import {
  Envelope,
  FloppyDisk,
  MapPin,
  SealWarning,
  User
} from '@phosphor-icons/react';
import { Input } from '@components/atoms/Input';
import { useContext, useState, useEffect, useMemo } from 'react';
import { AuthContext } from 'src/context/AuthContext';
import Button from '@components/atoms/Button';
import { useForm } from 'react-hook-form';
import { ICustomer } from 'src/types/customer';
import { toast } from 'react-toastify';
import { updateCustomer } from 'src/server/customer';
import { useHookFormMask } from 'use-mask-input';
import AccountTemplate from '@components/templates/AccountTemplate';
import { GetServerSideProps, GetServerSidePropsContext } from 'next';
import { getAddressByCep } from 'src/server/cep';
import { mountAdressValues } from 'src/utils/checkout';
import { verifyEmailRequest } from 'src/server/auth';
import { AxiosError } from 'axios';
import { accountMetaTags } from '@consts/meta/account';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useTranslation, i18n } from 'next-i18next';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@components/atoms/Input/NewSelect';
import { DocumentTypeEnum } from 'src/types';
import { isCNPJ, isCPF } from '@utils/validation';
import PhoneInput from '@components/atoms/Input/Phone';

interface IProfileProps {
  host: string;
}

const Profile = ({ host }: IProfileProps) => {
  const { t } = useTranslation(['common', 'my-profile'], {
    nsMode: 'fallback'
  });
  const { user, updateUser } = useContext(AuthContext);

  const [loading, setLoading] = useState<boolean>(false);
  const [emailVerifyLoading, setEmailVerifyLoading] = useState<boolean>(false);

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
    clearErrors,
    setFocus,
    setValue
  } = useForm<ICustomer>({
    values: user,
    mode: 'onBlur',
    reValidateMode: 'onBlur'
  });

  const registerWithMask = useHookFormMask(register);

  const gender = watch('gender');
  const documentType = watch('documentType');
  const postalCode = watch('address.postalCode');
  const DDI = watch('phone.ddi');
  const isCPFSelected = documentType === 'CPF';

  const genderOptions: any[] = [
    {
      value: 'F',
      label: t('genderFemale')
    },
    {
      value: 'M',
      label: t('genderMale')
    }
  ];

  const onSubmit = async (payload: ICustomer) => {
    const updatedPayload = {
      ...payload,
      documentNumber: payload.documentNumber.replace(/[.\-/() ]/g, ''),
      phone: {
        ddi: payload.phone?.ddi,
        areaCode: payload.phone?.areaCode?.replace(/[.\-/() ]/g, ''),
        number: payload.phone?.number?.replace(/[.\-/() ]/g, '')
      },
      address: {
        ...payload.address,
        postalCode: payload.address?.postalCode?.replace(/[.\-/() ]/g, '')
      }
    };

    try {
      setLoading(true);
      const { data } = await updateCustomer(updatedPayload);
      updateUser?.(payload);

      if (data) {
        toast(t('updateSuccessMessage'), {
          type: 'success'
        });
      }
    } catch (e: unknown) {
      toast(t('updateErrorMessage'), {
        type: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const formatedCep = useMemo(() => {
    const cepValue = postalCode?.replace(/\D+/g, '');

    return Number(cepValue) && cepValue?.length === 8 ? cepValue : undefined;
  }, [postalCode]);

  useEffect(() => {
    const fetchAddress = async (cep?: string) => {
      if (!cep) return;

      try {
        const { data } = await getAddressByCep(cep);

        if (data.erro) {
          throw new Error();
        }

        const newAddress = mountAdressValues(data);

        setValue('address', newAddress, {
          shouldValidate: true
        });

        setFocus('address.city');
        setFocus('address.country');
        setFocus('address.uf');
        setFocus('address.street');
        setFocus('address.neighborhood');
        setFocus('address.number');
      } catch (err) {
        toast(t('addressNotFoundMessage'), {
          type: 'error'
        });
      }
    };

    if (formatedCep !== user.address?.postalCode?.replace(/\D+/g, '')) {
      fetchAddress(formatedCep);
    } else {
      setValue('address', user.address);
    }
  }, [t, formatedCep, postalCode, setFocus, setValue, user.address]);

  const handleEmailVerify = async () => {
    try {
      setEmailVerifyLoading(true);
      await verifyEmailRequest(i18n!.language.toUpperCase());

      toast(t('verificationEmailSentMessage'), {
        type: 'success'
      });
    } catch (err) {
      const error = err as AxiosError<any>;

      if (error.response?.status === 400 || error.response?.status === 403) {
        toast(error.response?.data?.message || t('emailSendErrorMessage'), {
          type: 'error'
        });
      } else if (error.response?.status === 404) {
        toast(t('serverNotFoundMessage'), { type: 'error' });
      } else if (error.response?.status === 500) {
        toast(error.response?.data?.message || '', { type: 'error' });
      }
    } finally {
      setEmailVerifyLoading(false);
    }
  };

  const handleGenderChange = (value: string) => {
    setValue('gender', value);
    clearErrors('gender');
  };

  const handleDocumentTypeChange = (value: string) => {
    setValue('documentType', value);
    clearErrors('documentNumber');
  };

  return (
    <AccountTemplate
      title={t('title')}
      description={t('description')}
      extraTags={accountMetaTags}
      host={host}
      navbar={{
        fixed: false,
        backgroundColor: 'white',
        foregroundColor: 'primary',
        scrollingBackgroundColor: 'white',
        scrollingForegroundColor: 'primary'
      }}
    >
      <form onSubmit={handleSubmit(onSubmit)} className='flex flex-col gap-6'>
        {!user!.verified ? (
          <div className='flex flex-col gap-2 md:flex-row justify-between items-center p-6 bg-white rounded-default'>
            <div className='flex items-center gap-4'>
              <SealWarning
                size={45}
                weight='duotone'
                className='text-yellow-700'
              />
              <div className='flex flex-col'>
                <h3 className='text-primary-900 font-semibold text-lg leading-6'>
                  {t('verificationPendingTitle')}
                </h3>
                <p className='text-sm text-gray-500'>
                  {t('verificationPendingMessage')}
                </p>
              </div>
            </div>
            <Button
              color='primary'
              loading={emailVerifyLoading}
              onClick={() => handleEmailVerify()}
              className='w-full md:w-auto'
            >
              {t('verifyButton')}
              <Envelope size={22} />
            </Button>
          </div>
        ) : null}
        <div className='flex flex-col gap-2 p-6 bg-white rounded-default'>
          <div className='flex items-center gap-2 mb-1'>
            <User size={18} className='text-primary-900' />
            <h3 className='text-primary-900 font-medium'>
              {t('personalDataHeading')}
            </h3>
          </div>
          <div className='flex flex-col md:flex-row gap-3'>
            <Input
              label={t('nameLabel')}
              {...register('name', { required: t('fieldRequiredError') })}
              error={errors.name?.message}
              focus={() => setFocus('name')}
              color='gray'
            />
            <Input
              label={t('surnameLabel')}
              {...register('surname', { required: t('fieldRequiredError') })}
              error={errors.surname?.message}
              focus={() => setFocus('surname')}
              color='gray'
            />
          </div>
          <div className='flex gap-2 items-end'>
            {i18n!.language.toLowerCase() === 'pt_br' && (
              <Select
                onValueChange={handleDocumentTypeChange}
                {...register('documentType', {
                  required: t('fieldRequiredError')
                })}
                value={documentType}
              >
                <SelectTrigger className='w-auto mt-6'>
                  <SelectValue placeholder={t('documentTypeLabel')} />
                </SelectTrigger>
                <SelectContent position='item-aligned'>
                  <SelectItem value={DocumentTypeEnum.CPF}>CPF</SelectItem>
                  <SelectItem value={DocumentTypeEnum.CNPJ}>CNPJ</SelectItem>
                </SelectContent>
              </Select>
            )}
            <Input
              label={
                i18n!.language.toLowerCase() === 'pt_br'
                  ? isCPFSelected
                    ? 'CPF'
                    : 'CNPJ'
                  : t('documentNumber')
              }
              {...registerWithMask(
                'documentNumber',
                i18n!.language.toLowerCase() === 'pt_br'
                  ? isCPFSelected
                    ? '999.999.999-99'
                    : '99.999.999/9999-99'
                  : '',
                {
                  required: t('fieldRequiredError'),
                  validate: value =>
                    i18n!.language.toLowerCase() === 'pt_br'
                      ? isCPFSelected
                        ? isCPF(value) || t('invalidCPF')
                        : isCNPJ(value) || t('invalidCNPJ')
                      : true
                }
              )}
              error={errors.documentNumber?.message}
              focus={() => setFocus('documentNumber')}
              className='flex-4'
              color='gray'
            />
          </div>
          <div className='flex flex-col md:flex-row gap-2 items-start'>
            <div className='w-full flex gap-2 items-end'>
              <Select
                onValueChange={handleGenderChange}
                {...register('gender', { required: t('fieldRequiredError') })}
                value={gender}
              >
                <SelectTrigger className='w-auto'>
                  <SelectValue placeholder={t('genderLabel')} />
                </SelectTrigger>
                <SelectContent position='item-aligned'>
                  {genderOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Input
                type='date'
                label={t('birthdateLabel')}
                {...register('birthday', { required: t('fieldRequiredError') })}
                error={errors.birthday?.message}
                focus={() => setFocus('birthday')}
                className='w-auto'
                color='gray'
              />
            </div>
            <div className='w-full mt-6'>
              <PhoneInput
                DDI={DDI}
                phoneDDIKey='phone.ddi'
                phoneAreaCodeKey='phone.areaCode'
                phoneNumberKey='phone.number'
                registerWithMask={registerWithMask}
                setFocus={setFocus}
                errors={errors}
              />
            </div>
          </div>
        </div>
        <div className='flex flex-col gap-2 p-6 bg-white rounded-default'>
          <div className='flex items-center gap-2 mb-1'>
            <MapPin size={18} className='text-primary-900' />
            <h3 className='text-primary-900 font-medium'>
              {t('addressHeading')}
            </h3>
          </div>
          <div className='flex flex-col md:flex-row gap-2'>
            <Input
              label={t('postalCodeLabel')}
              {...registerWithMask('address.postalCode', '', {
                required: t('fieldRequiredError'),
                mask:
                  i18n?.language.toUpperCase() === 'PT_BR'
                    ? '99999-999'
                    : '9{0,12}'
              })}
              error={errors.address?.postalCode?.message}
              focus={() => setFocus('address.postalCode')}
              className='flex-1'
              color='gray'
            />
            <Input
              label={t('streetLabel')}
              {...register('address.street', {
                required: t('fieldRequiredError')
              })}
              error={errors.address?.street?.message}
              focus={() => setFocus('address.street')}
              className='flex-2'
              color='gray'
            />
            <Input
              label={t('numberLabel')}
              error={errors.address?.number?.message}
              focus={() => setFocus('address.number')}
              {...registerWithMask('address.number', 'integer', {
                required: t('fieldRequiredError'),
                rightAlign: false
              })}
              className='flex-1'
              color='gray'
            />
          </div>
          <div className='flex flex-col md:flex-row gap-3'>
            <Input
              label={t('neighborhoodLabel')}
              {...register('address.neighborhood', {
                required: t('fieldRequiredError')
              })}
              error={errors.address?.neighborhood?.message}
              focus={() => setFocus('address.neighborhood')}
              color='gray'
            />
            <Input
              label={t('complementLabel')}
              {...register('address.complement')}
              focus={() => setFocus('address.complement')}
              color='gray'
            />
          </div>
          <div className='flex flex-col md:flex-row gap-3'>
            <Input
              label={t('cityLabel')}
              {...register('address.city', {
                required: t('fieldRequiredError')
              })}
              error={errors.address?.city?.message}
              focus={() => setFocus('address.city')}
              color='gray'
            />
            <Input
              label={t('stateLabel')}
              {...register('address.uf', { required: t('fieldRequiredError') })}
              error={errors.address?.uf?.message}
              focus={() => setFocus('address.uf')}
              color='gray'
            />
            <Input
              label={t('countryLabel')}
              {...register('address.country', {
                required: t('fieldRequiredError')
              })}
              error={errors.address?.country?.message}
              focus={() => setFocus('address.country')}
              color='gray'
            />
          </div>
        </div>
        <div className='flex justify-end gap-3'>
          <Button
            type='submit'
            size='large'
            loading={loading}
            className='w-full md:w-auto'
          >
            {t('saveChangesButton')}
            <FloppyDisk size={22} />
          </Button>
        </div>
      </form>
    </AccountTemplate>
  );
};

export const getServerSideProps: GetServerSideProps = async (
  ctx: GetServerSidePropsContext
) => {
  const { token } = ctx.req.cookies;

  if (!token) {
    const destination =
      ctx.locale === ctx.defaultLocale
        ? `${i18n!.t('routes.signIn')}?referrer=${i18n!.t('routes.myProfile')}`
        : `/${ctx.locale}${i18n!.t('routes.signIn')}?referrer=${i18n!.t(
            'routes.myProfile'
          )}`;

    return {
      redirect: {
        destination,
        permanent: false
      }
    };
  }

  return {
    props: {
      ...(await serverSideTranslations(ctx.locale!))
    }
  };
};

export default Profile;
