import { AxiosResponse } from 'axios';
import {
  IHotelDetails,
  IHotelOfferPayload,
  IHotelOffersResponse
} from 'src/types/hotel';
import { mainApi } from '.';

export const hotelDetails = (
  id: string,
  channel: string,
  lang: string
): Promise<AxiosResponse<IHotelDetails>> => {
  return mainApi.get(`/hotel/content/${lang}/${id}`, { params: { channel } });
};

export const hotelOffers = (
  payload: IHotelOfferPayload
): Promise<AxiosResponse<IHotelOffersResponse>> => {
  return mainApi.post(`/hotel/offers`, payload);
};
