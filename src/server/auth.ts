import { AxiosResponse } from 'axios';
import {
  IChangePasswordPayload,
  ILoginPayload,
  IRecoveryPasswordPayload,
  IRecoveryPasswordRequestPayload
} from 'src/types/auth';
import { getCookie } from 'cookies-next';
import { mainApi } from '.';

export const login = (payload: ILoginPayload): Promise<AxiosResponse> => {
  return mainApi.post(`/auth`, {}, { auth: payload });
};

export const changePasswordRequest = (
  payload: IChangePasswordPayload
): Promise<AxiosResponse> => {
  return mainApi.put(`/user/change-password`, payload, {
    headers: { Authorization: `Bearer ${getCookie('token')}` }
  });
};

export const recoveryPasswordRequest = (
  payload: IRecoveryPasswordRequestPayload
): Promise<AxiosResponse> => {
  return mainApi.post(`/user/send-reset-password`, payload);
};

export const recoveryPassword = (
  payload: IRecoveryPasswordPayload
): Promise<AxiosResponse> => {
  return mainApi.post(`/user/reset-password`, payload);
};

export const verifyEmailRequest = (lang: string): Promise<AxiosResponse> => {
  return mainApi.post(
    `/user/send-account-verification`,
    {
      lang
    },
    {
      headers: { Authorization: `Bearer ${getCookie('token')}` }
    }
  );
};

export const verifyEmail = (token: string): Promise<AxiosResponse> => {
  return mainApi.post(`/user/account-verification`, { token });
};

export const verifyAccount = (email: string): Promise<AxiosResponse> => {
  return mainApi.post(`/user/next-step`, { email });
};
