import { AxiosResponse } from 'axios';
import {
  IDestination,
  IDestinationSearchResponse,
  ISearchPayload,
  ISearchResponse
} from 'src/types/search';
import { mainApi } from '.';

export const getDestinationAutoComplete = (
  destination: string
): Promise<AxiosResponse<IDestinationSearchResponse>> => {
  return mainApi.get(`/hotel/autocomplete?hint=${destination}`);
};

export const getDestination = (
  code: string,
  group: string
): Promise<AxiosResponse<IDestination>> => {
  return mainApi.get(`/hotel/autocomplete/destination/${group}/${code}`);
};

export const availability = (
  payload: ISearchPayload
): Promise<AxiosResponse<ISearchResponse>> => {
  return mainApi.post('/hotel/availability', payload);
};
