import { AxiosResponse } from 'axios';
import { ICheckoutRequestPayload } from 'src/types/checkout';
import { IGuestOrderResponse } from 'src/types/buy';
import { getCookie } from 'cookies-next';
import { GetServerSidePropsContext } from 'next';
import { IOrderResponse } from 'src/types/order';
import { mainApi } from '.';

export const postOrder = (
  payload: ICheckoutRequestPayload
): Promise<AxiosResponse<{ token?: string; orderId?: string }>> => {
  return mainApi.post(
    `/order`,
    payload,
    getCookie('token')
      ? {
          headers: { Authorization: `Bearer ${getCookie('token')}` }
        }
      : {}
  );
};

export const getOrder = (
  orderId: string,
  ctx?: GetServerSidePropsContext
): Promise<AxiosResponse<IGuestOrderResponse>> => {
  return mainApi.get(`/order/${orderId}`, {
    headers: { Authorization: `Bearer ${getCookie('token', { ...ctx })}` }
  });
};

export const getOrders = (
  page: number,
  size: number
): Promise<AxiosResponse<IOrderResponse>> => {
  return mainApi.get(`/order?page=${page}&size=${size}`, {
    headers: { Authorization: `Bearer ${getCookie('token')}` }
  });
};

export const cancelOrder = (
  orderId: string
): Promise<AxiosResponse<IGuestOrderResponse>> => {
  return mainApi.post(
    `/order/cancel`,
    { id: orderId },
    {
      headers: { Authorization: `Bearer ${getCookie('token')}` }
    }
  );
};
