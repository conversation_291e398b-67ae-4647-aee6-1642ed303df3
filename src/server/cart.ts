import {
  ICartResponse,
  ICreateCartPayload,
  ICreateCartResponse
} from 'src/types/cart';
import { AxiosResponse } from 'axios';
import { getCookie } from 'cookies-next';
import { GetServerSidePropsContext } from 'next';
import { mainApi } from '.';

export const createCart = (
  payload: ICreateCartPayload
): Promise<AxiosResponse<ICreateCartResponse>> => {
  return mainApi.post(`/cart`, payload);
};

export const getCart = (
  id: string,
  ctx?: GetServerSidePropsContext
): Promise<AxiosResponse<ICartResponse>> => {
  return mainApi.get(
    `/cart/${id}`,
    getCookie('token', { ...ctx })
      ? {
          headers: { Authorization: `Bearer ${getCookie('token', { ...ctx })}` }
        }
      : {}
  );
};
