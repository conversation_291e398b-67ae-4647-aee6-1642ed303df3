import { getCookie } from 'cookies-next';
import { AxiosResponse } from 'axios';
import {
  IRegisterPayload,
  IRegisterResponse,
  ICustomerResponse,
  ICustomer
} from 'src/types/customer';
import { GetServerSidePropsContext } from 'next';
import { mainApi } from '.';

export const getCustomer = async (
  ctx?: GetServerSidePropsContext
): Promise<AxiosResponse<ICustomerResponse>> => {
  return mainApi.get(`/customer`, {
    headers: { Authorization: `Bearer ${getCookie('token', { ...ctx })}` }
  });
};

export const updateCustomer = async (
  payload: ICustomer
): Promise<AxiosResponse<ICustomerResponse>> => {
  return mainApi.put(`/customer`, payload, {
    headers: { Authorization: `Bearer ${getCookie('token')}` }
  });
};

export const register = (
  payload: IRegisterPayload
): Promise<AxiosResponse<IRegisterResponse>> => {
  return mainApi.post(`/customer`, payload);
};
