interface ICustomerAddress {
  street?: string;
  number?: string;
  complement?: string;
  neighborhood?: string;
  postalCode?: string;
  city?: string;
  uf?: string;
  country?: string;
}

export interface ICustomerPhone {
  ddi?: string;
  areaCode?: string;
  number?: string;
}

export interface IRegisterPayload {
  name?: string;
  surname?: string;
  documentNumber: string;
  documentType: string;
  phone?: {
    ddi?: string;
    areaCode: string;
    number: string;
  };
  email: string;
  password?: string;
  passwordAgain?: string;
  lang?: string;
}

export interface IRegisterResponse {
  id: string;
  name: string;
  surname: string;
  documentNumber: string;
  documentType: string;
  gender?: string;
  birthday?: string;
  phone: {
    areaCode: string;
    number: string;
  };
  email: string;
  verified: boolean;
  address?: ICustomerAddress;
  token: string;
}

export interface ICustomerResponse {
  id: string;
  name: string;
  surname: string;
  documentNumber: string;
  documentType: string;
  gender?: string;
  birthday?: string;
  phone?: ICustomerPhone;
  email: string;
  address?: ICustomerAddress;
  verified: boolean;
}

export interface ICustomer {
  id?: string;
  name: string;
  surname: string;
  documentNumber: string;
  documentType: string;
  gender?: string;
  birthday?: string;
  email: string;
  address?: ICustomerAddress;
  phone?: ICustomerPhone;
  tripcashBalances?: string[];
  verified?: boolean;
}
