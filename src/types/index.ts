/* eslint-disable no-unused-vars */
/* eslint-disable no-shadow */
export type RecursivePartial<T> = {
  [P in keyof T]?: RecursivePartial<T[P]>;
};

export enum DocumentTypeEnum {
  CPF = 'CPF',
  CNPJ = 'CNPJ'
}

export type MetaDataTag = {
  name: string;
  content: string;
};

declare global {
  interface Window {
    dftp: {
      init: Function;
    };
    dataLayer: any;
  }
}

export enum ECookieTypes {
  ANALYTICS = 'analytics',
  PUBLICITY = 'publicity',
  REQUIRED = 'required',
  FUNCTIONAL = 'functional'
}
