import { ISearchFilters } from './search';

type FiltersType = 'range' | 'checkbox';

export type CheckboxFilters = Exclude<
  keyof ISearchFilters,
  'price' | 'hotelName'
>;

export type Filters = keyof ISearchFilters;

export interface IRangeFilter {
  id: string;
  type?: FiltersType;
  display: string;
  applied: boolean;
  min?: number;
  max?: number;
  formatedMin?: number;
  formatedMax?: number;
  disabled?: boolean;
  initialValues?: number[];
  // eslint-disable-next-line no-unused-vars
  onChange: (range?: number[]) => void;
}

type CheckboxOptionType = {
  id: string;
  applied: boolean;
  display: string;
  quantity: number;
};

export interface ICheckBoxFilter {
  id: CheckboxFilters;
  type?: FiltersType;
  display: string;
  applied: boolean;
  options?: CheckboxOptionType[];
  // eslint-disable-next-line no-unused-vars
  onChange: (id: CheckboxFilters, value: string, applied: boolean) => void;
}
