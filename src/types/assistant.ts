export interface IMessageContent {
  type?: string;
  text: {
    value: string;
    annotations: [];
  };
}

export interface IMessage {
  id: string;
  object?: string;
  created_at: number;
  thread_id?: string;
  role: 'assistant' | 'user';
  content: IMessageContent[];
  file_ids?: [];
  assistant_id?: string;
  run_id?: string;
  metadata?: object;
}

export interface IMessageResponse {
  message: IMessage;
}
