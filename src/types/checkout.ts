/* eslint-disable no-unused-vars */
/* eslint-disable no-shadow */
declare global {
  // eslint-disable-next-line no-unused-vars
  interface Window {
    MercadoPago: any;
  }
}

export type PaymentMethodType = 'PIX' | 'CREDIT_CARD' | 'EXTERNAL';

export enum PaymentMethodEnum {
  PIX = 'PIX',
  CREDIT_CARD = 'CREDIT_CARD',
  EXTERNAL = 'EXTERNAL'
}

export enum PaymentMethodIdEnum {
  master = 'Mastercard',
  visa = 'Visa',
  amex = 'American Express',
  pix = 'Pix'
}

export type EmailFormType = {
  email: string;
};

export type GuestFields = { name: string; age?: number; birthday?: string };

export type GuestRoomType = {
  adult: GuestFields[];
  document: string;
  kid?: GuestFields[];
};

export interface GuestFormType {
  room: GuestRoomType[];
}

export interface ICardExtraDetails {
  totalAmount?: number;
  installments: number;
  installmentAmount?: number;
  paymentMethodOptionId?: string;
  instalmentRates?: number;
}

export interface GuestPayload {
  name: string;
  surname: string;
  document: string;
  age?: number;
  birthday?: string;
  child: boolean;
}

export interface IGuestsPayload {
  index: number;
  guests: GuestPayload[];
}

export interface ICardInfo {
  cvv: string;
  name: string;
  number: string;
  expireDate: string;
}

export interface ICheckoutRequestPayer {
  userAsPayer: boolean;
  email: string;
  firstName: string;
  lastName: string;
  identification: {
    documentType: string;
    documentNumber: string;
  };
  phone?: {
    ddi?: string;
    areaCode: string;
    number: string;
  };
}

export interface ICheckoutCreditCardPayerAddress {
  street?: string;
  number?: string;
  complement?: string;
  neighborhood?: string;
  postalCode?: string;
  city?: string;
  uf?: string;
  country?: string;
}

export interface ICheckoutRequestCreditCardPayer extends ICheckoutRequestPayer {
  address?: ICheckoutCreditCardPayerAddress;
}

export interface ICreditCardFormFields extends ICardInfo {
  payer: ICheckoutRequestCreditCardPayer;
  installments: number;
  paymentMethodId?: string;
  paymentMethodOptionId?: string;
  installmentOptionId?: string;
  token?: string;
}

export interface CheckoutFields extends GuestFormType, EmailFormType {
  pix: ICheckoutRequestPayer;
  external: ICheckoutRequestPayer;
  creditCard: ICreditCardFormFields;
  customerRemark?: string;
}

export interface ICheckoutRequestCard {
  holderName: string;
  number: string;
  expirationMonth: string;
  expirationYear: string;
  cvv: string;
}

export interface ICheckoutRequestPaymentInfo {
  method: PaymentMethodType;
  paymentMethodId?: string;
  transactionAmount?: number;
  transactionAmountOriginal?: number;
  instalmentRates?: number;
  installmentAmount?: number;
  paymentMethodOptionId?: string;
  installmentOptionId?: string;
  payer: ICheckoutRequestCreditCardPayer;
  installments?: number;
  feeAmount?: number;
  token?: string;
  tripcashApplied: boolean;
  card?: ICheckoutRequestCard;
  attemptReference?: string;
}

export interface ICheckoutRequestPayload {
  guests: IGuestsPayload[];
  cartId: string;
  paymentInfo: ICheckoutRequestPaymentInfo;
  customerRemark?: string;
}

export type PaymentData =
  | { pix: ICheckoutRequestPayer }
  | { external: ICheckoutRequestPayer }
  | { creditCard: ICreditCardFormFields };

export enum CheckoutStepEnum {
  ACCOUNT = 0,
  PAYMENT = 1,
  GUESTS = 2,
  RESUME = 3
}

export type CheckoutStepType = {
  current: number;
  next: number;
};
