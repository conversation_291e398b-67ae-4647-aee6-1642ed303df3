/* eslint-disable import/no-cycle */
import { ISearchPayloadRooms } from './search';

interface IHotelAmenity {
  code?: string;
  description?: string;
  icon?: string;
}

export interface IHotelAmenitiesGroup {
  code?: string;
  description?: string;
  icon?: string;
  amenities?: IHotelAmenity[];
}

export interface IHotelPhoto {
  url?: string;
  smallUrl?: string;
}

interface IHotelAddressLocation {
  latitude?: string;
  longitude?: string;
}

interface IHotelAddress {
  fullAddress?: string;
  street?: string;
  number?: string;
  complement?: string;
  neighborhood?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  location?: IHotelAddressLocation;
}

export interface IHotelDetails {
  id?: string;
  name?: string;
  description?: string;
  type?: string;
  stars?: string;
  phone?: string;
  email?: string;
  address?: IHotelAddress;
  rating?: string;
  photoCover?: IHotelPhoto;
  photos?: IHotelPhoto[];
  amenitiesGroups?: IHotelAmenitiesGroup[];
}

export interface IHotelOfferPayload {
  hotelId: string;
  checkin: string;
  checkout: string;
  distribution: ISearchPayloadRooms[];
  channel: string;
  plans?: string;
  currency: string;
  lang: string;
}

export interface IHotelOfferPrices {
  price: number;
  pricePromotional?: number;
  pricePerNight: number;
  pricePerNightPromotional?: number;
  taxValue: number;
  taxValuePromotional?: number;
  finalPrice: number;
  finalPricePromotional?: number;
  paymentType: string;
  pricingType: string;
  currency: string;
  currencySymbol: string;
  promotionalAmount?: number;
  promotionalPercentual?: number;
}

export interface IHotelOfferCancellationPolicies {
  refundable: boolean;
  refundObservations: string;
  cancellationLimitDate: string;
}

interface IHotelOfferMealPlan {
  code: string;
  description: string;
}

interface IHotelOfferAdditionalTaxesDetails {
  dateStart?: string;
  dateEnd?: string;
  description?: string;
  price: number;
  included: boolean;
}

export interface IHotelOfferAdditionalTaxes {
  amountIncluded: number;
  amountNotIncluded: number;
  currency: string;
  currencySymbol: string;
  originalAmountIncluded: number;
  originalAmountNotIncluded: number;
  originalCurrency: string;
  originalTotalAmount: number;
  taxes: IHotelOfferAdditionalTaxesDetails[];
  totalAmount: number;
}

export interface IHotelOffer {
  accommodationIndex: number;
  additionalTaxes?: IHotelOfferAdditionalTaxes;
  appliedPromotions?: string[];
  token: string;
  prices: IHotelOfferPrices;
  adults: number;
  kids: number[];
  rateComments: any[];
  isSelected: boolean;
}

export interface IHotelOfferPlan {
  token: string;
  accommodationId: string;
  accommodationName: string;
  ratePlan: string;
  cancellationPolicies: IHotelOfferCancellationPolicies;
  mealPlan: IHotelOfferMealPlan;
  photoCover: IHotelPhoto;
  photos: IHotelPhoto[];
  amenitiesGroups: IHotelAmenitiesGroup[];
  offers: IHotelOffer[];
}

export interface IHotelOffersCashbackInfo {
  formatedPercent: string;
  formatedValue: string;
  percent: number;
  value: number;
}

export interface IHotelOffersResponse {
  search: IHotelOfferPayload;
  searchToken: string;
  offerPlans: IHotelOfferPlan[];
  tripcashInfo: IHotelOffersCashbackInfo;
}
