/* eslint-disable no-shadow */

import { IBookingHotel } from './booking';
import { TripcashInfo } from './cart';

/* eslint-disable no-unused-vars */
export interface PriceValues {
  value?: number;
  formattedValue: string;
  description: string;
  discount?: boolean;
  currency?: string;
}

export interface Hotel extends IBookingHotel {
  phone: string;
  type: string;
}

export interface Price {
  price: PriceValues;
  priceWithTax: PriceValues;
  finalPrice: PriceValues;
  discount: PriceValues;
  priceWithDiscount: PriceValues;
  installmentAmount: PriceValues;
  installments: number;
  currency: string;
  taxValue: PriceValues;
  currencySymbol: string;
  tripcashInfo: { planId: string } & TripcashInfo;
}

export interface PayerIdentification {
  documentType: string;
  documentNumber: string;
}

export interface Phone {
  areaCode: string;
  number: string;
}

export interface Address {
  street: string;
  number: string;
  complement: string;
  neighborhood: string;
  postalCode: string;
  city: string;
  uf: string;
  country: any;
}

export interface PaymentResponse {
  paymentResponseId: string;
  status: string;
  detail: string;
  qrCodeBase64: string;
  qrCode: string;
  qrCodeUrl: any;
  paymentMethodId: string;
  paymentApi: string;
  creationDate: string;
  captureDate: any;
  secondsRemaining: number;
}

export interface Payer {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  payerIdentification: PayerIdentification;
  phone: Phone;
  address: Address;
}
export interface Payment {
  id: string;
  method: 'PIX' | 'CREDIT_CARD' | 'TRIPCASH' | 'EXTERNAL';
  paymentApi: any;
  token: any;
  issuerId: any;
  buyerUuid: any;
  paymentMethodId: any;
  transactionAmount: number;
  transactionAmountRefunded: any;
  transactionAmountOriginal: number;
  feeAmount: number;
  installmentAmount: number;
  installments: number;
  installmentsRate: any;
  payer: Payer;
  paymentResponse: PaymentResponse;
  antifraudResponse: any;
  authorizationCode: any;
  description: string;
}

export interface OrderPaymentResponse {
  paymentResponseId: string;
  status: string;
  detail: string;
  qrCodeBase64?: string;
  qrCode?: string;
  paymentApi: string;
  captureDate: string;
  creationDate: string;
  secondsRemaining: number;
  iframe: string;
}

export type OrderStatus =
  | 'CONFIRMED'
  | 'PENDING_PAYMENT'
  | 'CANCELED'
  | 'PENDING_ANTIFRAUD'
  | 'PAYMENT_REJECTED'
  | 'PENDING_CONFIRMATION';

export type OrderPaymentMethod =
  | 'PIX'
  | 'CREDIT_CARD'
  | 'TRIPCASH'
  | 'EXTERNAL';

export interface Guest {
  id: string;
  name: string;
  surname: string;
  document: string;
  age: number;
  child: boolean;
}

export interface CancellationPolicies {
  id: string;
  refundable: boolean;
  refundableOriginalInfo: boolean;
  refundObservations: string;
  cancellationLimitDate: string;
}

export interface Room {
  id: string;
  accommodationId: string;
  accommodationIndex: number;
  accommodationName: string;
  cancellationPolicies: CancellationPolicies;
  mealPlan: string;
  mealPlanDisplay: string;
  guests: Guest[];
  rateComments?: string[];
}

export interface Booking {
  id: string;
  status: string;
  orderStatus: string;
  statusDisplay: string;
  checkin: string;
  checkout: string;
  reference: any;
  reservationCode: string;
  hotel: Hotel;
  price: Price;
  orderItemCode: any;
  rooms: Room[];
  additionalInformation?: string;
}

export interface Item {
  id: string;
  index: number;
  type: string;
  finalPrice: number;
  booking: Booking;
  tripcashTransaction: any;
}

export interface IGuestOrderResponse {
  id: string;
  status: OrderStatus;
  sentDate: string;
  paymentResponse: OrderPaymentResponse;
  reference: any;
  reservationCode: string;
  price: Price;
  payment: Payment;
  itens: Item[];
  orderCode: string;
  paymentMethod: OrderPaymentMethod;
  tripcashInfo: Omit<TripcashInfo, 'formatedPercent' | 'percent'>;
  adjustments: {
    id: string;
    type: 'DISCOUNT_PIX' | 'TRIPCASH';
    value: number;
  }[];
}

export type BookingInfo = {
  [key in OrderPaymentMethod]: {
    [key in OrderStatus]: {
      title: string;
      subtitle?: string;
      icon: any;
      messageType: 'success' | 'info' | 'danger';
    };
  };
};
