import { IBooking<PERSON><PERSON>, IBookingRoom, IBookingStatusEnum } from './booking';
import { IHotelPhoto } from './hotel';

interface IOrderItem {
  id: string;
  index: number;
  type: string;
  finalPrice: number;
  booking: {
    id: string;
    status: IBookingStatusEnum;
    orderStatus: string;
    statusDisplay: string;
    checkin: string;
    checkout: string;
    reference: string;
    reservationCode: string;
    hotel: {
      id: string;
      name: string;
      stars: string;
      address: string;
      photoCover: IHotelPhoto;
    };
    price: IBookingPrice;
    orderItemCode: any;
    rooms: IBookingRoom[];
  };
  cashbackTransaction: any;
}

interface IOrderPriceDetail {
  value: number;
  formattedValue: string;
  description: string;
}

interface IOrderPrice {
  price: IOrderPriceDetail;
  discountAmount: any;
  priceWithDiscount: any;
  installments: number;
  installmentsFormatted: IOrderPriceDetail;
  installmentAmount: IOrderPriceDetail;
  originalPrice: IOrderPriceDetail;
  finalPrice: IOrderPriceDetail;
  currency: string;
  currencySymbol: string;
  cashbackInfo: any;
}

interface IOrderPaymentResponse {
  paymentResponseId: string;
  status: string;
  detail: string;
  qrCodeBase64: string;
  qrCode: string;
  paymentMethodId: string;
  paymentApi: string;
  captureDate: string;
  creationDate: string;
  secondsRemaining: number;
  iframe?: string;
}

export interface IOrderPayment {
  id: string;
  status:
    | 'CONFIRMED'
    | 'PENDING_PAYMENT'
    | 'PAYMENT_EXPIRED'
    | 'CANCELED'
    | 'PENDING_ANTIFRAUD'
    | 'PAYMENT_REJECTED';
  method: 'CREDIT_CARD' | 'PIX';
  paymentApi?: string;
  token?: string;
  issuerId?: string;
  paymentMethodId: 'master';
  transactionAmount: number;
  transactionAmountRefunded: number;
  transactionAmountOriginal: number;
  feeAmount: number;
  installmentAmount: number;
  installments: number;
  installmentsRate?: number;
  payer?: string;
  paymentResponse: IOrderPaymentResponse;
  authorizationCode: string;
  description: string;
}

export interface IOrder {
  id: string;
  status:
    | 'CONFIRMED'
    | 'PENDING_PAYMENT'
    | 'PAYMENT_EXPIRED'
    | 'CANCELED'
    | 'PENDING_ANTIFRAUD'
    | 'PAYMENT_REJECTED';
  sentDate: string;
  orderCode: string;
  price: IOrderPrice;
  payment: IOrderPayment;
  paymentMethod: 'CREDIT_CARD' | 'PIX';
  paymentResponse: IOrderPaymentResponse;
  itens: IOrderItem[];
  cancellationDate: string;
  cancellationInfo: {
    infos: string[];
    isCancellable: boolean;
  };
}

export interface IOrderResponse {
  content: IOrder[];
  pageable: {
    sort: [];
    offset: number;
    pageNumber: number;
    pageSize: number;
    paged: boolean;
    unpaged: boolean;
  };
  last: boolean;
  totalElements: number;
  totalPages: number;
  sort: {
    direction: 'DESC' | 'ASC';
    property: string;
    ignoreCase: boolean;
    nullHandling: string;
    ascending: boolean;
    descending: boolean;
  }[];
  first: boolean;
  size: number;
  number: number;
  numberOfElements: number;
  empty: boolean;
}
