import { ISearchPayloadRooms } from './search';

export interface ICreateCartPayload {
  searchToken: string;
  offerTokens: string[];
  partnerReferenceId?: string;
  channel?: string;
  utmId?: string;
  lang: string;
  pos?: string | undefined;
}

type PaymentMethodsAcceptedTypes = 'PIX' | 'CREDIT_CARD';

export interface ICreateCartResponse {
  id: string;
}

export interface PhotoCover {
  url: any;
  smallUrl: any;
}

export interface ICartHotel {
  id: string;
  name: string;
  stars: string;
  address: string;
  photoCover: PhotoCover;
}

export interface Prices {
  price: number;
  taxValue: number;
  finalPrice: number;
  paymentType: string;
  pricingType: string;
  currency: string;
  currencySymbol: string;
}

export interface CancellationPolicies {
  refundable: boolean;
  refundObservations: string;
  cancellationLimitDate: string;
}

export interface MealPlan {
  code: string;
  description: string;
}

export interface IRateComment {
  dateStart: any;
  dateEnd: any;
  description: string;
  price: number;
  included: boolean;
  onlyDescription: boolean;
}

export interface ICartRoomRateComments {
  amountIncluded: number;
  amountNotIncluded: number;
  totalAmount: number;
  currency: any;
  currencySymbol: any;
  rateComments: IRateComment[];
}

export interface ICardRequiredGuestInformation {
  birthday: boolean;
}

export interface ICartRoomDetails {
  accommodationIndex: number;
  accommodationName: string;
  prices: Prices;
  cancellationPolicies: CancellationPolicies;
  mealPlan: MealPlan;
  adults: number;
  kids: number[];
  requiredGuestInformation: ICardRequiredGuestInformation;
  photoCover: any;
  rateComments: string[];
}

export interface PriceDescription {
  text: string;
  value: string;
  discount?: boolean;
  currency?: string;
}

export interface Summary {
  taxesAndFees: PriceDescription;
  total: PriceDescription;
  totalWithoutTaxes: PriceDescription;
  unitPrice: PriceDescription;
  adjustments: PriceDescription[];
  subTotal: PriceDescription;
}

export interface TripcashInfo {
  formatedPercent: string;
  formatedValue: string;
  percent: number;
  value: number;
}

export interface AdditionalTaxes {
  amountIncluded: PriceDescription;
  amountNotIncluded: PriceDescription;
  totalAmount: PriceDescription;
  totalWithAmountNotIncluded: PriceDescription;
  description: string;
  currency: string;
  currencySymbol: string;
  originalAmountIncluded: string;
  originalAmountNotIncluded: string;
  originalTotalAmount: string;
  originalCurrency: string;
}

export interface Price {
  currency: string;
  userCurrency: string;
  currencySymbol: string;
  userCurrencySymbol: string;
  main: string;
  mainText: string;
  price: number;
  userFinalPrice: number;
  userFinalPriceWithDecimal: number;
  priceWithDecimal: number;
  taxMessage: string;
  tripcashInfo: TripcashInfo;
  summary: Summary;
  additionalTaxes: AdditionalTaxes;
  promotionalPercentual: number;
  promotionalAmount: number;
  applyUserPrice: boolean;
}

export interface ICartRoom {
  id: string;
  type: 'HOTEL';
  offers: ICartRoomDetails[];
  prices: Price;
}

export interface ICartInstallments {
  totalAmount: number;
  totalAmountWithDecimal: number;
  totalAmountText: string;
  interestRate: number;
  installmentAmount: number;
  installmentAmountText: string;
  interestAmount: number;
  installments: number;
  installmentOptionId: string;
  recommendedMessage: string;
  pkTokenization: string;
}

export interface ICartResponse {
  id: string;
  searchToken: string;
  checkin: string;
  checkout: string;
  creationDate: string;
  secondsRemaining: number;
  hotel: ICartHotel;
  items: ICartRoom[];
  price: Price;
  priceWithTripcash: Price;
  pricePromotional: Price;
  pricePromotionalWithTripcash: Price;
  distribution: ISearchPayloadRooms[];
  paymentMethodsAccepted: PaymentMethodsAcceptedTypes[];
  paymentInstallments: ICartInstallments[];
  paymentInstallmentsWithTripcash: ICartInstallments[];
  paymentsApi: {
    api: string;
    installmentsMax: number;
    installmentsMin: number;
    method: string;
    pkTokenization: string;
  }[];
}
