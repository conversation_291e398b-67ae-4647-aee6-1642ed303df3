/* eslint-disable import/no-cycle */
import { Dispatch, SetStateAction } from 'react';
import { TFunction } from 'next-i18next';
import { IHotelPhoto } from './hotel';

export interface ISearchPayloadRooms {
  adults: number;
  kids: number[];
}

type DestinationGroup = 'CITY' | 'HOTEL' | '';

export interface LatLng {
  latitude: number;
  longitude: number;
}

export interface ISearchFilters {
  mealPlans: string[];
  stars: string[];
  acommodationTypes: string[];
  amenities: string[];
  price: string | null;
  hotelName: string | null;
  cancellationPolicyRefundable: boolean;
  tripcash: string[];
}

export interface IDestination {
  display: string;
  id: string;
  group: DestinationGroup;
  location: LatLng;
  country?: string;
  countryCode?: string;
  city?: string;
  state?: string;
}

export interface ISearchBoxPayload {
  destination: IDestination;
  checkin: Date | string;
  checkout: Date | string;
}

export interface ISearchPayload {
  destination: IDestination;
  checkin: string | null;
  checkout: string | null;
  distribution: ISearchPayloadRooms[];
  page: number;
  currency?: string;
  limit: number;
  searchId: string;
  sorting: string;
  filters: ISearchFilters;
  channel: string;
  lang?: string;
}

export interface ISearchDTO {
  checkin: string;
  checkout: string;
  distribution: ISearchPayloadRooms[];
}

export interface IDestinationSearchGroup {
  group: DestinationGroup;
  display: string;
  itens: IDestination[];
}

export interface IDestinationSearchResponse {
  suggestion: {
    groups: IDestinationSearchGroup[];
  };
}

export interface SearchUrlParams {
  t: TFunction;
  destination: string;
  code: string;
  group: DestinationGroup;
  latlng: string;
  checkin: string;
  checkout: string;
  distribution: string;
  currency?: string;
  lang: string;
}

interface ISearchResponsePaging {
  page: number;
  limit: number;
  total: number;
}

export interface ISearchResponseFiltersApplied {
  id: string;
  type: string;
  display: string;
  applied: boolean;
  value: string;
}

interface ISearchResponseMainFilterOptions {
  id: string;
  applied: boolean;
  display: string;
  quantity: number;
}

interface ISearchResponsePrices {
  text: string;
  value: string;
}

export interface ISearchResponseMainFilters {
  id: keyof ISearchFilters;
  type: string;
  display: string;
  applied: boolean;
  min?: number;
  max?: number;
  appliedMin?: number;
  appliedMax?: number;
  formatedMin?: string;
  formatedMax?: string;
  options?: ISearchResponseMainFilterOptions[];
}

export interface ISearchSorting {
  selected: boolean;
  display: string;
  label: string;
}

interface ISearchResponseMealPlan {
  code: string;
  description: string;
}

export interface ISearchResponseResult {
  accomodation: {
    id: string;
    fullAddress: string;
    destinations: string[];
    type: string;
    address: { fullAddress: string };
    name: string;
    stars: number;
    photosCover: IHotelPhoto;
    photos: IHotelPhoto[];
    amenitiesGroups: null;
  };
  detailsUrl: string;
  mealPlan: ISearchResponseMealPlan;
  refundable: boolean;
  position: number;
  rate: number | null;
  lastRooms: number;
  distance: number;
  unavailable: boolean;
  choose: boolean;
  price: {
    currency: string;
    currencySymbol: string;
    main: string;
    mainText: string;
    price: number;
    taxMessage: string;
    tripcashInfo: {
      formatedPercent: string;
      formatedValue: string;
      percent: number;
      value: number;
    };
    summary: {
      taxesAndFees: ISearchResponsePrices;
      total: ISearchResponsePrices;
      totalWithoutTaxes: ISearchResponsePrices;
      unitPrice: ISearchResponsePrices;
    };
  };
  pricePromotional?: {
    currency: string;
    currencySymbol: string;
    main: string;
    mainText: string;
    price: number;
    priceWithDecimal: number;
    taxMessage: string;
    tripcashInfo: object;
    summary: {
      taxesAndFees: {
        text: string;
        value: string;
      };
      total: {
        text: string;
        value: string;
      };
      subTotal: {
        text: string;
        value: string;
      };
      totalWithoutTaxes: {
        text: string;
        value: string;
      };
      unitPrice: {
        text: string;
        value: string;
      };
      adjustments: {
        text: string;
        value: string;
      }[];
    };
    additionalTaxes: null;
    promotionalPercentual: 3;
    promotionalAmount: 537;
  };
  highlightTag?: string;
}

export interface ISearchResponse {
  searchId: string;
  checkin: string;
  checkout: string;
  paging: ISearchResponsePaging;
  filtersApplied: ISearchResponseFiltersApplied[];
  filtersMain: ISearchResponseMainFilters[];
  sorting: ISearchSorting[];
  result: ISearchResponseResult[];
}

export interface ISearchContext {
  searchPayload: ISearchPayload;
  setSearchPayload: Dispatch<SetStateAction<ISearchPayload>>;
  searchResponse: ISearchResponse;
  setSearchResponse: Dispatch<SetStateAction<ISearchResponse>>;
  loading: boolean;
  setLoading: Dispatch<SetStateAction<boolean>>;
}
