/* eslint-disable no-unused-vars */
/* eslint-disable no-shadow */
import { IHotelOfferPlan, IHotelPhoto } from './hotel';

export interface IBookingHotel {
  id: string;
  name: string;
  stars: string;
  address: string;
  photoCover: IHotelPhoto;
}

interface IBookingPriceDescription {
  value: number;
  formattedValue: string;
  description: string;
}

export interface IBookingPrice {
  price: IBookingPriceDescription;
  pricePerNight: IBookingPriceDescription;
  taxValue: IBookingPriceDescription;
  priceWithTax: IBookingPriceDescription;
  discount: IBookingPriceDescription;
  priceWithDiscount: IBookingPriceDescription;
  installments: number;
  installmentValue: IBookingPriceDescription;
  finalPrice: IBookingPriceDescription;
  currency: string;
  currencySymbol: string;
  tripcashInfo: IBookingPriceDescription;
  orderItemCode?: string;
}

export interface IBookingGuest {
  id: string;
  age: number;
  child: boolean;
  document: string;
  name: string;
  surname: string;
}

export interface IBookingRoom
  extends Omit<
    IHotelOfferPlan,
    'photos' | 'photoCover' | 'amenitiesGroups' | 'token' | 'offers'
  > {
  offerToken: string;
  accommodationIndex: number;
  mealPlanDisplay: string;
  guests: IBookingGuest[];
}

export enum IBookingStatusEnum {
  CREATED = 'CREATED',
  BOOKING = 'BOOKING',
  BOOKED = 'BOOKED',
  FAILED = 'FAILED',
  DISCONTINUED = 'DISCONTINUED',
  PENDING_CANCEL = 'PENDING_CANCEL',
  CANCELED = 'CANCELED'
}

export interface IBooking {
  id: string;
  status: IBookingStatusEnum;
  statusDisplay: string;
  orderStatus: string;
  orderItemCode: string;
  checkin: string;
  checkout: string;
  reference: string;
  reservationCode: string;
  hotel: IBookingHotel;
  price: IBookingPrice;
  rooms: IBookingRoom[];
}
