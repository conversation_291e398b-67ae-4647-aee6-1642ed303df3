export interface ITripcashOffer {
  formatedPercent: string;
  formatedValue: string;
  percent: number;
  value: number;
}

export interface TripcashAmount {
  value: number;
  formattedValue: string;
  description: string;
}

export interface TripcashValue {
  description: string;
  amounts: TripcashAmount[];
}

interface TripcashTransaction {
  id: string;
  createAt: string;
  amount: Omit<TripcashAmount, 'description'>;
  description: string;
  status: 'CONFIRMED' | 'PENDING' | 'CANCELED';
  statusDisplay: string;
}

export interface ITripcash {
  pendingAmount: TripcashValue;
  availableAmount: TripcashValue;
  usedAmount: TripcashValue;
  transactions: TripcashTransaction[];
}
