// This file configures the initialization of Sentry on the client.
// The added config here will be used whenever a users loads a page in their browser.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: 'https://<EMAIL>/4509361751326800',
  tracesSampleRate: 0,
  debug: false
});

export const onRouterTransitionStart = Sentry.captureRouterTransitionStart;
