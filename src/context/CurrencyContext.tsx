/* eslint-disable no-unused-vars */
/* eslint-disable no-shadow */
import { setCookie } from 'cookies-next';
import { addYears } from 'date-fns';
import { i18n } from 'next-i18next';
import React, {
  createContext,
  useState,
  useContext,
  ReactNode,
  useMemo,
  useCallback
} from 'react';

type Currency = {
  code: string;
  symbol: string;
  show?: boolean;
};

interface CurrencyContextType {
  currency: Currency;
  setCurrency: (currency: string) => void;
}

export const currencies: Record<string, Currency> = {
  USD: { code: 'USD', symbol: '$', show: true },
  EUR: { code: 'EUR', symbol: '€', show: true },
  ARS: { code: 'ARS', symbol: '$', show: true },
  CLP: { code: 'CLP', symbol: '$', show: true },
  BRL: { code: 'BRL', symbol: 'R$', show: true }
};

const STORAGE_KEY = 'currency';

export const CurrencyContext = createContext<CurrencyContextType | undefined>(
  undefined
);

interface CurrencyProviderProps {
  currency: string;
  children: ReactNode;
}

export const CurrencyProvider: React.FC<CurrencyProviderProps> = ({
  currency,
  children
}) => {
  const [newCurrency, setNewCurrency] = useState<Currency>(
    currencies[currency]
  );

  const handleSetCurrency = useCallback((currency: string) => {
    setNewCurrency(currencies[currency]);
    setCookie(STORAGE_KEY, currency, {
      sameSite: 'lax',
      secure: true,
      expires: addYears(new Date(), 1),
      path: '/'
    });
  }, []);

  const value = useMemo(
    () => ({
      currency: newCurrency,
      setCurrency: handleSetCurrency,
      currencies
    }),
    [newCurrency, handleSetCurrency]
  );

  return (
    <CurrencyContext.Provider value={value}>
      {children}
    </CurrencyContext.Provider>
  );
};

export const useCurrency = () => {
  const context = useContext(CurrencyContext);

  if (context === undefined) {
    throw new Error('useCurrency must be used within a CurrencyProvider');
  }

  return context;
};
