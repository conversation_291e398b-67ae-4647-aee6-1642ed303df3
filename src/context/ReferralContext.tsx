import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useMemo,
  ReactNode
} from 'react';

interface ReferralParams {
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  utm_term?: string;
  utm_content?: string;
  utm_id?: string;
  gclid?: string;
  fbclid?: string;
  msclkid?: string;
  dclid?: string;
  awc?: string;
}

interface ReferralContextType {
  params: ReferralParams;
}

const ReferralContext = createContext<ReferralContextType | undefined>(
  undefined
);

export const ReferralProvider: React.FC<{ children: ReactNode }> = ({
  children
}) => {
  const [params, setParams] = useState<ReferralParams>({});

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const searchParams = new URLSearchParams(window.location.search);
      const extractedParams: ReferralParams = {};

      const potentialParams: (keyof ReferralParams)[] = [
        'utm_source',
        'utm_medium',
        'utm_campaign',
        'utm_term',
        'utm_content',
        'utm_id',
        'gclid',
        'fbclid',
        'msclkid',
        'dclid',
        'awc'
      ];

      potentialParams.forEach(paramKey => {
        const value = searchParams.get(paramKey);
        if (value) {
          extractedParams[paramKey] = value;
        }
      });

      setParams(extractedParams);
    }
  }, []);

  const contextValue = useMemo(() => ({ params }), [params]);

  return (
    <ReferralContext.Provider value={contextValue}>
      {children}
    </ReferralContext.Provider>
  );
};

export const useReferral = (): ReferralContextType => {
  const context = useContext(ReferralContext);
  if (context === undefined) {
    throw new Error('useReferral must be used within a ReferralProvider');
  }
  return context;
};
