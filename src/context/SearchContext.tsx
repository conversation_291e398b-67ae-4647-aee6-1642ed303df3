import {
  FC,
  ReactNode,
  createContext,
  useContext,
  useMemo,
  useState
} from 'react';
import {
  searchPayloadDefaultValue,
  searchResponseDefaultValues
} from '@consts/search';
import {
  ISearchContext,
  ISearchPayload,
  ISearchResponse
} from 'src/types/search';

export const searchContextDefaultValues = {
  searchPayload: searchPayloadDefaultValue,
  setSearchPayload: () => {},
  searchResponse: searchResponseDefaultValues,
  setSearchResponse: () => {},
  loading: false,
  setLoading: () => {}
};

export const SearchContext = createContext<ISearchContext | undefined>(
  undefined
);

interface SearchProviderProps {
  children: ReactNode;
}

export const SearchProvider: FC<SearchProviderProps> = ({
  children
}: SearchProviderProps) => {
  const [loading, setLoading] = useState(false);
  const [searchPayload, setSearchPayload] = useState<ISearchPayload>(
    searchPayloadDefaultValue
  );
  const [searchResponse, setSearchResponse] = useState<ISearchResponse>(
    searchResponseDefaultValues
  );

  const searchContextData = useMemo(
    () => ({
      searchPayload,
      setSearchPayload,
      searchResponse,
      setSearchResponse,
      loading,
      setLoading
    }),
    [searchPayload, searchResponse, loading]
  );

  return (
    <SearchContext.Provider value={searchContextData}>
      {children}
    </SearchContext.Provider>
  );
};

export const useSearch = () => {
  const context = useContext(SearchContext);

  if (!context) {
    throw new Error('useSearch must be used within a SearchProvider');
  }

  return context;
};
