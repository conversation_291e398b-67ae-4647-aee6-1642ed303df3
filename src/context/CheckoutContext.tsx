/* eslint-disable react/no-unused-prop-types */
/* eslint-disable no-shadow */
/* eslint-disable no-unused-vars */
import React, {
  FC,
  createContext,
  useState,
  useContext,
  ReactNode,
  useMemo,
  useEffect
} from 'react';
import { ICartResponse } from 'src/types/cart';

interface ICheckoutContext {
  cart: ICartResponse | null;
  isTripcashApplied: boolean;
  setIsTripcashApplied: (value: boolean) => void;
  isExpired: boolean;
  setIsExpired: (value: boolean) => void;
}

type CheckoutStep = {
  step: number;
  completed: boolean;
  component: ReactNode;
};

export const CheckoutContext = createContext<ICheckoutContext>({
  cart: null,
  isTripcashApplied: false,
  setIsTripcashApplied: () => {},
  isExpired: false,
  setIsExpired: () => {}
});

interface CheckoutProviderProps {
  cart: ICartResponse | null;
  children: ReactNode;
}

export const CheckoutProvider: FC<CheckoutProviderProps> = ({
  cart,
  children
}) => {
  const [steps, setSteps] = useState<CheckoutStep[]>([
    {
      step: 0,
      completed: false,
      component: <p>step 1</p>
    },
    {
      step: 1,
      completed: false,
      component: <p>step 2</p>
    },
    {
      step: 2,
      completed: false,
      component: <p>step 3</p>
    },
    {
      step: 3,
      completed: false,
      component: <p>step 4</p>
    }
  ]);
  const [selectedStep, setSelectedStep] = useState<number>(0);
  const [isExpired, setIsExpired] = useState<boolean>(false);
  const [isTripcashApplied, setIsTripcashApplied] = useState<boolean>(
    !!cart!.priceWithTripcash
  );

  const value = useMemo(
    () => ({
      cart,
      steps,
      selectedStep,
      isExpired,
      setIsExpired,
      isTripcashApplied,
      setIsTripcashApplied
    }),
    [cart, isExpired, isTripcashApplied, selectedStep, steps]
  );

  return (
    <CheckoutContext.Provider value={value}>
      {children}
    </CheckoutContext.Provider>
  );
};

export const useCheckout = () => {
  const context = useContext(CheckoutContext);

  if (context === undefined) {
    throw new Error('useCheckout must be used within a CheckoutProvider');
  }

  return context;
};
