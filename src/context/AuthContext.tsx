/* eslint-disable no-unused-vars */
import {
  ReactNode,
  createContext,
  useCallback,
  useEffect,
  useMemo,
  useState
} from 'react';
import { ICustomer, IRegisterPayload } from 'src/types/customer';
import { deleteCookie, setCookie } from 'cookies-next';
import { getCustomer, register } from 'src/server/customer';
import { IChangePasswordPayload, ILoginPayload } from 'src/types/auth';
import { login, changePasswordRequest } from 'src/server/auth';
import exportDataLayer from 'src/events/ga';
import { i18n, useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';

const defaultUserObject: ICustomer = {
  id: '',
  name: '',
  surname: '',
  documentNumber: '',
  documentType: 'CPF',
  email: '',
  birthday: '',
  gender: '',
  phone: {
    ddi: '',
    areaCode: '',
    number: ''
  },
  address: {
    street: '',
    number: '',
    complement: '',
    neighborhood: '',
    postalCode: '',
    city: '',
    uf: '',
    country: ''
  },
  verified: false,
  tripcashBalances: ['R$ 0,00']
};

type AuthContextType = {
  user: ICustomer;
  updateUser?: (payload: ICustomer) => void;
  isAuthenticated: boolean;
  signIn?: (payload: ILoginPayload) => Promise<void>;
  signUp?: (payload: IRegisterPayload) => Promise<void>;
  changePassword?: Function;
  logout?: Function;
};

export const AuthContext = createContext<AuthContextType>({
  user: defaultUserObject,
  isAuthenticated: false
});

export const AuthProvider = ({
  currentUser,
  children
}: {
  currentUser: ICustomer | null;
  children: ReactNode;
}) => {
  const { t } = useTranslation('common');
  const router = useRouter();
  const [user, setUser] = useState<ICustomer>(currentUser || defaultUserObject);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(!!user.id);

  useEffect(() => {
    if (user.id) {
      exportDataLayer('customerId', { customerId: user.id });
    }
  }, [user]);

  const logout = useCallback(async () => {
    if (
      window.location.pathname === t('routes.myProfile') ||
      window.location.pathname === t('routes.myReservations') ||
      window.location.pathname === t('routes.myTripcash')
    ) {
      await router.push('/', '', { locale: i18n!.language });
    }

    setUser(defaultUserObject);
    setIsAuthenticated(false);
    deleteCookie('token');
  }, [t, router]);

  const getUser = useCallback(async () => {
    try {
      const { data } = await getCustomer();

      setIsAuthenticated(true);
      setUser(data);
    } catch (err) {
      logout();
    }
  }, [logout]);

  const updateUser = (data: ICustomer) => {
    setUser(data);
    setIsAuthenticated(true);
  };

  const signIn = useCallback(
    async (payload: ILoginPayload) => {
      const { data } = await login(payload);
      setCookie('token', data, {
        maxAge: 3600
      });

      setIsAuthenticated(true);
      getUser();
    },
    [getUser]
  );

  const signUp = useCallback(async (payload: IRegisterPayload) => {
    const { data } = await register(payload);
    setCookie('token', data.token);
    setUser(data);
  }, []);

  const changePassword = useCallback(
    async (payload: IChangePasswordPayload) => {
      const { data } = await changePasswordRequest(payload);
      return data;
    },
    []
  );

  const authContextData = useMemo(
    () => ({
      user,
      updateUser,
      isAuthenticated,
      signIn,
      signUp,
      changePassword,
      logout
    }),
    [user, isAuthenticated, signIn, signUp, changePassword, logout]
  );

  return (
    <AuthContext.Provider value={authContextData}>
      {children}
    </AuthContext.Provider>
  );
};
