import {
  ReactNode,
  createContext,
  useCallback,
  useMemo,
  useState
} from 'react';

type OffersContextType = {
  selectedOffers: string[];
  setSelectedOffer?: Function;
};

export const OffersContext = createContext<OffersContextType>({
  selectedOffers: []
});

export const OffersProvider = ({ children }: { children: ReactNode }) => {
  const [selectedOffers, setOffers] = useState<string[]>([]);

  const setSelectedOffer = useCallback(
    (index: number, token: string) => {
      const updatedArray = [...selectedOffers];
      updatedArray[index] = token;
      setOffers(updatedArray);
    },
    [selectedOffers]
  );

  const offersContextData = useMemo(
    () => ({ selectedOffers, setSelectedOffer }),
    [selectedOffers, setSelectedOffer]
  );

  return (
    <OffersContext.Provider value={offersContextData}>
      {children}
    </OffersContext.Provider>
  );
};
