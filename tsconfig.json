{"compilerOptions": {"target": "es5", "lib": ["ES6", "ES2016", "ES2017", "dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "paths": {"@components/*": ["./src/components/*"], "@styles/*": ["./src/styles/*"], "@services/*": ["./src/services/*"], "@consts/*": ["./src/consts/*"], "@utils/*": ["./src/utils/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx"], "exclude": ["node_modules"]}