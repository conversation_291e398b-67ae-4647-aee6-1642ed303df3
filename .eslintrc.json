{"env": {"browser": true, "es6": true}, "extends": ["next/core-web-vitals", "airbnb", "plugin:prettier/recommended"], "parserOptions": {"ecmaVersion": 2023, "sourceType": "module", "ecmaFeatures": {"jsx": true, "tsx": true}}, "rules": {"react/no-danger": "off", "react/jsx-props-no-spreading": "off", "@next/next/no-img-element": "off", "jsx-a11y/click-events-have-key-events": "off", "jsx-a11y/no-static-element-interactions": "off", "no-nested-ternary": "off", "no-plusplus": "off", "react/jsx-filename-extension": [1, {"extensions": [".jsx", ".tsx", "ts"]}], "import/extensions": ["error", "ignorePackages", {"ts": "never", "tsx": "never"}], "react/react-in-jsx-scope": "off", "react/require-default-props": "off", "import/prefer-default-export": "off", "react/function-component-definition": "off", "react/button-has-type": "off"}, "plugins": ["import", "prettier", "@typescript-eslint"], "parser": "@typescript-eslint/parser", "settings": {"import/extensions": [".ts", ".tsx", ".js", ".jsx"], "import/resolver": {"typescript": {}}}}